#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import matplotlib
matplotlib.use('Agg')

import numpy as np
import matplotlib.pyplot as plt
import os
import warnings
warnings.filterwarnings('ignore')

plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False

# 小清新配色方案
COLORS = ['#FF9AA2', '#FFB7B2', '#FFDAC1', '#E2F0CB', '#B5EAD7', '#C7CEEA']
COLORS2 = ['#A8E6CF', '#DCEDC1', '#FFD3A5', '#FFA8A8', '#B5B5EA', '#F8BBD9']
COLORS3 = ['#87CEEB', '#98FB98', '#F0E68C', '#FFA07A', '#DDA0DD', '#F5DEB3']

def create_output_dir():
    output_dir = "output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/"
    os.makedirs(output_dir, exist_ok=True)
    return output_dir

def generate_extended_charts():
    output_dir = create_output_dir()
    chart_count = 0
    
    # 图表6: 发展现状对比
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))
    
    districts = ['Haidian', 'Changping', 'Daxing', 'Tongzhou', 'Fangshan', 'Miyun']
    base_counts = [8, 6, 5, 3, 4, 3]
    bars = ax1.bar(districts, base_counts, color=COLORS, alpha=0.8)
    ax1.set_title('Digital Agriculture Demo Bases by District')
    ax1.set_ylabel('Number of Bases')
    ax1.tick_params(axis='x', rotation=45)
    
    for bar, count in zip(bars, base_counts):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                f'{count}', ha='center', va='bottom', fontweight='bold')
    
    aspects = ['Infrastructure', 'Technology', 'Data Mgmt', 'Service Innovation', 'Talent']
    beijing_scores = [4.2, 3.8, 3.5, 4.0, 3.6]
    tongzhou_scores = [3.5, 3.2, 2.8, 3.3, 3.0]
    
    x = np.arange(len(aspects))
    width = 0.35
    ax2.bar(x - width/2, beijing_scores, width, label='Beijing Avg', color=COLORS2[0], alpha=0.8)
    ax2.bar(x + width/2, tongzhou_scores, width, label='Tongzhou', color=COLORS2[1], alpha=0.8)
    ax2.set_title('Digital Level Comparison')
    ax2.set_ylabel('Score (Max 5)')
    ax2.set_xticks(x)
    ax2.set_xticklabels(aspects, rotation=45)
    ax2.legend()
    ax2.grid(True, alpha=0.3, axis='y')
    
    plt.tight_layout()
    fig.savefig(os.path.join(output_dir, 'chart_2_发展现状_7.png'), dpi=300, bbox_inches='tight')
    plt.close(fig)
    chart_count += 1
    print("✓ Chart 6: Development Status")
    
    # 图表7: 痛点分析
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))
    
    problems = ['Small Scale', 'Low Efficiency', 'Weak Brand', 'Resource Underuse', 'Low Tech']
    severity = [100, 85, 75, 60, 45]
    
    y_pos = np.arange(len(problems))
    for i, (problem, sev) in enumerate(zip(problems, severity)):
        width = sev / 100 * 0.8
        left = (1 - width) / 2
        ax1.barh(i, width, left=left, height=0.6, color=COLORS[i], alpha=0.8)
        ax1.text(0.5, i, f'{problem}\n({sev}%)', ha='center', va='center', fontweight='bold')
    
    ax1.set_xlim(0, 1)
    ax1.set_ylim(-0.5, len(problems) - 0.5)
    ax1.set_yticks([])
    ax1.set_xticks([])
    ax1.set_title('Agricultural Development Pain Points')
    
    solutions = ['Digital Transform', 'Brand Building', 'Scale Operation', 'Tech Upgrade', 'Resource Integration']
    priority = [95, 80, 70, 65, 60]
    urgency = [90, 75, 60, 70, 55]
    
    scatter = ax2.scatter(priority, urgency, s=[p*3 for p in priority],
                         c=COLORS2[:5], alpha=0.7, edgecolors='white', linewidth=2)
    
    for i, solution in enumerate(solutions):
        ax2.annotate(solution, (priority[i], urgency[i]), 
                    xytext=(5, 5), textcoords='offset points', fontsize=9)
    
    ax2.set_xlabel('Importance')
    ax2.set_ylabel('Urgency')
    ax2.set_title('Solution Priority Matrix')
    ax2.grid(True, alpha=0.3)
    ax2.axhline(y=75, color='red', linestyle='--', alpha=0.5)
    ax2.axvline(x=75, color='red', linestyle='--', alpha=0.5)
    
    plt.tight_layout()
    fig.savefig(os.path.join(output_dir, 'chart_2_痛点分析_9.png'), dpi=300, bbox_inches='tight')
    plt.close(fig)
    chart_count += 1
    print("✓ Chart 7: Pain Point Analysis")
    
    # 图表8: 数字化水平仪表盘
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 8))
    
    # 仪表盘
    score = 3.2
    theta = np.linspace(0, np.pi, 100)
    r = np.ones_like(theta)
    ax1.plot(theta, r, 'k-', linewidth=8, alpha=0.3)
    
    score_theta = np.linspace(0, np.pi * (score/5), int(100 * score/5))
    score_r = np.ones_like(score_theta)
    ax1.plot(score_theta, score_r, color=COLORS[0], linewidth=8)
    
    pointer_angle = np.pi * (score/5)
    ax1.arrow(0, 0, 0.8*np.cos(pointer_angle), 0.8*np.sin(pointer_angle),
             head_width=0.1, head_length=0.1, fc='red', ec='red')
    
    ax1.set_xlim(-1.2, 1.2)
    ax1.set_ylim(-0.2, 1.2)
    ax1.set_aspect('equal')
    ax1.axis('off')
    ax1.text(0, -0.1, f'Digital Level\n{score}/5.0', ha='center', va='top', fontweight='bold')
    
    # 各维度评分
    dimensions = ['Infrastructure', 'Tech Application', 'Data Usage', 'Digital Mgmt']
    scores = [3.5, 3.0, 2.8, 3.2]
    bars = ax2.bar(dimensions, scores, color=COLORS2[:4], alpha=0.8)
    ax2.set_title('Digital Level by Dimension')
    ax2.set_ylabel('Score (Max 5)')
    ax2.tick_params(axis='x', rotation=45)
    ax2.set_ylim(0, 5)
    ax2.grid(True, alpha=0.3, axis='y')
    
    for bar, score in zip(bars, scores):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                f'{score}', ha='center', va='bottom', fontweight='bold')
    
    # 应用普及率
    applications = ['Mobile Pay', 'Online Booking', 'Smart Guide', 'Data Analytics', 'VR Experience', 'IoT']
    adoption_rate = [75, 45, 25, 20, 10, 15]
    ax3.barh(applications, adoption_rate, color=COLORS3, alpha=0.8)
    ax3.set_xlabel('Adoption Rate (%)')
    ax3.set_title('Digital Application Penetration')
    ax3.grid(True, alpha=0.3, axis='x')
    
    # 发展趋势
    years = list(range(2020, 2026))
    digital_index = [2.1, 2.4, 2.8, 3.2, 3.8, 4.2]
    ax4.plot(years, digital_index, marker='o', linewidth=3, color=COLORS[2], markersize=8)
    ax4.fill_between(years, digital_index, alpha=0.3, color=COLORS[2])
    ax4.set_xlabel('Year')
    ax4.set_ylabel('Digital Index')
    ax4.set_title('Digital Development Trend')
    ax4.grid(True, alpha=0.3)
    
    for year, index in zip(years, digital_index):
        ax4.annotate(f'{index}', (year, index), textcoords="offset points",
                    xytext=(0,10), ha='center', fontsize=9, fontweight='bold')
    
    plt.tight_layout()
    fig.savefig(os.path.join(output_dir, 'chart_2_数字化水平_10.png'), dpi=300, bbox_inches='tight')
    plt.close(fig)
    chart_count += 1
    print("✓ Chart 8: Digital Level Dashboard")
    
    # 图表9: 地理优势分析
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))
    
    # 交通便利性雷达图
    transport_aspects = ['Highway', 'Public Bus', 'Metro', 'Shared Transport', 'Parking', 'Accessibility']
    transport_scores = [4.5, 3.8, 3.2, 4.0, 3.5, 4.2]
    
    angles = np.linspace(0, 2*np.pi, len(transport_aspects), endpoint=False).tolist()
    transport_scores += transport_scores[:1]
    angles += angles[:1]
    
    ax1.plot(angles, transport_scores, 'o-', linewidth=3, color=COLORS[1], markersize=8)
    ax1.fill(angles, transport_scores, alpha=0.25, color=COLORS[1])
    ax1.set_xticks(angles[:-1])
    ax1.set_xticklabels(transport_aspects)
    ax1.set_ylim(0, 5)
    ax1.set_title('Transportation Convenience Assessment')
    ax1.grid(True)
    
    # 距离优势
    destinations = ['City Center', 'Airport', 'Railway Station', 'University Area', 'Business District']
    distances = [25, 45, 30, 20, 35]  # km
    travel_times = [40, 60, 45, 30, 50]  # minutes
    
    x = np.arange(len(destinations))
    width = 0.35
    ax2.bar(x - width/2, distances, width, label='Distance (km)', color=COLORS2[2], alpha=0.8)
    ax2_twin = ax2.twinx()
    ax2_twin.bar(x + width/2, travel_times, width, label='Travel Time (min)', color=COLORS2[3], alpha=0.8)
    
    ax2.set_xlabel('Destinations')
    ax2.set_ylabel('Distance (km)')
    ax2_twin.set_ylabel('Travel Time (min)')
    ax2.set_title('Distance & Travel Time to Key Areas')
    ax2.set_xticks(x)
    ax2.set_xticklabels(destinations, rotation=45)
    ax2.legend(loc='upper left')
    ax2_twin.legend(loc='upper right')
    
    plt.tight_layout()
    fig.savefig(os.path.join(output_dir, 'chart_3_地理优势_11.png'), dpi=300, bbox_inches='tight')
    plt.close(fig)
    chart_count += 1
    print("✓ Chart 9: Geographic Advantages")
    
    return chart_count

if __name__ == "__main__":
    try:
        count = generate_extended_charts()
        print(f"\n✅ Successfully generated {count} additional charts!")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
