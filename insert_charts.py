# insert_charts.py - 专业图表插入器
import json
import os
import shutil

def load_instructions():
    """加载插入指令"""
    with open('insert_instructions.json', 'r', encoding='utf-8') as f:
        return json.load(f)

def insert_charts_to_document():
    """将图表插入到文档中"""
    print("📝 开始插入图表到文档...")
    
    # 加载指令
    instructions = load_instructions()
    source_file = instructions['source_file']
    target_file = instructions['target_file']
    insertions = instructions['insertions']
    
    # 读取源文档
    with open(source_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    print(f"📖 源文档行数: {len(lines)}")
    
    # 创建输出目录
    output_dir = os.path.dirname(target_file)
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建charts目录
    charts_dir = os.path.join(output_dir, 'charts')
    os.makedirs(charts_dir, exist_ok=True)
    
    # 复制图表文件
    if os.path.exists('charts'):
        for chart_file in os.listdir('charts'):
            if chart_file.endswith('.png'):
                src_path = os.path.join('charts', chart_file)
                dst_path = os.path.join(charts_dir, chart_file)
                shutil.copy2(src_path, dst_path)
                print(f"📊 复制图表: {chart_file}")
    
    # 按行号排序插入指令（从大到小，避免行号偏移）
    insertions.sort(key=lambda x: x['after_line'], reverse=True)
    
    # 执行插入
    inserted_count = 0
    for insertion in insertions:
        after_line = insertion['after_line']
        content = insertion['content']
        chart_id = insertion['chart_id']
        
        # 检查行号是否有效
        if after_line < len(lines):
            # 在指定行后插入内容
            lines.insert(after_line, content)
            inserted_count += 1
            print(f"✅ 插入图表 {chart_id} 在第 {after_line} 行后")
        else:
            print(f"⚠️ 跳过图表 {chart_id}：行号 {after_line} 超出文档范围")
    
    # 写入目标文档
    with open(target_file, 'w', encoding='utf-8') as f:
        f.writelines(lines)
    
    print(f"🎉 图表插入完成！")
    print(f"📊 成功插入 {inserted_count} 张图表")
    print(f"📄 增强文档保存至: {target_file}")
    print(f"📈 最终文档行数: {len(lines)}")

def main():
    """主函数"""
    insert_charts_to_document()

if __name__ == "__main__":
    main()
