#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import matplotlib
matplotlib.use('Agg')

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os
from matplotlib import font_manager
import warnings
warnings.filterwarnings('ignore')

# 查找阿里巴巴字体
font_list = font_manager.findSystemFonts(fontpaths=None, fontext='ttf')
alibaba_fonts = [f for f in font_list if 'Alibaba' in f]

# 创建字体对象
alibaba_font = None
if alibaba_fonts:
    try:
        alibaba_font = font_manager.FontProperties(fname=alibaba_fonts[0])
        print(f"成功创建字体对象，使用字体: {os.path.basename(alibaba_fonts[0])}")
    except Exception as e:
        print(f"创建字体对象失败: {e}")

# 手动添加阿里巴巴字体
for font_path in alibaba_fonts:
    try:
        font_manager.fontManager.addfont(font_path)
    except:
        pass

# 设置字体
plt.rcParams['font.sans-serif'] = ['AlibabaPuHuiTi-3-55-Regular', 'AlibabaPuHuiTi-3-65-Medium', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 小清新亮色配色方案
COLORS = ['#FF9AA2', '#FFB7B2', '#FFDAC1', '#E2F0CB', '#B5EAD7', '#C7CEEA']
COLORS2 = ['#A8E6CF', '#DCEDC1', '#FFD3A5', '#FFA8A8', '#B5B5EA', '#F8BBD9']
COLORS3 = ['#87CEEB', '#98FB98', '#F0E68C', '#FFA07A', '#DDA0DD', '#F5DEB3']

def create_output_dir():
    output_dir = "output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/"
    os.makedirs(output_dir, exist_ok=True)
    return output_dir

def set_chinese_labels(ax, title, xlabel='', ylabel=''):
    """设置中文标签"""
    if alibaba_font:
        ax.set_title(title, fontsize=14, fontproperties=alibaba_font)
        if xlabel:
            ax.set_xlabel(xlabel, fontproperties=alibaba_font)
        if ylabel:
            ax.set_ylabel(ylabel, fontproperties=alibaba_font)
        for label in ax.get_xticklabels() + ax.get_yticklabels():
            label.set_fontproperties(alibaba_font)
    else:
        ax.set_title(title, fontsize=14)
        if xlabel:
            ax.set_xlabel(xlabel)
        if ylabel:
            ax.set_ylabel(ylabel)

def create_chart_by_type(chart_type, title, data):
    """根据类型创建图表"""
    
    if chart_type == "柱状图":
        fig, ax = plt.subplots(figsize=(10, 6))
        categories = data.get('categories', ['类别A', '类别B', '类别C', '类别D', '类别E'])
        values = data.get('values', [4.2, 3.8, 4.5, 3.9, 4.1])
        
        bars = ax.bar(categories, values, color=COLORS[:len(categories)], alpha=0.8, 
                     edgecolor='white', linewidth=2)
        set_chinese_labels(ax, title, ylabel='数值')
        ax.grid(True, alpha=0.3, axis='y')
        
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + height*0.02,
                    f'{value}', ha='center', va='bottom', fontweight='bold')
    
    elif chart_type == "饼图":
        fig, ax = plt.subplots(figsize=(8, 8))
        labels = data.get('labels', ['类别A', '类别B', '类别C', '类别D'])
        sizes = data.get('sizes', [30, 25, 25, 20])
        
        wedges, texts, autotexts = ax.pie(sizes, labels=labels, colors=COLORS[:len(labels)],
                                         autopct='%1.1f%%', startangle=90, textprops={'fontsize': 10})
        
        if alibaba_font:
            for text in texts:
                text.set_fontproperties(alibaba_font)
            for autotext in autotexts:
                autotext.set_fontproperties(alibaba_font)
        
        set_chinese_labels(ax, title)
    
    elif chart_type == "线图":
        fig, ax = plt.subplots(figsize=(10, 6))
        x_data = data.get('x', list(range(2020, 2026)))
        y_data = data.get('y', [10, 15, 22, 28, 35, 42])
        
        ax.plot(x_data, y_data, marker='o', linewidth=3, color=COLORS[0], markersize=8)
        ax.fill_between(x_data, y_data, alpha=0.3, color=COLORS[0])
        set_chinese_labels(ax, title, xlabel='年份', ylabel='数值')
        ax.grid(True, alpha=0.3)
        
        for x, y in zip(x_data, y_data):
            ax.annotate(f'{y}', (x, y), textcoords="offset points",
                       xytext=(0,10), ha='center', fontsize=9)
    
    elif chart_type == "雷达图":
        fig, ax = plt.subplots(figsize=(8, 8), subplot_kw=dict(projection='polar'))
        categories = data.get('categories', ['维度A', '维度B', '维度C', '维度D', '维度E'])
        values = data.get('values', [4.2, 3.8, 4.5, 3.9, 4.1])
        
        angles = np.linspace(0, 2*np.pi, len(categories), endpoint=False).tolist()
        values += values[:1]
        angles += angles[:1]
        
        ax.plot(angles, values, 'o-', linewidth=3, color=COLORS[0], markersize=8)
        ax.fill(angles, values, alpha=0.25, color=COLORS[0])
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(categories)
        if alibaba_font:
            for label in ax.get_xticklabels():
                label.set_fontproperties(alibaba_font)
        ax.set_ylim(0, 5)
        if alibaba_font:
            ax.set_title(title, fontsize=14, pad=30, fontproperties=alibaba_font)
        else:
            ax.set_title(title, fontsize=14, pad=30)
        ax.grid(True)
    
    elif chart_type == "散点图":
        fig, ax = plt.subplots(figsize=(10, 6))
        x_data = data.get('x', [1, 2, 3, 4, 5])
        y_data = data.get('y', [2, 5, 3, 8, 7])
        sizes = data.get('sizes', [100, 200, 150, 300, 250])
        
        scatter = ax.scatter(x_data, y_data, s=sizes, c=COLORS[:len(x_data)], 
                           alpha=0.7, edgecolors='white', linewidth=2)
        set_chinese_labels(ax, title, xlabel='X轴', ylabel='Y轴')
        ax.grid(True, alpha=0.3)
    
    elif chart_type == "组合图":
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))
        
        # 左侧柱状图
        categories1 = data.get('categories1', ['项目A', '项目B', '项目C'])
        values1 = data.get('values1', [85, 78, 92])
        bars = ax1.bar(categories1, values1, color=COLORS[:3], alpha=0.8)
        set_chinese_labels(ax1, data.get('title1', '子图1'), ylabel='数值')
        ax1.grid(True, alpha=0.3, axis='y')
        
        # 右侧饼图
        labels2 = data.get('labels2', ['类型A', '类型B', '类型C', '类型D'])
        sizes2 = data.get('sizes2', [40, 30, 20, 10])
        wedges, texts, autotexts = ax2.pie(sizes2, labels=labels2, colors=COLORS2[:4],
                                          autopct='%1.1f%%', startangle=90)
        if alibaba_font:
            for text in texts:
                text.set_fontproperties(alibaba_font)
        set_chinese_labels(ax2, data.get('title2', '子图2'))
        
        if alibaba_font:
            fig.suptitle(title, fontsize=16, fontproperties=alibaba_font)
        else:
            fig.suptitle(title, fontsize=16)
    
    else:
        # 默认柱状图
        fig, ax = plt.subplots(figsize=(10, 6))
        categories = ['指标A', '指标B', '指标C', '指标D']
        values = [4.2, 3.8, 4.5, 3.9]
        bars = ax.bar(categories, values, color=COLORS[:4], alpha=0.8)
        set_chinese_labels(ax, title, ylabel='评分')
        ax.grid(True, alpha=0.3, axis='y')
    
    plt.tight_layout()
    return fig

def generate_complete_charts():
    """生成完整的中文图表集合"""
    output_dir = create_output_dir()
    chart_count = 0
    
    # 定义35个图表的配置
    charts_config = [
        # 第一章图表 (5个)
        ("柱状图", "宋庄镇基本情况综合分析", "chart_1_基本情况_1.png", 
         {'categories': ['总面积(km²)', '人口(万)', '耕地(万亩)', '艺术家(千人)', '游客(万人次)'], 
          'values': [40, 5, 1.5, 5, 120]}),
        ("饼图", "城区家庭周末休闲需求分布", "chart_1_需求分布_2.png",
         {'labels': ['亲子农事体验', '文化创意活动', '健康饮食体验', '自然生态体验', '教育研学', '其他'],
          'sizes': [28, 25, 20, 15, 8, 4]}),
        ("线图", "数字农业市场规模增长趋势", "chart_1_市场趋势_3.png",
         {'x': list(range(2018, 2026)), 'y': [1200, 1450, 1750, 2100, 2500, 3000, 3600, 4300]}),
        ("柱状图", "项目实施时间安排", "chart_1_目标时间轴_4.png",
         {'categories': ['规划阶段', '建设阶段', '试运营', '正式运营', '优化提升'],
          'values': [3, 6, 3, 12, 6]}),
        ("组合图", "项目投资回报分析", "chart_1_投资回报_5.png",
         {'categories1': ['建设投资', '运营投资', '维护投资'], 'values1': [1800, 580, 200],
          'labels2': ['第一年', '第二年', '第三年', '第四年'], 'sizes2': [10, 25, 35, 30],
          'title1': '投资构成', 'title2': '收益分布'}),
        
        # 第二章图表 (5个)
        ("雷达图", "国家政策支持力度评估", "chart_2_政策支持_6.png",
         {'categories': ['数字乡村战略', '乡村振兴战略', '文旅融合政策', '数字经济政策', '农业现代化'],
          'values': [4.8, 4.6, 4.5, 4.7, 4.2]}),
        ("组合图", "北京市数字农旅发展现状", "chart_2_发展现状_7.png",
         {'categories1': ['海淀区', '昌平区', '大兴区', '通州区', '房山区'], 'values1': [8, 6, 5, 3, 4],
          'labels2': ['基础设施', '技术应用', '数据管理', '服务创新'], 'sizes2': [30, 25, 25, 20],
          'title1': '示范基地数量', 'title2': '发展水平分布'}),
        ("散点图", "通州区旅游目的地分布", "chart_2_目的地分布_8.png",
         {'x': [2, 4, 3, 1, 5], 'y': [4, 3, 2, 3, 1], 'sizes': [300, 240, 160, 400, 120]}),
        ("组合图", "宋庄镇农业发展痛点分析", "chart_2_痛点分析_9.png",
         {'categories1': ['规模化程度', '经济效益', '品牌影响', '资源利用', '技术水平'], 'values1': [2.5, 2.8, 2.3, 2.6, 2.4],
          'labels2': ['技术升级', '品牌建设', '规模扩大', '资源整合'], 'sizes2': [35, 25, 25, 15],
          'title1': '痛点评估', 'title2': '解决方案'}),
        ("雷达图", "宋庄镇数字化水平评估", "chart_2_数字化水平_10.png",
         {'categories': ['基础设施', '技术应用', '数据管理', '数字服务', '人才队伍'],
          'values': [3.5, 3.0, 2.8, 3.2, 2.9]}),
        
        # 第三章图表 (25个)
        ("组合图", "宋庄镇地理优势分析", "chart_3_地理优势_11.png",
         {'categories1': ['到市中心', '到机场', '到火车站', '到大学城'], 'values1': [25, 45, 30, 20],
          'labels2': ['高速公路', '公共交通', '地铁规划', '共享出行'], 'sizes2': [30, 25, 20, 25],
          'title1': '距离(公里)', 'title2': '交通方式'}),
        ("柱状图", "气候条件适宜性评估", "chart_3_气候条件_12.png",
         {'categories': ['农业生产', '旅游观光', '户外活动', '摄影创作', '教育研学'],
          'values': [4.2, 4.5, 4.0, 4.3, 4.1]}),
        ("饼图", "土地资源利用现状", "chart_3_土地利用_13.png",
         {'labels': ['农用地', '建设用地', '生态用地', '水域', '交通用地', '其他'],
          'sizes': [37.5, 25.0, 20.0, 8.5, 6.0, 3.0]}),
        ("线图", "宋庄艺术区发展历程", "chart_3_艺术发展_14.png",
         {'x': [1994, 1998, 2003, 2008, 2012, 2018, 2023], 
          'y': [50, 200, 800, 2000, 3500, 4500, 5000]}),
        ("柱状图", "艺术家资源分布", "chart_3_艺术家分布_15.png",
         {'categories': ['绘画', '雕塑', '装置艺术', '摄影', '数字艺术', '设计'],
          'values': [2000, 800, 600, 700, 500, 400]}),
        ("饼图", "艺术机构类型分析", "chart_3_机构类型_16.png",
         {'labels': ['画廊', '工作室', '美术馆', '艺术中心', '教育机构', '商业机构'],
          'sizes': [30, 40, 5, 8, 7, 10]}),
        ("雷达图", "基础设施水平评估", "chart_3_基础设施_17.png",
         {'categories': ['交通设施', '通信设施', '公用设施', '公共服务', '安全设施'],
          'values': [4.2, 3.8, 4.0, 3.5, 4.1]}),
        ("柱状图", "数字基础设施现状", "chart_3_数字基础_18.png",
         {'categories': ['网络覆盖率', '带宽速度', '5G基站', '物联网设备', '智能系统'],
          'values': [85, 78, 45, 32, 28]}),
        ("组合图", "公共服务设施配套", "chart_3_公共服务_19.png",
         {'categories1': ['旅游服务', '医疗卫生', '商业服务', '文化娱乐'], 'values1': [75, 68, 82, 59],
          'labels2': ['完善', '基本满足', '需要改善', '严重不足'], 'sizes2': [25, 45, 25, 5],
          'title1': '服务质量评分', 'title2': '设施状况'}),
        ("饼图", "人力资源结构分析", "chart_3_人力结构_20.png",
         {'labels': ['青年(20-35)', '中年(36-50)', '中老年(51-65)', '老年(65+)'],
          'sizes': [35, 40, 20, 5]}),
        ("雷达图", "数字素养水平评估", "chart_3_数字素养_21.png",
         {'categories': ['基础技能', '网络应用', '移动应用', '数字支付', '在线学习'],
          'values': [3.2, 3.8, 4.1, 4.5, 2.9]}),
        ("柱状图", "人才需求缺口分析", "chart_3_人才缺口_22.png",
         {'categories': ['技术专家', '营销人员', '管理人员', '运营人员', '创意人员'],
          'values': [15, 8, 5, 12, 10]}),
        ("线图", "城区居民休闲需求趋势", "chart_3_休闲需求_23.png",
         {'x': list(range(2020, 2026)), 'y': [68, 72, 78, 85, 91, 96]}),
        ("饼图", "目标客户群体画像", "chart_3_客户画像_24.png",
         {'labels': ['城区家庭', '青年群体', '中年群体', '教育机构'],
          'sizes': [45, 25, 20, 10]}),
        ("散点图", "市场竞争格局分析", "chart_3_竞争格局_25.png",
         {'x': [75, 68, 82, 59, 72], 'y': [4.2, 4.0, 3.5, 3.8, 4.5], 'sizes': [200, 180, 120, 80, 150]}),
        ("雷达图", "政策环境匹配度评估", "chart_3_政策匹配_26.png",
         {'categories': ['国家政策', '市级政策', '区级政策', '镇级政策', '行业政策'],
          'values': [4.8, 4.3, 4.1, 3.8, 4.5]}),
        ("柱状图", "政策受益程度分析", "chart_3_政策受益_27.png",
         {'categories': ['数字乡村', '乡村振兴', '文旅融合', '数字经济', '农业支持'],
          'values': [4.5, 4.3, 4.2, 4.4, 3.9]}),
        ("雷达图", "技术成熟度评估", "chart_3_技术成熟_28.png",
         {'categories': ['物联网', '大数据', '人工智能', 'VR/AR', '区块链', '云计算'],
          'values': [4.2, 3.8, 3.5, 3.2, 2.8, 4.5]}),
        ("柱状图", "技术实施难度分析", "chart_3_技术难度_29.png",
         {'categories': ['物联网', '大数据', '人工智能', 'VR/AR', '区块链'],
          'values': [2.5, 3.5, 4.0, 3.0, 4.5]}),
        ("散点图", "技术风险评估", "chart_3_技术风险_30.png",
         {'x': [0.3, 0.2, 0.4, 0.25, 0.35], 'y': [0.7, 0.9, 0.6, 0.8, 0.5], 'sizes': [150, 180, 120, 160, 140]}),
        ("饼图", "投资成本结构", "chart_3_投资成本_31.png",
         {'labels': ['基础设施', '技术系统', '营销推广', '人员培训', '其他'],
          'sizes': [47, 31, 12, 8, 2]}),
        ("线图", "项目收益预测", "chart_3_收益预测_32.png",
         {'x': list(range(2025, 2030)), 'y': [200, 450, 680, 845, 920]}),
        ("柱状图", "财务指标评价", "chart_3_财务指标_33.png",
         {'categories': ['净现值', '内部收益率', '投资回收期', '投资回报率'],
          'values': [1250, 28.5, 3.2, 25.0]}),
        ("柱状图", "敏感性分析", "chart_3_敏感性_34.png",
         {'categories': ['收入水平', '运营成本', '投资成本', '市场需求', '政策支持'],
          'values': [18, 15, 10, 12, 8]}),
        ("雷达图", "社会效益评估", "chart_3_社会效益_35.png",
         {'categories': ['就业促进', '收入增长', '文化传承', '教育发展', '社区建设'],
          'values': [4.3, 4.1, 4.5, 3.9, 4.2]}),
        ("散点图", "社会风险分析", "chart_3_社会风险_36.png",
         {'x': [0.4, 0.2, 0.3, 0.25, 0.35], 'y': [0.6, 0.8, 0.5, 0.9, 0.4], 'sizes': [120, 160, 100, 180, 80]}),
        ("柱状图", "社会接受度调查", "chart_3_社会接受_37.png",
         {'categories': ['当地居民', '农民群体', '艺术家', '政府部门', '投资方'],
          'values': [78, 82, 88, 92, 85]}),
        ("饼图", "环境影响评估", "chart_3_环境影响_38.png",
         {'labels': ['正面影响', '中性影响', '轻微负面', '可控风险'],
          'sizes': [60, 25, 10, 5]}),
        ("雷达图", "环境效益分析", "chart_3_环境效益_39.png",
         {'categories': ['生态保护', '空气质量', '水资源保护', '能源效率', '废物减少'],
          'values': [4.3, 4.1, 4.2, 3.8, 4.0]}),
        ("雷达图", "综合可行性评价", "chart_3_综合评价_40.png",
         {'categories': ['市场可行性', '技术可行性', '经济可行性', '社会可行性', '环境可行性', '政策可行性'],
          'values': [4.1, 4.0, 4.3, 4.3, 4.5, 4.7]}),
        
        # 第四章图表 (2个)
        ("柱状图", "项目实施路线图", "chart_4_实施路线_41.png",
         {'categories': ['2025年', '2026年', '2027年'], 'values': [25, 65, 100]}),
        ("柱状图", "项目发展前景", "chart_4_发展前景_42.png",
         {'categories': ['产业升级', '品牌提升', '示范效应', '经济增长', '社会影响'],
          'values': [85, 78, 82, 88, 80]})
    ]
    
    for chart_type, title, filename, data in charts_config:
        try:
            fig = create_chart_by_type(chart_type, title, data)
            fig.savefig(os.path.join(output_dir, filename), 
                       dpi=300, bbox_inches='tight', facecolor='white')
            plt.close(fig)
            chart_count += 1
            print(f"✓ 图表{chart_count}: {title}")
        except Exception as e:
            print(f"❌ 图表生成失败 {title}: {e}")
    
    return chart_count

if __name__ == "__main__":
    try:
        count = generate_complete_charts()
        print(f"\n✅ 成功生成 {count} 个完整的中文图表!")
        print("📝 所有图表均使用阿里巴巴字体，中文显示正常")
        
        # 统计总数
        output_dir = create_output_dir()
        total_charts = len([f for f in os.listdir(output_dir) if f.endswith('.png')])
        print(f"📊 图表总数: {total_charts}")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()
