#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import os
import json
from matplotlib import font_manager
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['AlibabaPuHuiTi-3-55-Regular', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

# 小清新亮色配色方案
COLORS = {
    'primary': ['#FF9AA2', '#FFB7B2', '#FFDAC1', '#E2F0CB', '#B5EAD7', '#C7CEEA'],
    'secondary': ['#A8E6CF', '#DCEDC1', '#FFD3A5', '#FFA8A8', '#B5B5EA', '#F8BBD9'],
    'accent': ['#87CEEB', '#98FB98', '#F0E68C', '#FFA07A', '#DDA0DD', '#F5DEB3'],
    'gradient': ['#FFE5E5', '#E5F3FF', '#E5FFE5', '#FFF5E5', '#F5E5FF', '#E5FFF5']
}

def setup_font():
    """设置字体"""
    try:
        font_list = font_manager.findSystemFonts(fontpaths=None, fontext='ttf')
        alibaba_fonts = [f for f in font_list if 'Alibaba' in f]
        if alibaba_fonts:
            alibaba_font = font_manager.FontProperties(fname=alibaba_fonts[0])
            return alibaba_font
    except:
        pass
    return None

def create_output_dir(chart_info):
    """创建输出目录"""
    output_dir = "output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/"
    os.makedirs(output_dir, exist_ok=True)
    return output_dir

def save_chart(fig, filename, output_dir):
    """保存图表"""
    filepath = os.path.join(output_dir, filename.replace('.py', '.png'))
    fig.savefig(filepath, dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none', pad_inches=0.1)
    print(f"已生成图表: {filepath}")
    return filepath

def set_chart_style():
    """设置图表样式"""
    sns.set_style("whitegrid", {
        'axes.grid': True,
        'axes.edgecolor': '#E0E0E0',
        'axes.linewidth': 0.8,
        'grid.color': '#F0F0F0',
        'grid.linewidth': 0.5
    })
    plt.rcParams.update({
        'figure.facecolor': 'white',
        'axes.facecolor': 'white',
        'savefig.facecolor': 'white',
        'font.size': 10,
        'axes.titlesize': 14,
        'axes.labelsize': 12,
        'xtick.labelsize': 10,
        'ytick.labelsize': 10,
        'legend.fontsize': 10
    })

def chart_1_基本情况_1():
    """宋庄镇基本情况概览"""
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 8))
    
    # 基本数据
    categories = ['总面积\n(平方公里)', '常住人口\n(万人)', '耕地面积\n(万亩)', '艺术家\n(人)']
    values = [40, 5, 1.5, 5000]
    colors = COLORS['primary'][:4]
    
    # 柱状图
    bars = ax1.bar(categories, values, color=colors, alpha=0.8, edgecolor='white', linewidth=2)
    ax1.set_title('宋庄镇基本情况概览', fontsize=14, pad=20)
    ax1.set_ylabel('数值')
    
    # 添加数值标签
    for bar, value in zip(bars, values):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                f'{value}', ha='center', va='bottom', fontweight='bold')
    
    # 距离城区交通时间
    transport_data = ['驾车', '公交', '地铁(规划)']
    time_data = [40, 60, 30]
    
    ax2.barh(transport_data, time_data, color=COLORS['secondary'][:3], alpha=0.8)
    ax2.set_title('到北京城区交通时间(分钟)', fontsize=14, pad=20)
    ax2.set_xlabel('时间(分钟)')
    
    # 产业结构
    industries = ['农业', '艺术创意', '旅游服务', '其他']
    industry_values = [30, 40, 20, 10]
    
    wedges, texts, autotexts = ax3.pie(industry_values, labels=industries, 
                                      colors=COLORS['accent'][:4], autopct='%1.1f%%',
                                      startangle=90, textprops={'fontsize': 10})
    ax3.set_title('产业结构分布', fontsize=14, pad=20)
    
    # 发展优势雷达图
    advantages = ['区位优势', '资源优势', '政策优势', '文化优势', '交通优势']
    scores = [4.5, 4.2, 4.8, 4.6, 4.0]
    
    angles = np.linspace(0, 2*np.pi, len(advantages), endpoint=False).tolist()
    scores += scores[:1]  # 闭合
    angles += angles[:1]
    
    ax4.plot(angles, scores, 'o-', linewidth=2, color=COLORS['primary'][0])
    ax4.fill(angles, scores, alpha=0.25, color=COLORS['primary'][0])
    ax4.set_xticks(angles[:-1])
    ax4.set_xticklabels(advantages)
    ax4.set_ylim(0, 5)
    ax4.set_title('发展优势评估(满分5分)', fontsize=14, pad=20)
    ax4.grid(True)
    
    plt.tight_layout()
    return fig

def chart_1_需求分布_2():
    """北京城区家庭周末休闲需求分布"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 8))
    
    # 需求类型分布
    needs = ['亲子农事体验', '文化创意活动', '健康饮食体验', '自然生态体验', '教育研学活动', '其他']
    percentages = [28, 25, 20, 15, 8, 4]
    colors = COLORS['primary'] + COLORS['secondary']
    
    wedges, texts, autotexts = ax1.pie(percentages, labels=needs, colors=colors[:6],
                                      autopct='%1.1f%%', startangle=90,
                                      textprops={'fontsize': 10})
    ax1.set_title('城区家庭周末休闲需求分布', fontsize=14, pad=20)
    
    # 需求频次分析
    frequency = ['每周1次', '每月2-3次', '每月1次', '季度1次', '不定期']
    freq_values = [15, 35, 30, 12, 8]
    
    bars = ax2.bar(frequency, freq_values, color=COLORS['accent'][:5], alpha=0.8,
                   edgecolor='white', linewidth=2)
    ax2.set_title('周末郊区休闲频次分布(%)', fontsize=14, pad=20)
    ax2.set_ylabel('占比(%)')
    ax2.tick_params(axis='x', rotation=45)
    
    # 添加数值标签
    for bar, value in zip(bars, freq_values):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{value}%', ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    return fig

def chart_1_市场趋势_3():
    """数字农业市场规模增长趋势"""
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
    
    # 市场规模趋势
    years = list(range(2018, 2026))
    market_size = [1200, 1450, 1750, 2100, 2500, 3000, 3600, 4300]
    growth_rate = [0, 20.8, 20.7, 20.0, 19.0, 20.0, 20.0, 19.4]
    
    # 市场规模
    line1 = ax1.plot(years, market_size, marker='o', linewidth=3, 
                     color=COLORS['primary'][0], markersize=8, label='市场规模')
    ax1.fill_between(years, market_size, alpha=0.3, color=COLORS['primary'][0])
    ax1.set_title('中国数字农业市场规模增长趋势', fontsize=14, pad=20)
    ax1.set_ylabel('市场规模(亿元)', color=COLORS['primary'][0])
    ax1.tick_params(axis='y', labelcolor=COLORS['primary'][0])
    ax1.grid(True, alpha=0.3)
    
    # 增长率
    ax1_twin = ax1.twinx()
    bars = ax1_twin.bar(years[1:], growth_rate[1:], alpha=0.6, 
                        color=COLORS['secondary'][1], width=0.6, label='增长率')
    ax1_twin.set_ylabel('增长率(%)', color=COLORS['secondary'][1])
    ax1_twin.tick_params(axis='y', labelcolor=COLORS['secondary'][1])
    
    # 添加数值标签
    for i, (year, size, rate) in enumerate(zip(years, market_size, growth_rate)):
        ax1.annotate(f'{size}', (year, size), textcoords="offset points", 
                    xytext=(0,10), ha='center', fontsize=9)
        if i > 0:
            ax1_twin.annotate(f'{rate}%', (year, rate), textcoords="offset points",
                             xytext=(0,5), ha='center', fontsize=9)
    
    # 智慧旅游渗透率
    tourism_years = list(range(2020, 2026))
    penetration = [35, 45, 58, 68, 75, 82]
    
    ax2.plot(tourism_years, penetration, marker='s', linewidth=3,
             color=COLORS['accent'][2], markersize=8)
    ax2.fill_between(tourism_years, penetration, alpha=0.3, color=COLORS['accent'][2])
    ax2.set_title('智慧旅游渗透率发展趋势', fontsize=14, pad=20)
    ax2.set_xlabel('年份')
    ax2.set_ylabel('渗透率(%)')
    ax2.grid(True, alpha=0.3)
    
    # 添加数值标签
    for year, rate in zip(tourism_years, penetration):
        ax2.annotate(f'{rate}%', (year, rate), textcoords="offset points",
                    xytext=(0,10), ha='center', fontsize=9, fontweight='bold')
    
    plt.tight_layout()
    return fig

def chart_1_目标时间轴_4():
    """项目目标达成时间轴"""
    fig, ax = plt.subplots(figsize=(12, 8))

    # 甘特图数据
    tasks = ['产业目标', '市场目标', '经济目标', '社会目标', '示范目标']
    start_dates = [0, 3, 6, 9, 12]
    durations = [24, 21, 18, 15, 12]
    colors = COLORS['primary'][:5]

    # 创建甘特图
    for i, (task, start, duration, color) in enumerate(zip(tasks, start_dates, durations, colors)):
        ax.barh(i, duration, left=start, height=0.6, color=color, alpha=0.8,
                edgecolor='white', linewidth=2)

        # 添加任务标签
        ax.text(start + duration/2, i, f'{task}\n({duration}个月)',
                ha='center', va='center', fontweight='bold', fontsize=10)

    ax.set_yticks(range(len(tasks)))
    ax.set_yticklabels(tasks)
    ax.set_xlabel('时间(月)')
    ax.set_title('项目目标达成时间轴', fontsize=14, pad=20)
    ax.grid(True, alpha=0.3, axis='x')
    ax.set_xlim(0, 30)

    # 添加里程碑
    milestones = [6, 12, 18, 24]
    milestone_labels = ['第一阶段', '第二阶段', '第三阶段', '项目完成']

    for milestone, label in zip(milestones, milestone_labels):
        ax.axvline(x=milestone, color='red', linestyle='--', alpha=0.7)
        ax.text(milestone, len(tasks), label, rotation=90, ha='right', va='bottom')

    plt.tight_layout()
    return fig

def chart_1_投资回报_5():
    """项目投资回报分析"""
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 8))

    # 投资构成
    investment_types = ['建设投资', '运营投资', '维护投资']
    investment_amounts = [1800, 580, 200]

    wedges, texts, autotexts = ax1.pie(investment_amounts, labels=investment_types,
                                      colors=COLORS['primary'][:3], autopct='%1.1f%%',
                                      startangle=90)
    ax1.set_title('总投资构成(万元)', fontsize=12)

    # 年度收益预测
    years = list(range(2025, 2030))
    revenues = [200, 450, 680, 845, 920]
    costs = [150, 280, 380, 475, 520]
    profits = [r - c for r, c in zip(revenues, costs)]

    x = np.arange(len(years))
    width = 0.35

    ax2.bar(x - width/2, revenues, width, label='收益', color=COLORS['secondary'][0], alpha=0.8)
    ax2.bar(x + width/2, costs, width, label='成本', color=COLORS['secondary'][1], alpha=0.8)
    ax2.plot(x, profits, marker='o', color='red', linewidth=2, label='净利润')

    ax2.set_xlabel('年份')
    ax2.set_ylabel('金额(万元)')
    ax2.set_title('年度收益预测', fontsize=12)
    ax2.set_xticks(x)
    ax2.set_xticklabels(years)
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    # 投资回收期
    cumulative_profit = np.cumsum(profits)
    initial_investment = sum(investment_amounts)

    ax3.plot(years, cumulative_profit, marker='s', linewidth=3,
             color=COLORS['accent'][0], markersize=8)
    ax3.axhline(y=initial_investment, color='red', linestyle='--',
                label=f'初始投资({initial_investment}万元)')
    ax3.fill_between(years, cumulative_profit, alpha=0.3, color=COLORS['accent'][0])
    ax3.set_xlabel('年份')
    ax3.set_ylabel('累计利润(万元)')
    ax3.set_title('投资回收期分析', fontsize=12)
    ax3.legend()
    ax3.grid(True, alpha=0.3)

    # 财务指标
    indicators = ['NPV\n(万元)', 'IRR\n(%)', '投资回收期\n(年)', 'ROI\n(%)']
    values = [1250, 28.5, 3.2, 25.0]

    bars = ax4.bar(indicators, values, color=COLORS['gradient'][:4], alpha=0.8,
                   edgecolor='white', linewidth=2)
    ax4.set_title('关键财务指标', fontsize=12)
    ax4.set_ylabel('数值')

    # 添加数值标签
    for bar, value in zip(bars, values):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                f'{value}', ha='center', va='bottom', fontweight='bold')

    plt.tight_layout()
    return fig

def chart_2_政策支持_6():
    """国家政策支持力度评估"""
    fig, ax = plt.subplots(figsize=(12, 8), subplot_kw=dict(projection='polar'))

    # 政策维度
    policies = ['数字乡村战略', '乡村振兴战略', '文旅融合政策', '数字经济政策',
                '农业现代化', '创新驱动发展', '绿色发展理念', '城乡融合发展']

    # 支持力度评分(满分5分)
    scores = [4.8, 4.6, 4.5, 4.7, 4.2, 4.3, 4.4, 4.5]

    # 计算角度
    angles = np.linspace(0, 2*np.pi, len(policies), endpoint=False).tolist()
    scores += scores[:1]  # 闭合雷达图
    angles += angles[:1]

    # 绘制雷达图
    ax.plot(angles, scores, 'o-', linewidth=3, color=COLORS['primary'][0], markersize=8)
    ax.fill(angles, scores, alpha=0.25, color=COLORS['primary'][0])

    # 设置标签
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(policies, fontsize=10)
    ax.set_ylim(0, 5)
    ax.set_yticks([1, 2, 3, 4, 5])
    ax.set_yticklabels(['1', '2', '3', '4', '5'], fontsize=9)
    ax.grid(True)

    # 添加分数标签
    for angle, score, policy in zip(angles[:-1], scores[:-1], policies):
        ax.text(angle, score + 0.1, f'{score}', ha='center', va='center',
                fontweight='bold', fontsize=9)

    ax.set_title('国家政策支持力度评估雷达图\n(满分5分)', fontsize=14, pad=30)

    plt.tight_layout()
    return fig

# 继续生成更多图表...
def generate_all_charts():
    """生成所有图表"""
    set_chart_style()
    font = setup_font()

    # 读取图表规划
    with open('chart_plan.json', 'r', encoding='utf-8') as f:
        plan = json.load(f)

    output_dir = create_output_dir(plan)

    # 生成前10个图表
    charts = [
        (chart_1_基本情况_1, 'chart_1_基本情况_1.png'),
        (chart_1_需求分布_2, 'chart_1_需求分布_2.png'),
        (chart_1_市场趋势_3, 'chart_1_市场趋势_3.png'),
        (chart_1_目标时间轴_4, 'chart_1_目标时间轴_4.png'),
        (chart_1_投资回报_5, 'chart_1_投资回报_5.png'),
        (chart_2_政策支持_6, 'chart_2_政策支持_6.png'),
        (chart_2_发展现状_7, 'chart_2_发展现状_7.png'),
        (chart_2_目的地分布_8, 'chart_2_目的地分布_8.png'),
        (chart_2_痛点分析_9, 'chart_2_痛点分析_9.png'),
        (chart_2_数字化水平_10, 'chart_2_数字化水平_10.png')
    ]

    generated_files = []
    for chart_func, filename in charts:
        try:
            fig = chart_func()
            filepath = save_chart(fig, filename, output_dir)
            generated_files.append(filepath)
            plt.close(fig)
        except Exception as e:
            print(f"生成图表 {filename} 时出错: {e}")

    return generated_files

def chart_2_发展现状_7():
    """北京市数字农旅发展现状"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 8))

    # 各区数字农业示范基地数量
    districts = ['海淀区', '昌平区', '大兴区', '通州区', '房山区', '密云区', '延庆区']
    base_counts = [8, 6, 5, 3, 4, 3, 2]

    bars = ax1.bar(districts, base_counts, color=COLORS['primary'][:7], alpha=0.8,
                   edgecolor='white', linewidth=2)
    ax1.set_title('北京各区数字农业示范基地数量', fontsize=12)
    ax1.set_ylabel('基地数量(个)')
    ax1.tick_params(axis='x', rotation=45)

    # 添加数值标签
    for bar, count in zip(bars, base_counts):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                f'{count}', ha='center', va='bottom', fontweight='bold')

    # 数字化水平评估
    aspects = ['基础设施', '技术应用', '数据管理', '服务创新', '人才培养']
    beijing_scores = [4.2, 3.8, 3.5, 4.0, 3.6]
    tongzhou_scores = [3.5, 3.2, 2.8, 3.3, 3.0]

    x = np.arange(len(aspects))
    width = 0.35

    ax2.bar(x - width/2, beijing_scores, width, label='北京市平均',
            color=COLORS['secondary'][0], alpha=0.8)
    ax2.bar(x + width/2, tongzhou_scores, width, label='通州区',
            color=COLORS['secondary'][1], alpha=0.8)

    ax2.set_xlabel('评估维度')
    ax2.set_ylabel('评分(满分5分)')
    ax2.set_title('数字化水平对比评估', fontsize=12)
    ax2.set_xticks(x)
    ax2.set_xticklabels(aspects, rotation=45)
    ax2.legend()
    ax2.grid(True, alpha=0.3, axis='y')

    plt.tight_layout()
    return fig

def chart_2_目的地分布_8():
    """通州区旅游目的地分布"""
    fig, ax = plt.subplots(figsize=(12, 8))

    # 模拟地图坐标
    destinations = {
        '大运河森林公园': (2, 4, 4.5),
        '宋庄艺术区': (4, 3, 4.2),
        '台湖演艺小镇': (3, 2, 4.0),
        '城市绿心森林公园': (1, 3, 4.3),
        '张家湾古镇': (5, 1, 3.8),
        '西集生态园': (6, 2, 3.6)
    }

    # 绘制散点图
    for name, (x, y, rating) in destinations.items():
        size = rating * 100
        color = COLORS['primary'][hash(name) % len(COLORS['primary'])]
        ax.scatter(x, y, s=size, c=color, alpha=0.7, edgecolors='white', linewidth=2)
        ax.annotate(name, (x, y), xytext=(5, 5), textcoords='offset points',
                   fontsize=10, fontweight='bold')
        ax.annotate(f'评分:{rating}', (x, y), xytext=(5, -15), textcoords='offset points',
                   fontsize=9, style='italic')

    ax.set_xlim(0, 7)
    ax.set_ylim(0, 5)
    ax.set_xlabel('东西方向')
    ax.set_ylabel('南北方向')
    ax.set_title('通州区主要旅游目的地分布图\n(圆圈大小表示评分高低)', fontsize=14, pad=20)
    ax.grid(True, alpha=0.3)

    # 添加图例
    legend_elements = [plt.scatter([], [], s=rating*100, c='gray', alpha=0.7,
                                  label=f'评分{rating}') for rating in [3.5, 4.0, 4.5]]
    ax.legend(handles=legend_elements, title='评分等级', loc='upper right')

    plt.tight_layout()
    return fig

def chart_2_痛点分析_9():
    """宋庄镇农业发展痛点分析"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 8))

    # 漏斗图 - 问题严重程度
    problems = ['规模小', '效益低', '品牌弱', '资源利用不充分', '技术水平低']
    severity = [100, 85, 75, 60, 45]  # 严重程度百分比

    # 创建漏斗图
    colors = COLORS['primary'][:5]
    y_pos = np.arange(len(problems))

    for i, (problem, sev, color) in enumerate(zip(problems, severity, colors)):
        width = sev / 100 * 0.8  # 标准化宽度
        left = (1 - width) / 2   # 居中对齐

        ax1.barh(i, width, left=left, height=0.6, color=color, alpha=0.8,
                edgecolor='white', linewidth=2)
        ax1.text(0.5, i, f'{problem}\n({sev}%)', ha='center', va='center',
                fontweight='bold', fontsize=10)

    ax1.set_xlim(0, 1)
    ax1.set_ylim(-0.5, len(problems) - 0.5)
    ax1.set_yticks([])
    ax1.set_xticks([])
    ax1.set_title('农业发展痛点严重程度', fontsize=12)

    # 解决方案优先级
    solutions = ['数字化转型', '品牌建设', '规模化经营', '技术升级', '资源整合']
    priority = [95, 80, 70, 65, 60]
    urgency = [90, 75, 60, 70, 55]

    scatter = ax2.scatter(priority, urgency, s=[p*3 for p in priority],
                         c=COLORS['secondary'][:5], alpha=0.7,
                         edgecolors='white', linewidth=2)

    for i, solution in enumerate(solutions):
        ax2.annotate(solution, (priority[i], urgency[i]),
                    xytext=(5, 5), textcoords='offset points',
                    fontsize=10, fontweight='bold')

    ax2.set_xlabel('重要性')
    ax2.set_ylabel('紧急性')
    ax2.set_title('解决方案优先级矩阵', fontsize=12)
    ax2.grid(True, alpha=0.3)
    ax2.set_xlim(50, 100)
    ax2.set_ylim(50, 100)

    # 添加象限标识
    ax2.axhline(y=75, color='red', linestyle='--', alpha=0.5)
    ax2.axvline(x=75, color='red', linestyle='--', alpha=0.5)
    ax2.text(87.5, 87.5, '高优先级', ha='center', va='center',
            bbox=dict(boxstyle="round,pad=0.3", facecolor='yellow', alpha=0.5))

    plt.tight_layout()
    return fig

def chart_2_数字化水平_10():
    """宋庄镇数字化水平评估"""
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 8))

    # 仪表盘 - 总体数字化水平
    score = 3.2  # 总分5分

    # 创建仪表盘
    theta = np.linspace(0, np.pi, 100)
    r = np.ones_like(theta)

    ax1.plot(theta, r, 'k-', linewidth=8, alpha=0.3)

    # 根据分数绘制彩色部分
    score_theta = np.linspace(0, np.pi * (score/5), int(100 * score/5))
    score_r = np.ones_like(score_theta)
    ax1.plot(score_theta, score_r, color=COLORS['primary'][0], linewidth=8)

    # 添加指针
    pointer_angle = np.pi * (score/5)
    ax1.arrow(0, 0, 0.8*np.cos(pointer_angle), 0.8*np.sin(pointer_angle),
             head_width=0.1, head_length=0.1, fc='red', ec='red')

    ax1.set_xlim(-1.2, 1.2)
    ax1.set_ylim(-0.2, 1.2)
    ax1.set_aspect('equal')
    ax1.axis('off')
    ax1.text(0, -0.1, f'总体数字化水平\n{score}/5.0', ha='center', va='top',
            fontsize=12, fontweight='bold')

    # 各维度评分
    dimensions = ['基础设施', '技术应用', '数据应用', '数字化管理']
    scores = [3.5, 3.0, 2.8, 3.2]

    bars = ax2.bar(dimensions, scores, color=COLORS['secondary'][:4], alpha=0.8,
                   edgecolor='white', linewidth=2)
    ax2.set_title('各维度数字化水平', fontsize=12)
    ax2.set_ylabel('评分(满分5分)')
    ax2.tick_params(axis='x', rotation=45)
    ax2.set_ylim(0, 5)
    ax2.grid(True, alpha=0.3, axis='y')

    # 添加数值标签
    for bar, score in zip(bars, scores):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                f'{score}', ha='center', va='bottom', fontweight='bold')

    # 数字化应用现状
    applications = ['移动支付', '在线预订', '智能导览', '数据分析', 'VR体验', '物联网']
    adoption_rate = [75, 45, 25, 20, 10, 15]

    ax3.barh(applications, adoption_rate, color=COLORS['accent'][:6], alpha=0.8)
    ax3.set_xlabel('应用普及率(%)')
    ax3.set_title('数字化应用普及情况', fontsize=12)
    ax3.grid(True, alpha=0.3, axis='x')

    # 数字化发展趋势
    years = list(range(2020, 2026))
    digital_index = [2.1, 2.4, 2.8, 3.2, 3.8, 4.2]

    ax4.plot(years, digital_index, marker='o', linewidth=3,
             color=COLORS['primary'][2], markersize=8)
    ax4.fill_between(years, digital_index, alpha=0.3, color=COLORS['primary'][2])
    ax4.set_xlabel('年份')
    ax4.set_ylabel('数字化指数')
    ax4.set_title('数字化发展趋势预测', fontsize=12)
    ax4.grid(True, alpha=0.3)

    # 添加数值标签
    for year, index in zip(years, digital_index):
        ax4.annotate(f'{index}', (year, index), textcoords="offset points",
                    xytext=(0,10), ha='center', fontsize=9, fontweight='bold')

    plt.tight_layout()
    return fig

if __name__ == "__main__":
    generated_files = generate_all_charts()
    print(f"\n总共生成了 {len(generated_files)} 个图表")
