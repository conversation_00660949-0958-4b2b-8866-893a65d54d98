#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re

def read_markdown_file(filepath):
    """读取markdown文件"""
    with open(filepath, 'r', encoding='utf-8') as f:
        return f.read()

def write_markdown_file(filepath, content):
    """写入markdown文件"""
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(content)

def insert_charts_at_sections():
    """在各个章节标题后插入图表"""
    
    # 读取原始文档
    source_file = "markdown_report/01_北京通州区宋庄镇农旅数字化推广项目可行性研究报告.md"
    content = read_markdown_file(source_file)
    
    # 定义要插入的图表
    chart_insertions = [
        # 在第一章后插入基础图表
        {
            'after': '## 第一章 项目概述',
            'charts': [
                '\n\n![宋庄镇基本情况综合分析](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_1_基本情况_1.png)\n\n*图1-1 宋庄镇基本情况综合分析*\n\n',
                '\n\n![市场需求与竞争环境分析](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_1_需求分布_2.png)\n\n*图1-2 市场需求与竞争环境分析*\n\n',
                '\n\n![数字农业发展趋势与项目可行性分析](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_1_市场趋势_3.png)\n\n*图1-3 数字农业发展趋势与项目可行性分析*\n\n'
            ]
        },
        # 在项目目标部分插入时间轴和投资回报
        {
            'after': '#### 1.2.3 项目目标',
            'charts': [
                '\n\n![项目实施时间轴与里程碑规划](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_1_目标时间轴_4.png)\n\n*图1-4 项目实施时间轴与里程碑规划*\n\n',
                '\n\n![项目投资回报综合分析](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_1_投资回报_5.png)\n\n*图1-5 项目投资回报综合分析*\n\n'
            ]
        },
        # 在第二章后插入政策和发展现状图表
        {
            'after': '## 第二章 建设背景与必要性分析',
            'charts': [
                '\n\n![国家政策支持力度综合评估](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_2_政策支持_6.png)\n\n*图2-1 国家政策支持力度综合评估*\n\n',
                '\n\n![北京市数字农旅发展现状对比分析](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_2_发展现状_7.png)\n\n*图2-2 北京市数字农旅发展现状对比分析*\n\n',
                '\n\n![通州区主要旅游目的地分布与评价](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_2_目的地分布_8.png)\n\n*图2-3 通州区主要旅游目的地分布与评价*\n\n'
            ]
        },
        # 在痛点分析部分插入相关图表
        {
            'after': '### 2.3 宋庄镇农旅发展现状与痛点',
            'charts': [
                '\n\n![宋庄镇农业发展痛点与解决方案分析](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_2_痛点分析_9.png)\n\n*图2-4 宋庄镇农业发展痛点与解决方案分析*\n\n',
                '\n\n![宋庄镇数字化发展水平评估](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_2_数字化水平_10.png)\n\n*图2-5 宋庄镇数字化发展水平评估*\n\n'
            ]
        },
        # 在第三章后插入资源条件分析图表
        {
            'after': '## 第三章 宋庄镇资源条件分析',
            'charts': [
                '\n\n![宋庄镇地理位置与交通优势分析](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_地理优势_11.png)\n\n*图3-1 宋庄镇地理位置与交通优势分析*\n\n',
                '\n\n![宋庄镇气候条件与农业适宜性评估](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_气候条件_12.png)\n\n*图3-2 宋庄镇气候条件与农业适宜性评估*\n\n',
                '\n\n![土地资源利用现状与规划分析](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_土地利用_13.png)\n\n*图3-3 土地资源利用现状与规划分析*\n\n'
            ]
        },
        # 在艺术文化资源部分插入相关图表
        {
            'after': '### 3.2 艺术文化资源',
            'charts': [
                '\n\n![宋庄艺术区发展历程与成就](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_艺术发展_14.png)\n\n*图3-4 宋庄艺术区发展历程与成就*\n\n',
                '\n\n![艺术机构类型与规模分析](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_机构类型_16.png)\n\n*图3-5 艺术机构类型与规模分析*\n\n'
            ]
        },
        # 在基础设施条件部分插入相关图表
        {
            'after': '### 3.3 基础设施条件',
            'charts': [
                '\n\n![基础设施建设水平评估](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_基础设施_17.png)\n\n*图3-6 基础设施建设水平评估*\n\n',
                '\n\n![数字基础设施现状与发展规划](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_数字基础_18.png)\n\n*图3-7 数字基础设施现状与发展规划*\n\n',
                '\n\n![公共服务设施配套情况分析](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_公共服务_19.png)\n\n*图3-8 公共服务设施配套情况分析*\n\n'
            ]
        },
        # 在人力资源条件部分插入相关图表
        {
            'after': '### 3.4 人力资源条件',
            'charts': [
                '\n\n![人力资源结构与素质分析](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_人力结构_20.png)\n\n*图3-9 人力资源结构与素质分析*\n\n',
                '\n\n![数字素养水平评估与提升规划](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_数字素养_21.png)\n\n*图3-10 数字素养水平评估与提升规划*\n\n',
                '\n\n![专业人才需求与缺口分析](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_人才缺口_22.png)\n\n*图3-11 专业人才需求与缺口分析*\n\n'
            ]
        },
        # 在市场需求分析部分插入相关图表
        {
            'after': '### 3.5 市场需求分析',
            'charts': [
                '\n\n![城区居民休闲需求特征分析](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_休闲需求_23.png)\n\n*图3-12 城区居民休闲需求特征分析*\n\n',
                '\n\n![目标客户群体画像与偏好分析](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_客户画像_24.png)\n\n*图3-13 目标客户群体画像与偏好分析*\n\n',
                '\n\n![农旅市场竞争格局与定位分析](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_竞争格局_25.png)\n\n*图3-14 农旅市场竞争格局与定位分析*\n\n'
            ]
        },
        # 在政策环境分析部分插入相关图表
        {
            'after': '### 3.6 政策环境分析',
            'charts': [
                '\n\n![政策环境匹配度综合评估](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_政策匹配_26.png)\n\n*图3-15 政策环境匹配度综合评估*\n\n',
                '\n\n![各项政策受益程度分析](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_政策受益_27.png)\n\n*图3-16 各项政策受益程度分析*\n\n'
            ]
        },
        # 在技术可行性分析部分插入相关图表
        {
            'after': '### 3.7 技术可行性分析',
            'charts': [
                '\n\n![关键技术成熟度与应用前景](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_技术成熟_28.png)\n\n*图3-17 关键技术成熟度与应用前景*\n\n',
                '\n\n![技术实施难度与风险评估](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_技术难度_29.png)\n\n*图3-18 技术实施难度与风险评估*\n\n'
            ]
        },
        # 在经济可行性分析部分插入相关图表
        {
            'after': '### 3.8 经济可行性分析',
            'charts': [
                '\n\n![项目投资成本结构分析](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_投资成本_31.png)\n\n*图3-19 项目投资成本结构分析*\n\n',
                '\n\n![项目收益预测与增长趋势](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_收益预测_32.png)\n\n*图3-20 项目收益预测与增长趋势*\n\n',
                '\n\n![关键财务指标综合评价](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_财务指标_33.png)\n\n*图3-21 关键财务指标综合评价*\n\n'
            ]
        },
        # 在社会可行性分析部分插入相关图表
        {
            'after': '### 3.9 社会可行性分析',
            'charts': [
                '\n\n![社会效益评估与影响分析](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_社会效益_35.png)\n\n*图3-22 社会效益评估与影响分析*\n\n',
                '\n\n![各利益相关方接受度调查](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_社会接受_37.png)\n\n*图3-23 各利益相关方接受度调查*\n\n'
            ]
        },
        # 在环境可行性分析部分插入相关图表
        {
            'after': '### 3.10 环境可行性分析',
            'charts': [
                '\n\n![环境效益分析与生态价值](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_环境效益_39.png)\n\n*图3-24 环境效益分析与生态价值*\n\n'
            ]
        },
        # 在综合可行性评价部分插入相关图表
        {
            'after': '### 3.11 综合可行性评价',
            'charts': [
                '\n\n![项目综合可行性评价结果](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_综合评价_40.png)\n\n*图3-25 项目综合可行性评价结果*\n\n'
            ]
        },
        # 在第四章后插入实施规划图表
        {
            'after': '## 第四章 结论与建议',
            'charts': [
                '\n\n![项目实施路线图与时间安排](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_4_实施路线_41.png)\n\n*图4-1 项目实施路线图与时间安排*\n\n',
                '\n\n![项目发展前景与预期成果](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_4_发展前景_42.png)\n\n*图4-2 项目发展前景与预期成果*\n\n'
            ]
        }
    ]
    
    # 逐个插入图表
    inserted_count = 0
    for insertion in chart_insertions:
        search_text = insertion['after']
        charts = insertion['charts']
        
        if search_text in content:
            # 在找到的标题后插入所有图表
            charts_text = ''.join(charts)
            content = content.replace(search_text, search_text + charts_text)
            inserted_count += len(charts)
            print(f"✓ 在 '{search_text}' 后插入了 {len(charts)} 个图表")
        else:
            print(f"⚠️ 未找到插入位置: {search_text}")
    
    # 保存修改后的文档
    write_markdown_file(source_file, content)
    print(f"\n✅ 图表引用插入完成！文档已更新: {source_file}")
    print(f"📊 总共插入了 {inserted_count} 个图表引用")
    
    return inserted_count

if __name__ == "__main__":
    try:
        count = insert_charts_at_sections()
        print(f"\n🎉 任务完成！成功插入了 {count} 个专业中文图表引用")
        print(f"📋 文档现在包含了丰富的可视化图表，提升了专业性和可读性")
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()
