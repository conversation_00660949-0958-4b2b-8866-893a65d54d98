{"document_info": {"total_lines": 3470, "total_words": 85000, "total_chapters": 6, "content_units": {"text_paragraphs": 280, "tables": 0, "ordered_lists": 45, "unordered_lists": 85, "headers": 65, "data_blocks": 25, "code_blocks": 0, "quote_blocks": 0}, "calculated_chart_count": 34, "calculation_process": "基于行数规模计算：3470÷80=43张，最小保障：3470÷100=34张，最终确定34张图表"}, "professional_settings": {"color_scheme": "fresh_professional", "quality_level": "premium", "chart_complexity": "advanced", "combination_ratio": 0.4}, "charts": [{"id": "chart_01", "title": "宋庄镇农旅发展现状分析", "type": "advanced_bar_combo", "chapter": "第一章 项目概述", "data_description": "农业规模、艺术资源、基础设施对比分析", "professional_features": {"has_secondary_axis": true, "includes_trend_line": true, "color_scheme": "primary", "annotations": true}, "data": {"categories": ["农业规模", "艺术资源", "基础设施", "数字化水平", "服务质量"], "values1": [65, 85, 70, 45, 60], "values2": [80, 90, 85, 75, 80], "trend": [1.2, 1.1, 1.2, 1.7, 1.3]}, "save_path": "charts/chart_01.png"}, {"id": "chart_02", "title": "北京城区居民休闲需求分布", "type": "donut_with_breakdown", "chapter": "第一章 项目概述", "data_description": "城区居民周末休闲需求类型及占比", "professional_features": {"has_secondary_axis": false, "includes_trend_line": false, "color_scheme": "secondary", "annotations": true}, "data": {"labels": ["亲子体验", "自然观光", "文化艺术", "休闲放松"], "sizes": [35, 28, 22, 15], "breakdown": {"亲子体验": [20, 15], "自然观光": [15, 13], "文化艺术": [12, 10], "休闲放松": [8, 7]}}, "save_path": "charts/chart_02.png"}, {"id": "chart_03", "title": "数字农旅发展趋势预测", "type": "multi_line_area", "chapter": "第二章 建设背景与必要性分析", "data_description": "2020-2025年数字农旅各项指标发展趋势", "professional_features": {"has_secondary_axis": false, "includes_trend_line": true, "color_scheme": "gradient", "annotations": true}, "data": {"x_data": [2020, 2021, 2022, 2023, 2024, 2025], "y_data1": [100, 125, 155, 190, 235, 285], "y_data2": [80, 105, 135, 170, 210, 255], "y_data3": [60, 80, 105, 135, 170, 210]}, "save_path": "charts/chart_03.png"}, {"id": "chart_04", "title": "政策支持体系框架", "type": "sankey_flow", "chapter": "第二章 建设背景与必要性分析", "data_description": "国家、市、区、镇四级政策支持体系", "professional_features": {"has_secondary_axis": false, "includes_trend_line": false, "color_scheme": "business", "annotations": true}, "data": {"stages": ["国家政策", "市级政策", "区级政策", "镇级实施"], "flows": ["乡村振兴", "数字乡村", "文旅融合", "农旅发展"]}, "save_path": "charts/chart_04.png"}, {"id": "chart_05", "title": "投入产出效益分析", "type": "bubble_regression", "chapter": "第二章 建设背景与必要性分析", "data_description": "不同投入规模下的预期收益关系", "professional_features": {"has_secondary_axis": false, "includes_trend_line": true, "color_scheme": "primary", "annotations": true}, "data": {"x_data": [50, 80, 120, 180, 250, 350, 480, 650], "y_data": [65, 95, 140, 200, 275, 370, 490, 630], "sizes": [200, 300, 400, 500, 600, 700, 800, 900], "categories": ["核心项目", "核心项目", "扩展项目", "扩展项目", "综合项目", "综合项目", "旗舰项目", "旗舰项目"]}, "save_path": "charts/chart_05.png"}, {"id": "chart_06", "title": "宋庄镇资源条件评估", "type": "correlation_cluster", "chapter": "第三章 宋庄镇资源条件分析", "data_description": "自然资源、艺术资源、基础设施相关性分析", "professional_features": {"has_secondary_axis": false, "includes_trend_line": false, "color_scheme": "accent", "annotations": true}, "data": {"variables": ["自然环境", "艺术资源", "交通条件", "数字基础", "人力资源"]}, "save_path": "charts/chart_06.png"}, {"id": "chart_07", "title": "数字农旅项目KPI仪表盘", "type": "kpi_dashboard", "chapter": "第三章 宋庄镇资源条件分析", "data_description": "项目关键指标综合展示", "professional_features": {"has_secondary_axis": false, "includes_trend_line": false, "color_scheme": "business", "annotations": true}, "data": {"kpis": {"数字化覆盖率": {"value": 75.6, "target": 80, "unit": "%"}, "游客满意度": {"value": 4.2, "target": 4.0, "unit": "/5"}, "收入增长率": {"value": 18.4, "target": 15, "unit": "%"}, "环保达标率": {"value": 88.1, "target": 85, "unit": "%"}}}, "save_path": "charts/chart_07.png"}, {"id": "chart_08", "title": "艺术农业融合业态分布", "type": "hierarchical_treemap", "chapter": "第三章 宋庄镇资源条件分析", "data_description": "艺术与农业融合的各种业态占比", "professional_features": {"has_secondary_axis": false, "includes_trend_line": false, "color_scheme": "primary", "annotations": true}, "data": {"treemap_data": {"艺农融合": {"艺术农园": 30, "创意农场": 25, "文化体验": 25, "数字展示": 20}}}, "save_path": "charts/chart_08.png"}, {"id": "chart_09", "title": "不同客户群体需求分布", "type": "distribution_violin", "chapter": "第三章 宋庄镇资源条件分析", "data_description": "城区家庭、青年群体、中老年群体需求分布", "professional_features": {"has_secondary_axis": false, "includes_trend_line": false, "color_scheme": "secondary", "annotations": true}, "data": {"groups": ["城区家庭", "青年群体", "中老年群体"]}, "save_path": "charts/chart_09.png"}, {"id": "chart_10", "title": "数字农旅竞争力评估", "type": "multi_radar_compare", "chapter": "第三章 宋庄镇资源条件分析", "data_description": "宋庄镇与竞争对手多维度对比", "professional_features": {"has_secondary_axis": false, "includes_trend_line": false, "color_scheme": "gradient", "annotations": true}, "data": {"categories": ["区位优势", "资源特色", "数字化水平", "服务质量", "品牌影响", "发展潜力"], "values1": [8, 9, 6, 7, 6, 8], "values2": [7, 6, 8, 8, 7, 7], "values3": [9, 7, 9, 9, 8, 9]}, "save_path": "charts/chart_10.png"}, {"id": "chart_11", "title": "利益相关方网络关系", "type": "force_directed", "chapter": "第四章 项目方案设计", "data_description": "政府、企业、农户、游客等利益相关方关系", "professional_features": {"has_secondary_axis": false, "includes_trend_line": false, "color_scheme": "accent", "annotations": true}, "data": {"nodes": ["政府部门", "农业合作社", "艺术家群体", "旅游企业", "技术公司", "游客群体"]}, "save_path": "charts/chart_11.png"}, {"id": "chart_12", "title": "项目实施时间线", "type": "timeline_milestone", "chapter": "第四章 项目方案设计", "data_description": "项目各阶段关键节点和里程碑", "professional_features": {"has_secondary_axis": false, "includes_trend_line": false, "color_scheme": "business", "annotations": true}, "data": {"milestones": [{"date": "2024年1月", "event": "项目启动", "type": "start"}, {"date": "2024年3月", "event": "基础建设", "type": "process"}, {"date": "2024年6月", "event": "系统上线", "type": "milestone"}, {"date": "2024年9月", "event": "试运营", "type": "process"}, {"date": "2024年12月", "event": "正式运营", "type": "milestone"}, {"date": "2025年6月", "event": "全面推广", "type": "end"}]}, "save_path": "charts/chart_12.png"}, {"id": "chart_13", "title": "数字技术应用架构", "type": "advanced_bar_combo", "chapter": "第四章 项目方案设计", "data_description": "各类数字技术在项目中的应用程度", "professional_features": {"has_secondary_axis": true, "includes_trend_line": true, "color_scheme": "primary", "annotations": true}, "data": {"categories": ["物联网", "大数据", "云计算", "人工智能", "区块链"], "values1": [80, 70, 85, 60, 40], "values2": [90, 85, 95, 80, 65], "trend": [1.1, 1.2, 1.1, 1.3, 1.6]}, "save_path": "charts/chart_13.png"}, {"id": "chart_14", "title": "运营模式收入结构", "type": "donut_with_breakdown", "chapter": "第五章 运营模式与商业模式", "data_description": "不同运营模式的收入占比及细分", "professional_features": {"has_secondary_axis": false, "includes_trend_line": false, "color_scheme": "secondary", "annotations": true}, "data": {"labels": ["门票收入", "体验服务", "农产品销售", "文创产品"], "sizes": [30, 35, 20, 15], "breakdown": {"门票收入": [18, 12], "体验服务": [20, 15], "农产品销售": [12, 8], "文创产品": [8, 7]}}, "save_path": "charts/chart_14.png"}, {"id": "chart_15", "title": "财务预测与投资回报", "type": "multi_line_area", "chapter": "第五章 运营模式与商业模式", "data_description": "5年期财务预测和投资回报分析", "professional_features": {"has_secondary_axis": false, "includes_trend_line": true, "color_scheme": "gradient", "annotations": true}, "data": {"x_data": [2024, 2025, 2026, 2027, 2028], "y_data1": [500, 800, 1200, 1600, 2000], "y_data2": [300, 500, 750, 1000, 1300], "y_data3": [200, 300, 450, 600, 700]}, "save_path": "charts/chart_15.png"}, {"id": "chart_16", "title": "风险评估矩阵", "type": "bubble_regression", "chapter": "第六章 风险评估与应对措施", "data_description": "各类风险的概率与影响程度分析", "professional_features": {"has_secondary_axis": false, "includes_trend_line": false, "color_scheme": "accent", "annotations": true}, "data": {"x_data": [20, 35, 50, 65, 80], "y_data": [30, 45, 60, 75, 85], "sizes": [300, 400, 500, 600, 700], "categories": ["技术风险", "市场风险", "政策风险", "运营风险", "财务风险"]}, "save_path": "charts/chart_16.png"}, {"id": "chart_17", "title": "可持续发展指标体系", "type": "correlation_cluster", "chapter": "第六章 风险评估与应对措施", "data_description": "经济、社会、环境可持续发展指标相关性", "professional_features": {"has_secondary_axis": false, "includes_trend_line": false, "color_scheme": "primary", "annotations": true}, "data": {"variables": ["经济效益", "社会效益", "环境效益", "技术创新", "文化传承"]}, "save_path": "charts/chart_17.png"}, {"id": "chart_18", "title": "项目综合效益评估", "type": "kpi_dashboard", "chapter": "第七章 效益分析", "data_description": "经济、社会、环境效益综合评估", "professional_features": {"has_secondary_axis": false, "includes_trend_line": false, "color_scheme": "business", "annotations": true}, "data": {"kpis": {"经济效益": {"value": 2580, "target": 2500, "unit": "万元"}, "就业创造": {"value": 320, "target": 300, "unit": "个"}, "游客增长": {"value": 45, "target": 40, "unit": "%"}, "农民增收": {"value": 3200, "target": 3000, "unit": "元"}}}, "save_path": "charts/chart_18.png"}, {"id": "chart_19", "title": "数字化转型路径", "type": "sankey_flow", "chapter": "第七章 效益分析", "data_description": "从传统农旅到数字农旅的转型路径", "professional_features": {"has_secondary_axis": false, "includes_trend_line": false, "color_scheme": "gradient", "annotations": true}, "data": {"stages": ["传统农业", "数字化改造", "智能化升级", "融合发展"], "flows": ["基础设施", "技术应用", "服务创新", "模式创新"]}, "save_path": "charts/chart_19.png"}, {"id": "chart_20", "title": "区域发展影响分析", "type": "hierarchical_treemap", "chapter": "第七章 效益分析", "data_description": "项目对区域各方面发展的影响程度", "professional_features": {"has_secondary_axis": false, "includes_trend_line": false, "color_scheme": "secondary", "annotations": true}, "data": {"treemap_data": {"区域发展": {"经济发展": 35, "社会发展": 25, "文化发展": 25, "环境改善": 15}}}, "save_path": "charts/chart_20.png"}, {"id": "chart_21", "title": "人才培养体系分布", "type": "distribution_violin", "chapter": "第八章 实施保障", "data_description": "不同层次人才培养需求分布", "professional_features": {"has_secondary_axis": false, "includes_trend_line": false, "color_scheme": "primary", "annotations": true}, "data": {"groups": ["技术人才", "管理人才", "服务人才"]}, "save_path": "charts/chart_21.png"}, {"id": "chart_22", "title": "技术创新能力评估", "type": "multi_radar_compare", "chapter": "第八章 实施保障", "data_description": "项目技术创新各维度能力对比", "professional_features": {"has_secondary_axis": false, "includes_trend_line": false, "color_scheme": "accent", "annotations": true}, "data": {"categories": ["研发能力", "应用能力", "创新能力", "协作能力", "学习能力", "适应能力"], "values1": [7, 8, 6, 8, 7, 8], "values2": [8, 7, 8, 7, 8, 7], "values3": [9, 9, 9, 9, 9, 9]}, "save_path": "charts/chart_22.png"}, {"id": "chart_23", "title": "组织管理网络", "type": "force_directed", "chapter": "第八章 实施保障", "data_description": "项目组织管理各部门协作关系", "professional_features": {"has_secondary_axis": false, "includes_trend_line": false, "color_scheme": "business", "annotations": true}, "data": {"nodes": ["项目办", "技术部", "运营部", "市场部", "财务部", "监督部"]}, "save_path": "charts/chart_23.png"}, {"id": "chart_24", "title": "实施进度计划", "type": "timeline_milestone", "chapter": "第八章 实施保障", "data_description": "项目实施各阶段详细进度安排", "professional_features": {"has_secondary_axis": false, "includes_trend_line": false, "color_scheme": "gradient", "annotations": true}, "data": {"milestones": [{"date": "第1季度", "event": "前期准备", "type": "start"}, {"date": "第2季度", "event": "基础建设", "type": "process"}, {"date": "第3季度", "event": "系统开发", "type": "milestone"}, {"date": "第4季度", "event": "试运行", "type": "process"}, {"date": "第5季度", "event": "正式运营", "type": "milestone"}, {"date": "第6季度", "event": "推广扩展", "type": "end"}]}, "save_path": "charts/chart_24.png"}, {"id": "chart_25", "title": "资金投入结构分析", "type": "advanced_bar_combo", "chapter": "第九章 投资估算", "data_description": "各项投资的资金投入及预期回报", "professional_features": {"has_secondary_axis": true, "includes_trend_line": true, "color_scheme": "secondary", "annotations": true}, "data": {"categories": ["基础设施", "技术设备", "人员培训", "营销推广", "运营资金"], "values1": [800, 600, 200, 300, 680], "values2": [1000, 750, 250, 400, 850], "trend": [1.25, 1.25, 1.25, 1.33, 1.25]}, "save_path": "charts/chart_25.png"}, {"id": "chart_26", "title": "成本效益分析", "type": "donut_with_breakdown", "chapter": "第九章 投资估算", "data_description": "项目成本构成及效益分解", "professional_features": {"has_secondary_axis": false, "includes_trend_line": false, "color_scheme": "primary", "annotations": true}, "data": {"labels": ["建设成本", "运营成本", "人力成本", "其他成本"], "sizes": [45, 25, 20, 10], "breakdown": {"建设成本": [30, 15], "运营成本": [15, 10], "人力成本": [12, 8], "其他成本": [6, 4]}}, "save_path": "charts/chart_26.png"}, {"id": "chart_27", "title": "投资回报预测", "type": "multi_line_area", "chapter": "第九章 投资估算", "data_description": "5年期投资回报率变化趋势", "professional_features": {"has_secondary_axis": false, "includes_trend_line": true, "color_scheme": "accent", "annotations": true}, "data": {"x_data": [2024, 2025, 2026, 2027, 2028], "y_data1": [-500, 200, 800, 1400, 2000], "y_data2": [0, 300, 600, 900, 1200], "y_data3": [500, 500, 200, 500, 800]}, "save_path": "charts/chart_27.png"}, {"id": "chart_28", "title": "敏感性分析", "type": "bubble_regression", "chapter": "第九章 投资估算", "data_description": "关键参数变化对项目收益的影响", "professional_features": {"has_secondary_axis": false, "includes_trend_line": true, "color_scheme": "business", "annotations": true}, "data": {"x_data": [80, 90, 100, 110, 120], "y_data": [1800, 2100, 2400, 2700, 3000], "sizes": [400, 500, 600, 700, 800], "categories": ["悲观", "较悲观", "基准", "较乐观", "乐观"]}, "save_path": "charts/chart_28.png"}, {"id": "chart_29", "title": "环境影响评估", "type": "correlation_cluster", "chapter": "第十章 环境影响评估", "data_description": "项目对环境各要素的影响相关性", "professional_features": {"has_secondary_axis": false, "includes_trend_line": false, "color_scheme": "gradient", "annotations": true}, "data": {"variables": ["空气质量", "水体质量", "土壤质量", "生物多样性", "景观美化"]}, "save_path": "charts/chart_29.png"}, {"id": "chart_30", "title": "生态效益评估", "type": "kpi_dashboard", "chapter": "第十章 环境影响评估", "data_description": "项目生态环境效益综合评估", "professional_features": {"has_secondary_axis": false, "includes_trend_line": false, "color_scheme": "primary", "annotations": true}, "data": {"kpis": {"碳减排量": {"value": 120, "target": 100, "unit": "吨"}, "绿化覆盖": {"value": 85, "target": 80, "unit": "%"}, "水资源节约": {"value": 15, "target": 12, "unit": "%"}, "废物利用率": {"value": 78, "target": 75, "unit": "%"}}}, "save_path": "charts/chart_30.png"}, {"id": "chart_31", "title": "绿色发展路径", "type": "sankey_flow", "chapter": "第十章 环境影响评估", "data_description": "项目绿色发展的实施路径", "professional_features": {"has_secondary_axis": false, "includes_trend_line": false, "color_scheme": "secondary", "annotations": true}, "data": {"stages": ["资源投入", "绿色生产", "清洁服务", "循环利用"], "flows": ["清洁能源", "有机农业", "生态旅游", "废物回收"]}, "save_path": "charts/chart_31.png"}, {"id": "chart_32", "title": "社会效益分布", "type": "hierarchical_treemap", "chapter": "第十一章 社会效益评估", "data_description": "项目社会效益各方面占比", "professional_features": {"has_secondary_axis": false, "includes_trend_line": false, "color_scheme": "accent", "annotations": true}, "data": {"treemap_data": {"社会效益": {"就业促进": 30, "文化传承": 25, "教育普及": 25, "社区发展": 20}}}, "save_path": "charts/chart_32.png"}, {"id": "chart_33", "title": "利益相关方满意度", "type": "distribution_violin", "chapter": "第十一章 社会效益评估", "data_description": "不同利益相关方对项目的满意度分布", "professional_features": {"has_secondary_axis": false, "includes_trend_line": false, "color_scheme": "business", "annotations": true}, "data": {"groups": ["政府部门", "当地农民", "游客群体"]}, "save_path": "charts/chart_33.png"}, {"id": "chart_34", "title": "项目综合评价", "type": "multi_radar_compare", "chapter": "第十二章 结论与建议", "data_description": "项目可行性综合评价多维度对比", "professional_features": {"has_secondary_axis": false, "includes_trend_line": false, "color_scheme": "gradient", "annotations": true}, "data": {"categories": ["技术可行性", "经济可行性", "社会可行性", "环境可行性", "政策可行性", "市场可行性"], "values1": [8, 8, 9, 8, 9, 8], "values2": [7, 7, 8, 7, 8, 7], "values3": [9, 9, 9, 9, 9, 9]}, "save_path": "charts/chart_34.png"}]}