#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import matplotlib
matplotlib.use('Agg')

import numpy as np
import matplotlib.pyplot as plt
import os
import warnings
warnings.filterwarnings('ignore')

plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False

# 配色方案
COLORS = ['#FF9AA2', '#FFB7B2', '#FFDAC1', '#E2F0CB', '#B5EAD7', '#C7CEEA']

def create_output_dir():
    output_dir = "output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/"
    os.makedirs(output_dir, exist_ok=True)
    return output_dir

def generate_final_few():
    """生成最后几个图表"""
    output_dir = create_output_dir()
    chart_count = 0
    
    # 图表40: 敏感性分析龙卷风图
    fig, ax = plt.subplots(figsize=(10, 6))
    factors = ['Revenue Level', 'Operating Cost', 'Investment Cost', 'Market Demand', 'Policy Support']
    low_impact = [-15, -12, -8, -10, -5]
    high_impact = [18, 15, 10, 12, 8]
    
    y_pos = np.arange(len(factors))
    
    # 创建龙卷风图
    for i, (factor, low, high) in enumerate(zip(factors, low_impact, high_impact)):
        ax.barh(i, abs(low), left=low, height=0.6, color=COLORS[0], alpha=0.7, label='Negative' if i == 0 else "")
        ax.barh(i, high, height=0.6, color=COLORS[1], alpha=0.7, label='Positive' if i == 0 else "")
        
        # 添加标签
        ax.text(low - 1, i, f'{low}%', ha='right', va='center', fontweight='bold')
        ax.text(high + 1, i, f'+{high}%', ha='left', va='center', fontweight='bold')
    
    ax.set_yticks(y_pos)
    ax.set_yticklabels(factors)
    ax.set_xlabel('Impact on NPV (%)')
    ax.set_title('Sensitivity Analysis - Tornado Chart')
    ax.axvline(x=0, color='black', linewidth=1)
    ax.legend()
    ax.grid(True, alpha=0.3, axis='x')
    
    plt.tight_layout()
    fig.savefig(os.path.join(output_dir, 'chart_3_敏感性_34.png'), dpi=300, bbox_inches='tight')
    plt.close(fig)
    chart_count += 1
    print("✓ Final Chart 1: Sensitivity Analysis Tornado")
    
    # 图表41: 社会风险矩阵
    fig, ax = plt.subplots(figsize=(10, 6))
    risks = ['Digital Divide', 'Cultural Conflict', 'Community Relations', 'Privacy Security', 'Expectation Gap']
    probability = [0.4, 0.2, 0.3, 0.25, 0.35]
    impact = [0.6, 0.8, 0.5, 0.9, 0.4]
    
    # 计算风险等级并设置颜色
    risk_colors = []
    for p, i in zip(probability, impact):
        if p * i > 0.25:
            risk_colors.append('red')
        elif p * i > 0.15:
            risk_colors.append('orange')
        else:
            risk_colors.append('green')
    
    scatter = ax.scatter(probability, impact, s=200, c=risk_colors, alpha=0.7, edgecolors='white', linewidth=2)
    
    for i, risk in enumerate(risks):
        ax.annotate(risk, (probability[i], impact[i]), xytext=(5, 5), 
                   textcoords='offset points', fontsize=9, fontweight='bold')
    
    ax.set_xlabel('Probability')
    ax.set_ylabel('Impact')
    ax.set_title('Social Risk Assessment Matrix')
    ax.grid(True, alpha=0.3)
    ax.set_xlim(0, 0.5)
    ax.set_ylim(0, 1)
    
    # 添加风险区域
    ax.axhline(y=0.6, color='red', linestyle='--', alpha=0.5)
    ax.axvline(x=0.3, color='red', linestyle='--', alpha=0.5)
    ax.text(0.35, 0.8, 'High Risk', ha='center', va='center', 
            bbox=dict(boxstyle="round,pad=0.3", facecolor='red', alpha=0.3))
    ax.text(0.15, 0.3, 'Low Risk', ha='center', va='center', 
            bbox=dict(boxstyle="round,pad=0.3", facecolor='green', alpha=0.3))
    
    plt.tight_layout()
    fig.savefig(os.path.join(output_dir, 'chart_3_社会风险_36.png'), dpi=300, bbox_inches='tight')
    plt.close(fig)
    chart_count += 1
    print("✓ Final Chart 2: Social Risk Matrix")
    
    # 图表42: 环境效益雷达图
    fig, ax = plt.subplots(figsize=(8, 8), subplot_kw=dict(projection='polar'))
    
    env_aspects = ['Ecological Protection', 'Air Quality', 'Water Conservation', 
                   'Energy Efficiency', 'Waste Reduction', 'Carbon Footprint']
    env_scores = [4.3, 4.1, 4.2, 3.8, 4.0, 3.9]
    
    angles = np.linspace(0, 2*np.pi, len(env_aspects), endpoint=False).tolist()
    env_scores += env_scores[:1]
    angles += angles[:1]
    
    ax.plot(angles, env_scores, 'o-', linewidth=3, color=COLORS[2], markersize=8)
    ax.fill(angles, env_scores, alpha=0.25, color=COLORS[2])
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(env_aspects, fontsize=10)
    ax.set_ylim(0, 5)
    ax.set_title('Environmental Benefits Assessment\n(Max Score: 5)', fontsize=14, pad=30)
    ax.grid(True)
    
    # 添加分数标签
    for angle, score in zip(angles[:-1], env_scores[:-1]):
        ax.text(angle, score + 0.1, f'{score}', ha='center', va='center', 
                fontweight='bold', fontsize=9)
    
    plt.tight_layout()
    fig.savefig(os.path.join(output_dir, 'chart_3_环境效益_39.png'), dpi=300, bbox_inches='tight')
    plt.close(fig)
    chart_count += 1
    print("✓ Final Chart 3: Environmental Benefits Radar")
    
    # 图表43: 项目里程碑时间线
    fig, ax = plt.subplots(figsize=(12, 6))
    
    milestones = ['Project Approval', 'Infrastructure Setup', 'System Development', 
                  'Pilot Testing', 'Official Launch', 'Full Operation', 'Evaluation & Optimization']
    dates = [0, 3, 8, 12, 15, 18, 24]  # months
    completion = [100, 100, 100, 80, 60, 40, 20]  # completion percentage
    
    # 创建时间线
    ax.plot(dates, [1]*len(dates), 'o-', linewidth=4, color=COLORS[0], markersize=12)
    
    for i, (milestone, date, comp) in enumerate(zip(milestones, dates, completion)):
        # 根据完成度设置颜色
        if comp == 100:
            color = 'green'
        elif comp >= 50:
            color = 'orange'
        else:
            color = 'red'
        
        ax.scatter(date, 1, s=200, c=color, alpha=0.8, edgecolors='white', linewidth=2, zorder=5)
        
        # 添加里程碑标签
        y_offset = 0.1 if i % 2 == 0 else -0.1
        ax.annotate(f'{milestone}\n({comp}%)', (date, 1), 
                   xytext=(0, y_offset*500), textcoords='offset points',
                   ha='center', va='center' if y_offset > 0 else 'top',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor=COLORS[i % len(COLORS)], alpha=0.7),
                   arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))
    
    ax.set_xlim(-2, 26)
    ax.set_ylim(0.5, 1.5)
    ax.set_xlabel('Timeline (Months)')
    ax.set_title('Project Milestone Timeline')
    ax.set_yticks([])
    ax.grid(True, alpha=0.3, axis='x')
    
    # 添加图例
    legend_elements = [plt.scatter([], [], s=100, c='green', label='Completed'),
                      plt.scatter([], [], s=100, c='orange', label='In Progress'),
                      plt.scatter([], [], s=100, c='red', label='Planned')]
    ax.legend(handles=legend_elements, loc='upper right')
    
    plt.tight_layout()
    fig.savefig(os.path.join(output_dir, 'chart_4_项目里程碑_43.png'), dpi=300, bbox_inches='tight')
    plt.close(fig)
    chart_count += 1
    print("✓ Final Chart 4: Project Milestone Timeline")
    
    # 图表44: 成功因素权重分析
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))
    
    # 成功因素重要性
    factors = ['Policy Support', 'Technology Innovation', 'Market Demand', 
               'Team Capability', 'Financial Resources', 'Community Support']
    weights = [25, 20, 18, 15, 12, 10]
    
    wedges, texts, autotexts = ax1.pie(weights, labels=factors, colors=COLORS,
                                      autopct='%1.1f%%', startangle=90)
    ax1.set_title('Success Factors Weight Distribution')
    
    # 当前满足度评分
    satisfaction = [4.5, 3.8, 4.2, 3.5, 4.0, 4.1]
    
    bars = ax2.bar(factors, satisfaction, color=COLORS, alpha=0.8)
    ax2.set_ylabel('Satisfaction Score (Max 5)')
    ax2.set_title('Current Satisfaction Level')
    ax2.tick_params(axis='x', rotation=45)
    ax2.set_ylim(0, 5)
    ax2.grid(True, alpha=0.3, axis='y')
    
    for bar, score in zip(bars, satisfaction):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.05,
                f'{score}', ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    fig.savefig(os.path.join(output_dir, 'chart_4_成功因素_44.png'), dpi=300, bbox_inches='tight')
    plt.close(fig)
    chart_count += 1
    print("✓ Final Chart 5: Success Factors Analysis")
    
    return chart_count

if __name__ == "__main__":
    try:
        count = generate_final_few()
        print(f"\n✅ Successfully generated {count} final charts!")
        
        # 统计总数
        output_dir = create_output_dir()
        total_charts = len([f for f in os.listdir(output_dir) if f.endswith('.png')])
        print(f"📊 Total charts generated: {total_charts}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
