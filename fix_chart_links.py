#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re

def read_markdown_file(filepath):
    """读取markdown文件"""
    with open(filepath, 'r', encoding='utf-8') as f:
        return f.read()

def write_markdown_file(filepath, content):
    """写入markdown文件"""
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(content)

def fix_chart_links():
    """修复markdown文档中的图表链接"""
    
    # 读取文档
    source_file = "markdown_report/01_北京通州区宋庄镇农旅数字化推广项目可行性研究报告.md"
    content = read_markdown_file(source_file)
    
    print("开始修复图表链接...")
    
    # 定义正确的图表路径前缀
    correct_path_prefix = "output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/"
    
    # 修复各种错误的路径格式
    fixes = [
        # 修复混乱的路径
        (r'!\[([^\]]*)\]\(\.\/output\/charts\/png\/[^)]*\)分析\]\(\.\/output\/charts\/北京通州区宋庄镇农旅数字化推广项目\/[^)]*\)', 
         r'![\1](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_2_目的地分布_8.png)'),
        
        # 修复 ./output/charts/png/ 路径
        (r'!\[([^\]]*)\]\(\.\/output\/charts\/png\/([^)]*)\)', 
         r'![\1](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_1_需求分布_2.png)'),
        
        # 修复 output/charts/农旅数字化推广项目/ 路径
        (r'!\[([^\]]*)\]\(output\/charts\/农旅数字化推广项目\/([^)]*)\)', 
         r'![\1](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_政策匹配_26.png)'),
        
        # 修复重复的图表引用
        (r'!\[游客满意度与消费金额关系\n\n!\[满意度与消费金额关系\]\(\.\/output\/charts\/png\/满意度与消费关系_散点图\.png\)分析\]\(\.\/output\/charts\/北京通州区宋庄镇农旅数字化推广项目\/满意度与消费关系_散点图\.png\)',
         r'![满意度与消费金额关系分析](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_2_目的地分布_8.png)'),
    ]
    
    # 应用修复
    fix_count = 0
    for pattern, replacement in fixes:
        matches = re.findall(pattern, content)
        if matches:
            content = re.sub(pattern, replacement, content)
            fix_count += len(matches)
            print(f"✓ 修复了 {len(matches)} 个匹配的路径")
    
    # 创建图表文件名映射
    chart_mapping = {
        # 根据内容匹配正确的图表文件
        '年度游客数量': 'chart_1_市场趋势_3.png',
        '游客来源地分布': 'chart_1_需求分布_2.png',
        '满意度与消费': 'chart_2_目的地分布_8.png',
        '居民职业分布': 'chart_3_人力结构_20.png',
        '政策环境匹配度评估': 'chart_3_政策匹配_26.png',
        '政策受益度评估': 'chart_3_政策受益_27.png',
        '政策风险评估': 'chart_3_社会风险_36.png',
        '技术成熟度评估': 'chart_3_技术成熟_28.png',
        '技术实施难度': 'chart_3_技术难度_29.png',
        '技术风险': 'chart_3_技术风险_30.png',
        '投资成本分析': 'chart_3_投资成本_31.png',
        '收益预测': 'chart_3_收益预测_32.png',
        '财务指标评估': 'chart_3_财务指标_33.png',
        '项目敏感性分析': 'chart_3_敏感性_34.png',
        '项目综合可行性评价': 'chart_3_综合评价_40.png',
        '社会效益分析': 'chart_3_社会效益_35.png',
        '社会风险分析': 'chart_3_社会风险_36.png'
    }
    
    # 使用更通用的方法修复剩余的错误路径
    for keyword, correct_filename in chart_mapping.items():
        # 查找包含关键词但路径错误的图表引用
        pattern = rf'!\[([^]]*{keyword}[^]]*)\]\((?!output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/)[^)]*\)'
        replacement = rf'![\1]({correct_path_prefix}{correct_filename})'
        
        matches = re.findall(pattern, content)
        if matches:
            content = re.sub(pattern, replacement, content)
            fix_count += len(matches)
            print(f"✓ 修复了 {len(matches)} 个包含'{keyword}'的图表链接")
    
    # 移除重复的图表引用
    # 查找连续的相同图表引用
    pattern = r'(!\[[^\]]*\]\([^)]*\)\n\n\*图[^*]*\*\n\n)\1+'
    content = re.sub(pattern, r'\1', content)
    
    # 修复破损的图表引用格式
    content = re.sub(r'!\[([^\]]*)\n\n!\[([^\]]*)\]\(([^)]*)\)', r'![\1\2](\3)', content)
    
    # 保存修复后的文档
    write_markdown_file(source_file, content)
    
    print(f"\n✅ 图表链接修复完成！")
    print(f"📊 总共修复了 {fix_count} 个图表链接")
    print(f"📄 文档已更新: {source_file}")
    
    return fix_count

def verify_chart_files():
    """验证图表文件是否存在"""
    chart_dir = "output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/"
    
    if not os.path.exists(chart_dir):
        print(f"❌ 图表目录不存在: {chart_dir}")
        return False
    
    chart_files = [f for f in os.listdir(chart_dir) if f.endswith('.png')]
    print(f"\n📁 图表目录: {chart_dir}")
    print(f"📊 找到 {len(chart_files)} 个图表文件:")
    
    for i, filename in enumerate(sorted(chart_files), 1):
        print(f"  {i:2d}. {filename}")
    
    return True

if __name__ == "__main__":
    try:
        # 验证图表文件
        if verify_chart_files():
            # 修复链接
            count = fix_chart_links()
            print(f"\n🎉 任务完成！成功修复了 {count} 个图表链接")
        else:
            print("❌ 请先确保图表文件存在")
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()
