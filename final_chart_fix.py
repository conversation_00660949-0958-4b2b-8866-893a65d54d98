#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re

def read_markdown_file(filepath):
    """读取markdown文件"""
    with open(filepath, 'r', encoding='utf-8') as f:
        return f.read()

def write_markdown_file(filepath, content):
    """写入markdown文件"""
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(content)

def final_fix_charts():
    """最终修复所有图表链接问题"""
    
    source_file = "markdown_report/01_北京通州区宋庄镇农旅数字化推广项目可行性研究报告.md"
    content = read_markdown_file(source_file)
    
    print("开始最终修复...")
    
    # 修复重复的标题
    content = re.sub(r'!\[游客满意度与消费金额关系满意度与消费金额关系\]', 
                     r'![满意度与消费金额关系分析]', content)
    
    # 修复所有错误的图表路径，统一使用正确的路径
    correct_base_path = "output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/"
    
    # 创建具体的图表映射
    chart_fixes = [
        # 修复年度游客数量图表
        (r'!\[年度游客数量变化趋势\]\(\.\/output\/charts\/png\/年度游客数量_柱状图\.png\)',
         f'![年度游客数量变化趋势]({correct_base_path}chart_1_市场趋势_3.png)'),
        
        # 修复游客来源地分布图表
        (r'!\[游客来源地分布\]\(\.\/output\/charts\/png\/游客来源地分布_饼图\.png\)',
         f'![游客来源地分布]({correct_base_path}chart_1_需求分布_2.png)'),
        
        # 修复居民职业分布图表
        (r'!\[宋庄镇居民职业分布\]\(\.\/output\/charts\/png\/居民职业分布_饼图\.png\)',
         f'![宋庄镇居民职业分布]({correct_base_path}chart_3_人力结构_20.png)'),
        
        # 修复所有农旅数字化推广项目路径
        (r'!\[([^\]]*)\]\(output\/charts\/农旅数字化推广项目\/([^)]*)\)',
         rf'![\1]({correct_base_path}chart_3_政策匹配_26.png)'),
        
        # 修复政策环境匹配度评估
        (r'!\[政策环境匹配度评估雷达图\]\(output\/charts\/农旅数字化推广项目\/政策环境匹配度评估_雷达图\.png\)',
         f'![政策环境匹配度评估雷达图]({correct_base_path}chart_3_政策匹配_26.png)'),
        
        # 修复政策受益度评估
        (r'!\[政策受益度评估条形图\]\(output\/charts\/农旅数字化推广项目\/政策受益度评估_条形图\.png\)',
         f'![政策受益度评估条形图]({correct_base_path}chart_3_政策受益_27.png)'),
        
        # 修复政策风险评估
        (r'!\[政策风险评估热力图\]\(output\/charts\/农旅数字化推广项目\/政策风险评估_热力图\.png\)',
         f'![政策风险评估热力图]({correct_base_path}chart_3_社会风险_36.png)'),
        
        # 修复技术成熟度评估
        (r'!\[技术成熟度评估雷达图\]\(output\/charts\/农旅数字化推广项目\/技术成熟度评估_雷达图\.png\)',
         f'![技术成熟度评估雷达图]({correct_base_path}chart_3_技术成熟_28.png)'),
        
        # 修复技术实施难度
        (r'!\[技术实施难度热力图\]\(output\/charts\/农旅数字化推广项目\/技术实施难度评估_热力图\.png\)',
         f'![技术实施难度热力图]({correct_base_path}chart_3_技术难度_29.png)'),
        
        # 修复技术风险散点图
        (r'!\[技术风险散点图\]\(output\/charts\/农旅数字化推广项目\/技术风险评估_散点图\.png\)',
         f'![技术风险散点图]({correct_base_path}chart_3_技术风险_30.png)'),
        
        # 修复投资成本分析
        (r'!\[投资成本分析柱状图\]\(output\/charts\/农旅数字化推广项目\/投资成本分析_柱状图\.png\)',
         f'![投资成本分析柱状图]({correct_base_path}chart_3_投资成本_31.png)'),
        
        # 修复收益预测
        (r'!\[收益预测柱状图\]\(output\/charts\/农旅数字化推广项目\/收益预测分析_柱状图\.png\)',
         f'![收益预测柱状图]({correct_base_path}chart_3_收益预测_32.png)'),
        
        # 修复财务指标评估
        (r'!\[财务指标评估组合图\]\(output\/charts\/农旅数字化推广项目\/财务指标评估_组合图\.png\)',
         f'![财务指标评估组合图]({correct_base_path}chart_3_财务指标_33.png)'),
        
        # 修复敏感性分析
        (r'!\[项目敏感性分析条形图\]\(output\/charts\/农旅数字化推广项目\/项目敏感性分析_条形图\.png\)',
         f'![项目敏感性分析条形图]({correct_base_path}chart_3_敏感性_34.png)'),
        
        # 修复综合可行性评价
        (r'!\[项目综合可行性评价组合图\]\(output\/charts\/农旅数字化推广项目\/项目综合可行性评价_组合图\.png\)',
         f'![项目综合可行性评价组合图]({correct_base_path}chart_3_综合评价_40.png)'),
        
        # 修复社会效益分析
        (r'!\[社会效益分析组合图\]\(output\/charts\/农旅数字化推广项目\/社会效益分析_组合图\.png\)',
         f'![社会效益分析组合图]({correct_base_path}chart_3_社会效益_35.png)'),
        
        # 修复社会风险分析
        (r'!\[社会风险分析气泡图\]\(output\/charts\/农旅数字化推广项目\/社会风险分析_气泡图\.png\)',
         f'![社会风险分析气泡图]({correct_base_path}chart_3_社会风险_36.png)'),
    ]
    
    fix_count = 0
    for pattern, replacement in chart_fixes:
        matches = re.findall(pattern, content)
        if matches:
            content = re.sub(pattern, replacement, content)
            fix_count += len(matches)
            print(f"✓ 修复了 {len(matches)} 个图表链接")
    
    # 移除重复的图表引用（连续出现的相同图表）
    # 查找并移除重复的艺术家分布图表
    pattern = r'(!\[艺术家资源分布与结构分析\]\([^)]*\)\n\n\*图3-5 艺术家资源分布与结构分析\*\n\n)(?=.*?\1)'
    content = re.sub(pattern, '', content, flags=re.DOTALL)
    
    # 移除重复的环境效益图表
    pattern = r'(!\[环境效益分析与生态价值\]\([^)]*\)\n\n\*图3-29 环境效益分析与生态价值\*\n\n)(?=.*?\1)'
    content = re.sub(pattern, '', content, flags=re.DOTALL)
    
    # 保存修复后的文档
    write_markdown_file(source_file, content)
    
    print(f"\n✅ 最终修复完成！")
    print(f"📊 总共修复了 {fix_count} 个图表链接")
    print(f"📄 文档已更新: {source_file}")
    
    return fix_count

def verify_all_charts():
    """验证所有图表链接是否正确"""
    source_file = "markdown_report/01_北京通州区宋庄镇农旅数字化推广项目可行性研究报告.md"
    content = read_markdown_file(source_file)
    
    # 查找所有图表引用
    chart_pattern = r'!\[([^\]]*)\]\(([^)]*\.png)\)'
    matches = re.findall(chart_pattern, content)
    
    print(f"\n📊 文档中共有 {len(matches)} 个图表引用:")
    
    correct_path = "output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/"
    correct_count = 0
    error_count = 0
    
    for i, (title, path) in enumerate(matches, 1):
        if path.startswith(correct_path):
            print(f"  ✓ {i:2d}. {title}")
            correct_count += 1
        else:
            print(f"  ❌ {i:2d}. {title} -> {path}")
            error_count += 1
    
    print(f"\n📈 统计结果:")
    print(f"  ✅ 正确路径: {correct_count} 个")
    print(f"  ❌ 错误路径: {error_count} 个")
    
    return error_count == 0

if __name__ == "__main__":
    try:
        # 最终修复
        count = final_fix_charts()
        
        # 验证结果
        if verify_all_charts():
            print(f"\n🎉 所有图表链接修复完成！")
        else:
            print(f"\n⚠️  仍有部分图表链接需要手动检查")
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()
