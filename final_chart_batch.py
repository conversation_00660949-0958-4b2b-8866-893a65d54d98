#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import matplotlib
matplotlib.use('Agg')

import numpy as np
import matplotlib.pyplot as plt
import os
import warnings
warnings.filterwarnings('ignore')

plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False

# 配色方案
COLORS = ['#FF9AA2', '#FFB7B2', '#FFDAC1', '#E2F0CB', '#B5EAD7', '#C7CEEA']
COLORS2 = ['#A8E6CF', '#DCEDC1', '#FFD3A5', '#FFA8A8', '#B5B5EA', '#F8BBD9']
COLORS3 = ['#87CEEB', '#98FB98', '#F0E68C', '#FFA07A', '#DDA0DD', '#F5DEB3']

def create_output_dir():
    output_dir = "output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/"
    os.makedirs(output_dir, exist_ok=True)
    return output_dir

def create_simple_chart(chart_type, title, filename, data=None):
    """创建简单图表的通用函数"""
    fig, ax = plt.subplots(figsize=(10, 6))
    
    if chart_type == 'bar':
        categories = data.get('categories', ['A', 'B', 'C', 'D', 'E'])
        values = data.get('values', [20, 35, 30, 25, 40])
        bars = ax.bar(categories, values, color=COLORS[:len(categories)], alpha=0.8)
        ax.set_ylabel('Value')
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                    f'{value}', ha='center', va='bottom', fontweight='bold')
    
    elif chart_type == 'line':
        x_data = data.get('x', list(range(2020, 2026)))
        y_data = data.get('y', [10, 15, 22, 28, 35, 42])
        ax.plot(x_data, y_data, marker='o', linewidth=3, color=COLORS[0], markersize=8)
        ax.fill_between(x_data, y_data, alpha=0.3, color=COLORS[0])
        ax.set_xlabel('Year')
        ax.set_ylabel('Value')
        for x, y in zip(x_data, y_data):
            ax.annotate(f'{y}', (x, y), textcoords="offset points",
                       xytext=(0,10), ha='center', fontsize=9)
    
    elif chart_type == 'pie':
        labels = data.get('labels', ['Category A', 'Category B', 'Category C', 'Category D'])
        sizes = data.get('sizes', [30, 25, 25, 20])
        ax.pie(sizes, labels=labels, colors=COLORS[:len(labels)], autopct='%1.1f%%', startangle=90)
    
    elif chart_type == 'radar':
        categories = data.get('categories', ['Aspect A', 'Aspect B', 'Aspect C', 'Aspect D', 'Aspect E'])
        values = data.get('values', [4.2, 3.8, 4.5, 3.9, 4.1])
        
        fig, ax = plt.subplots(figsize=(8, 8), subplot_kw=dict(projection='polar'))
        angles = np.linspace(0, 2*np.pi, len(categories), endpoint=False).tolist()
        values += values[:1]
        angles += angles[:1]
        
        ax.plot(angles, values, 'o-', linewidth=3, color=COLORS[0], markersize=8)
        ax.fill(angles, values, alpha=0.25, color=COLORS[0])
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(categories)
        ax.set_ylim(0, 5)
        ax.grid(True)
    
    ax.set_title(title, fontsize=14, pad=20)
    ax.grid(True, alpha=0.3)
    plt.tight_layout()
    return fig

def generate_final_batch():
    """生成最终批次的图表"""
    output_dir = create_output_dir()
    chart_count = 0
    
    # 定义图表数据
    charts_data = [
        # 图表15-20: 基础设施与配套
        {
            'type': 'radar',
            'title': 'Infrastructure Assessment',
            'filename': 'chart_3_基础设施_17.png',
            'data': {
                'categories': ['Transportation', 'Communication', 'Utilities', 'Public Services', 'Safety'],
                'values': [4.2, 3.8, 4.0, 3.5, 4.1]
            }
        },
        {
            'type': 'bar',
            'title': 'Digital Infrastructure Status',
            'filename': 'chart_3_数字基础_18.png',
            'data': {
                'categories': ['Network Coverage', 'Bandwidth Speed', '5G Stations', 'IoT Devices', 'Smart Systems'],
                'values': [85, 78, 45, 32, 28]
            }
        },
        {
            'type': 'line',
            'title': 'Public Service Development Trend',
            'filename': 'chart_3_公共服务_19.png',
            'data': {
                'x': list(range(2020, 2026)),
                'y': [65, 70, 75, 82, 88, 92]
            }
        },
        
        # 图表21-25: 人力资源
        {
            'type': 'pie',
            'title': 'Human Resource Structure',
            'filename': 'chart_3_人力结构_20.png',
            'data': {
                'labels': ['Young (20-35)', 'Middle-aged (36-50)', 'Senior (51-65)', 'Elderly (65+)'],
                'sizes': [35, 40, 20, 5]
            }
        },
        {
            'type': 'radar',
            'title': 'Digital Literacy Assessment',
            'filename': 'chart_3_数字素养_21.png',
            'data': {
                'categories': ['Basic Skills', 'Internet Usage', 'Mobile Apps', 'Digital Payment', 'Online Learning'],
                'values': [3.2, 3.8, 4.1, 4.5, 2.9]
            }
        },
        {
            'type': 'bar',
            'title': 'Talent Gap Analysis',
            'filename': 'chart_3_人才缺口_22.png',
            'data': {
                'categories': ['Tech Specialists', 'Marketing', 'Management', 'Operations', 'Creative'],
                'values': [15, 8, 5, 12, 10]
            }
        },
        
        # 图表26-30: 市场需求
        {
            'type': 'line',
            'title': 'Urban Leisure Demand Trend',
            'filename': 'chart_3_休闲需求_23.png',
            'data': {
                'x': list(range(2020, 2026)),
                'y': [68, 72, 78, 85, 91, 96]
            }
        },
        {
            'type': 'pie',
            'title': 'Target Customer Profile',
            'filename': 'chart_3_客户画像_24.png',
            'data': {
                'labels': ['Urban Families', 'Young Groups', 'Middle-aged', 'Educational'],
                'sizes': [45, 25, 20, 10]
            }
        },
        {
            'type': 'bar',
            'title': 'Market Competition Analysis',
            'filename': 'chart_3_竞争格局_25.png',
            'data': {
                'categories': ['Competitor A', 'Competitor B', 'Competitor C', 'Competitor D', 'Songzhuang'],
                'values': [75, 68, 82, 59, 72]
            }
        },
        
        # 图表31-35: 技术与经济
        {
            'type': 'radar',
            'title': 'Technology Maturity Assessment',
            'filename': 'chart_3_技术成熟_28.png',
            'data': {
                'categories': ['IoT', 'Big Data', 'AI', 'VR/AR', 'Blockchain', 'Cloud Computing'],
                'values': [4.2, 3.8, 3.5, 3.2, 2.8, 4.5]
            }
        },
        {
            'type': 'bar',
            'title': 'Investment Cost Breakdown',
            'filename': 'chart_3_投资成本_31.png',
            'data': {
                'categories': ['Infrastructure', 'Technology', 'Marketing', 'Operations', 'Maintenance'],
                'values': [1200, 800, 300, 200, 80]
            }
        },
        {
            'type': 'line',
            'title': 'Revenue Forecast',
            'filename': 'chart_3_收益预测_32.png',
            'data': {
                'x': list(range(2025, 2030)),
                'y': [200, 450, 680, 845, 920]
            }
        },
        
        # 图表36-40: 社会与环境
        {
            'type': 'radar',
            'title': 'Social Benefits Assessment',
            'filename': 'chart_3_社会效益_35.png',
            'data': {
                'categories': ['Employment', 'Income Growth', 'Cultural Heritage', 'Education', 'Community Development'],
                'values': [4.3, 4.1, 4.5, 3.9, 4.2]
            }
        },
        {
            'type': 'bar',
            'title': 'Social Acceptance Survey',
            'filename': 'chart_3_社会接受_37.png',
            'data': {
                'categories': ['Local Residents', 'Farmers', 'Artists', 'Government', 'Investors'],
                'values': [78, 82, 88, 92, 85]
            }
        },
        {
            'type': 'pie',
            'title': 'Environmental Impact Distribution',
            'filename': 'chart_3_环境影响_38.png',
            'data': {
                'labels': ['Positive Impact', 'Neutral', 'Minor Negative', 'Manageable Risk'],
                'sizes': [60, 25, 10, 5]
            }
        },
        
        # 图表41-45: 综合评价
        {
            'type': 'radar',
            'title': 'Comprehensive Feasibility Assessment',
            'filename': 'chart_3_综合评价_40.png',
            'data': {
                'categories': ['Market', 'Technology', 'Economic', 'Social', 'Environmental', 'Policy'],
                'values': [4.1, 4.0, 4.3, 4.3, 4.5, 4.7]
            }
        },
        {
            'type': 'line',
            'title': 'Project Implementation Timeline',
            'filename': 'chart_4_实施路线_41.png',
            'data': {
                'x': list(range(2025, 2028)),
                'y': [25, 65, 100]
            }
        },
        {
            'type': 'bar',
            'title': 'Development Prospects',
            'filename': 'chart_4_发展前景_42.png',
            'data': {
                'categories': ['Industry Upgrade', 'Brand Enhancement', 'Demo Effect', 'Economic Growth', 'Social Impact'],
                'values': [85, 78, 82, 88, 80]
            }
        }
    ]
    
    # 生成所有图表
    for chart_info in charts_data:
        try:
            fig = create_simple_chart(
                chart_info['type'],
                chart_info['title'],
                chart_info['filename'],
                chart_info['data']
            )
            fig.savefig(os.path.join(output_dir, chart_info['filename']), 
                       dpi=300, bbox_inches='tight', facecolor='white')
            plt.close(fig)
            chart_count += 1
            print(f"✓ Chart {chart_count + 14}: {chart_info['title']}")
        except Exception as e:
            print(f"❌ Error generating {chart_info['title']}: {e}")
    
    return chart_count

if __name__ == "__main__":
    try:
        count = generate_final_batch()
        print(f"\n✅ Successfully generated {count} final charts!")
        
        # 统计总数
        output_dir = create_output_dir()
        total_charts = len([f for f in os.listdir(output_dir) if f.endswith('.png')])
        print(f"📊 Total charts generated: {total_charts}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
