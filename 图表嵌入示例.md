# 图表嵌入Markdown示例

## 介绍

本文档展示如何在Markdown文档中嵌入生成的PNG格式图表，以便在最终的PDF报告中使用。

## 图表嵌入方法

在Markdown中嵌入图片的基本语法是：

```markdown
![图片描述](图片路径)
```

对于我们生成的图表，可以使用相对路径引用。以下是几个示例：

## 示例图表

### 1. 宋庄镇居民职业分布饼图

![宋庄镇居民职业分布饼图](output/charts/png/居民职业分布_饼图.png)

### 2. 不同群体数字素养水平对比条形图

![不同群体数字素养水平对比条形图](output/charts/png/数字素养水平_条形图.png)

### 3. 人才需求与缺口雷达图

![人才需求与缺口雷达图](output/charts/png/人才需求与缺口_雷达图.png)

### 4. 游客来源地分布饼图

![游客来源地分布饼图](output/charts/png/游客来源地分布_饼图.png)

### 5. 不同客户群体活动偏好热力图

![不同客户群体活动偏好热力图](output/charts/png/客户活动偏好_热力图.png)

## 图表尺寸调整

如果需要调整图表在PDF中的显示尺寸，可以使用HTML标签：

```markdown
<img src="output/charts/png/居民职业分布_饼图.png" width="500" />
```

效果如下：

<img src="output/charts/png/居民职业分布_饼图.png" width="500" />

## 图表并排显示

如果需要并排显示多个图表，可以使用HTML表格：

```markdown
<table>
  <tr>
    <td><img src="output/charts/png/居民职业分布_饼图.png" width="350" /></td>
    <td><img src="output/charts/png/游客来源地分布_饼图.png" width="350" /></td>
  </tr>
  <tr>
    <td>居民职业分布</td>
    <td>游客来源地分布</td>
  </tr>
</table>
```

效果如下：

<table>
  <tr>
    <td><img src="output/charts/png/居民职业分布_饼图.png" width="350" /></td>
    <td><img src="output/charts/png/游客来源地分布_饼图.png" width="350" /></td>
  </tr>
  <tr>
    <td>居民职业分布</td>
    <td>游客来源地分布</td>
  </tr>
</table>

## 注意事项

1. 确保图片路径正确，可以使用相对路径或绝对路径
2. 在生成PDF时，确保图片文件可以被访问
3. 如果使用HTML标签调整图片大小，确保Markdown转PDF工具支持HTML标签

## 所有生成的图表

以下是所有已生成的图表列表，可以根据需要在报告中使用：

1. 居民职业分布_饼图.png
2. 数字素养水平_条形图.png
3. 人才需求与缺口_雷达图.png
4. 游客来源地分布_饼图.png
5. 客户活动偏好_热力图.png
6. 数字化服务接受度_条形图.png
7. 消费决策因素_堆叠条形图.png
8. 市场竞争格局_气泡图.png
9. 年度游客数量_柱状图.png
10. 满意度与消费关系_散点图.png