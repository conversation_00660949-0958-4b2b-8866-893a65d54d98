#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import matplotlib
matplotlib.use('Agg')  # 非交互式后端

import numpy as np
import matplotlib.pyplot as plt
import os
import warnings
warnings.filterwarnings('ignore')

# 设置字体 - 使用系统默认字体避免中文问题
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False

# 小清新配色
COLORS = ['#FF9AA2', '#FFB7B2', '#FFDAC1', '#E2F0CB', '#B5EAD7', '#C7CEEA']

def create_output_dir():
    output_dir = "output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/"
    os.makedirs(output_dir, exist_ok=True)
    return output_dir

def generate_charts():
    output_dir = create_output_dir()
    print(f"输出目录: {output_dir}")
    
    # 图表1: 基本情况柱状图
    fig, ax = plt.subplots(figsize=(10, 6))
    categories = ['Total Area(km²)', 'Population(10k)', 'Farmland(10k mu)', 'Artists(k)']
    values = [40, 5, 1.5, 5]
    bars = ax.bar(categories, values, color=COLORS[:4], alpha=0.8, edgecolor='white', linewidth=2)
    ax.set_title('Songzhuang Town Basic Information Overview', fontsize=14, pad=20)
    ax.set_ylabel('Value')
    
    for bar, value in zip(bars, values):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + height*0.02,
                f'{value}', ha='center', va='bottom', fontweight='bold')
    
    ax.grid(True, alpha=0.3, axis='y')
    plt.tight_layout()
    fig.savefig(os.path.join(output_dir, 'chart_1_基本情况_1.png'), 
                dpi=300, bbox_inches='tight', facecolor='white')
    plt.close(fig)
    print("✓ Chart 1: Basic Information")
    
    # 图表2: 需求分布饼图
    fig, ax = plt.subplots(figsize=(10, 8))
    needs = ['Family Farm Experience', 'Cultural Activities', 'Healthy Food', 'Nature Experience', 'Education', 'Others']
    percentages = [28, 25, 20, 15, 8, 4]
    
    wedges, texts, autotexts = ax.pie(percentages, labels=needs, colors=COLORS,
                                     autopct='%1.1f%%', startangle=90, textprops={'fontsize': 10})
    ax.set_title('Weekend Leisure Demand Distribution of Urban Families', fontsize=14, pad=20)
    
    plt.tight_layout()
    fig.savefig(os.path.join(output_dir, 'chart_1_需求分布_2.png'), 
                dpi=300, bbox_inches='tight', facecolor='white')
    plt.close(fig)
    print("✓ Chart 2: Demand Distribution")
    
    # 图表3: 市场趋势线图
    fig, ax = plt.subplots(figsize=(12, 6))
    years = list(range(2018, 2026))
    market_size = [1200, 1450, 1750, 2100, 2500, 3000, 3600, 4300]
    
    ax.plot(years, market_size, marker='o', linewidth=3, color=COLORS[0], markersize=8)
    ax.fill_between(years, market_size, alpha=0.3, color=COLORS[0])
    ax.set_title('Digital Agriculture Market Size Growth Trend', fontsize=14, pad=20)
    ax.set_xlabel('Year')
    ax.set_ylabel('Market Size (100M RMB)')
    ax.grid(True, alpha=0.3)
    
    for year, size in zip(years, market_size):
        ax.annotate(f'{size}', (year, size), textcoords="offset points",
                   xytext=(0,10), ha='center', fontsize=9, fontweight='bold')
    
    plt.tight_layout()
    fig.savefig(os.path.join(output_dir, 'chart_1_市场趋势_3.png'), 
                dpi=300, bbox_inches='tight', facecolor='white')
    plt.close(fig)
    print("✓ Chart 3: Market Trend")
    
    # 图表4: 投资回报组合图
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 8))
    
    # 投资构成饼图
    investment_types = ['Construction', 'Operation', 'Maintenance']
    investment_amounts = [1800, 580, 200]
    ax1.pie(investment_amounts, labels=investment_types, colors=COLORS[:3], 
            autopct='%1.1f%%', startangle=90)
    ax1.set_title('Investment Composition (10k RMB)')
    
    # 年度收益对比
    years = list(range(2025, 2030))
    revenues = [200, 450, 680, 845, 920]
    costs = [150, 280, 380, 475, 520]
    
    x = np.arange(len(years))
    width = 0.35
    ax2.bar(x - width/2, revenues, width, label='Revenue', color=COLORS[1], alpha=0.8)
    ax2.bar(x + width/2, costs, width, label='Cost', color=COLORS[2], alpha=0.8)
    ax2.set_xlabel('Year')
    ax2.set_ylabel('Amount (10k RMB)')
    ax2.set_title('Annual Revenue Forecast')
    ax2.set_xticks(x)
    ax2.set_xticklabels(years)
    ax2.legend()
    ax2.grid(True, alpha=0.3, axis='y')
    
    # 投资回收期
    profits = [r - c for r, c in zip(revenues, costs)]
    cumulative_profit = np.cumsum(profits)
    initial_investment = sum(investment_amounts)
    
    ax3.plot(years, cumulative_profit, marker='s', linewidth=3, color=COLORS[3], markersize=8)
    ax3.axhline(y=initial_investment, color='red', linestyle='--', 
                label=f'Initial Investment ({initial_investment})')
    ax3.fill_between(years, cumulative_profit, alpha=0.3, color=COLORS[3])
    ax3.set_title('Investment Payback Analysis')
    ax3.set_xlabel('Year')
    ax3.set_ylabel('Cumulative Profit (10k RMB)')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 财务指标
    indicators = ['NPV\n(10k)', 'IRR\n(%)', 'Payback\n(Years)', 'ROI\n(%)']
    values = [1250, 28.5, 3.2, 25.0]
    bars = ax4.bar(indicators, values, color=COLORS[:4], alpha=0.8, edgecolor='white', linewidth=2)
    ax4.set_title('Key Financial Indicators')
    ax4.set_ylabel('Value')
    
    for bar, value in zip(bars, values):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height + height*0.02,
                f'{value}', ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    fig.savefig(os.path.join(output_dir, 'chart_1_投资回报_5.png'), 
                dpi=300, bbox_inches='tight', facecolor='white')
    plt.close(fig)
    print("✓ Chart 4: Investment Return Analysis")
    
    # 图表5: 政策支持雷达图
    fig, ax = plt.subplots(figsize=(10, 8), subplot_kw=dict(projection='polar'))
    
    policies = ['Digital Village', 'Rural Revitalization', 'Culture-Tourism', 'Digital Economy', 'Modern Agriculture', 'Innovation Drive']
    scores = [4.8, 4.6, 4.5, 4.7, 4.2, 4.3]
    
    angles = np.linspace(0, 2*np.pi, len(policies), endpoint=False).tolist()
    scores += scores[:1]  # 闭合
    angles += angles[:1]
    
    ax.plot(angles, scores, 'o-', linewidth=3, color=COLORS[0], markersize=8)
    ax.fill(angles, scores, alpha=0.25, color=COLORS[0])
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(policies, fontsize=10)
    ax.set_ylim(0, 5)
    ax.set_yticks([1, 2, 3, 4, 5])
    ax.set_title('National Policy Support Assessment\n(Max Score: 5)', fontsize=14, pad=30)
    ax.grid(True)
    
    # 添加分数标签
    for angle, score in zip(angles[:-1], scores[:-1]):
        ax.text(angle, score + 0.1, f'{score}', ha='center', va='center', 
                fontweight='bold', fontsize=9)
    
    plt.tight_layout()
    fig.savefig(os.path.join(output_dir, 'chart_2_政策支持_6.png'), 
                dpi=300, bbox_inches='tight', facecolor='white')
    plt.close(fig)
    print("✓ Chart 5: Policy Support Radar")
    
    return 5

if __name__ == "__main__":
    try:
        count = generate_charts()
        print(f"\n✅ Successfully generated {count} charts!")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
