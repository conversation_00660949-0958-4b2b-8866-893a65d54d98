#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import matplotlib
matplotlib.use('Agg')

import numpy as np
import matplotlib.pyplot as plt
import os
import warnings
warnings.filterwarnings('ignore')

plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False

# 配色方案
COLORS = ['#FF9AA2', '#FFB7B2', '#FFDAC1', '#E2F0CB', '#B5EAD7', '#C7CEEA']
COLORS2 = ['#A8E6CF', '#DCEDC1', '#FFD3A5', '#FFA8A8', '#B5B5EA', '#F8BBD9']
COLORS3 = ['#87CEEB', '#98FB98', '#F0E68C', '#FFA07A', '#DDA0DD', '#F5DEB3']

def create_output_dir():
    output_dir = "output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/"
    os.makedirs(output_dir, exist_ok=True)
    return output_dir

def generate_chart_10():
    """气候条件适宜性"""
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 8))
    
    # 月度气温
    months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
    temp_avg = [-4, -1, 6, 14, 20, 25, 27, 26, 21, 14, 5, -2]
    temp_max = [2, 5, 12, 21, 27, 31, 33, 32, 27, 20, 11, 4]
    temp_min = [-10, -7, 0, 7, 13, 19, 21, 20, 15, 8, -1, -8]
    
    ax1.plot(months, temp_avg, marker='o', linewidth=2, color=COLORS[0], label='Average')
    ax1.fill_between(months, temp_min, temp_max, alpha=0.3, color=COLORS[0])
    ax1.set_title('Monthly Temperature (°C)')
    ax1.set_ylabel('Temperature (°C)')
    ax1.tick_params(axis='x', rotation=45)
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    
    # 降水量
    precipitation = [3, 6, 9, 26, 29, 71, 176, 182, 49, 19, 8, 3]
    ax2.bar(months, precipitation, color=COLORS[1], alpha=0.8)
    ax2.set_title('Monthly Precipitation (mm)')
    ax2.set_ylabel('Precipitation (mm)')
    ax2.tick_params(axis='x', rotation=45)
    ax2.grid(True, alpha=0.3, axis='y')
    
    # 光照时数
    sunshine = [180, 190, 220, 240, 260, 250, 230, 220, 200, 190, 170, 160]
    ax3.plot(months, sunshine, marker='s', linewidth=3, color=COLORS[2], markersize=6)
    ax3.fill_between(months, sunshine, alpha=0.3, color=COLORS[2])
    ax3.set_title('Monthly Sunshine Hours')
    ax3.set_ylabel('Hours')
    ax3.tick_params(axis='x', rotation=45)
    ax3.grid(True, alpha=0.3)
    
    # 适宜性评分
    activities = ['Farming', 'Tourism', 'Outdoor Events', 'Photography', 'Education']
    suitability = [4.2, 4.5, 4.0, 4.3, 4.1]
    bars = ax4.bar(activities, suitability, color=COLORS3[:5], alpha=0.8)
    ax4.set_title('Climate Suitability for Activities')
    ax4.set_ylabel('Suitability Score (Max 5)')
    ax4.tick_params(axis='x', rotation=45)
    ax4.set_ylim(0, 5)
    ax4.grid(True, alpha=0.3, axis='y')
    
    for bar, score in zip(bars, suitability):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height + 0.05,
                f'{score}', ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    return fig

def generate_chart_11():
    """土地资源利用现状"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))
    
    # 土地利用饼图
    land_types = ['Farmland', 'Construction', 'Ecological', 'Water Bodies', 'Transportation', 'Others']
    land_areas = [37.5, 25.0, 20.0, 8.5, 6.0, 3.0]  # percentage
    
    wedges, texts, autotexts = ax1.pie(land_areas, labels=land_types, colors=COLORS,
                                      autopct='%1.1f%%', startangle=90)
    ax1.set_title('Land Use Distribution (%)')
    
    # 农业用地细分
    farm_types = ['Rice Fields', 'Vegetable Plots', 'Fruit Orchards', 'Greenhouse', 'Experimental Fields']
    farm_areas = [45, 30, 15, 7, 3]  # percentage of farmland
    
    bars = ax2.bar(farm_types, farm_areas, color=COLORS2[:5], alpha=0.8)
    ax2.set_title('Agricultural Land Breakdown (%)')
    ax2.set_ylabel('Percentage of Farmland')
    ax2.tick_params(axis='x', rotation=45)
    ax2.grid(True, alpha=0.3, axis='y')
    
    for bar, area in zip(bars, farm_areas):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{area}%', ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    return fig

def generate_chart_12():
    """艺术发展历程时间轴"""
    fig, ax = plt.subplots(figsize=(12, 6))
    
    # 时间轴数据
    years = [1994, 1998, 2003, 2008, 2012, 2018, 2023]
    events = ['First Artists', 'Art Village', 'Art Festival', 'Government Support', 
              'International Recognition', 'Digital Integration', 'Future Vision']
    artist_counts = [50, 200, 800, 2000, 3500, 4500, 5000]
    
    # 创建时间轴
    ax.plot(years, artist_counts, marker='o', linewidth=3, color=COLORS[0], markersize=10)
    ax.fill_between(years, artist_counts, alpha=0.3, color=COLORS[0])
    
    # 添加事件标注
    for i, (year, event, count) in enumerate(zip(years, events, artist_counts)):
        ax.annotate(f'{event}\n({count} artists)', 
                   (year, count), 
                   xytext=(0, 20 if i % 2 == 0 else -40),
                   textcoords='offset points',
                   ha='center',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor=COLORS[i % len(COLORS)], alpha=0.7),
                   arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))
    
    ax.set_xlabel('Year')
    ax.set_ylabel('Number of Artists')
    ax.set_title('Songzhuang Art District Development Timeline')
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    return fig

def generate_chart_13():
    """艺术家资源分布树状图"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))
    
    # 艺术领域分布
    art_fields = ['Painting', 'Sculpture', 'Installation', 'Photography', 'Digital Art', 'Design']
    artist_numbers = [2000, 800, 600, 700, 500, 400]
    
    # 创建树状图效果
    colors = COLORS[:len(art_fields)]
    bars = ax1.barh(art_fields, artist_numbers, color=colors, alpha=0.8)
    ax1.set_title('Artist Distribution by Field')
    ax1.set_xlabel('Number of Artists')
    
    for bar, number in zip(bars, artist_numbers):
        width = bar.get_width()
        ax1.text(width + 20, bar.get_y() + bar.get_height()/2.,
                f'{number}', ha='left', va='center', fontweight='bold')
    
    # 年龄结构
    age_groups = ['20-30', '31-40', '41-50', '51-60', '60+']
    age_percentages = [25, 35, 25, 12, 3]
    
    wedges, texts, autotexts = ax2.pie(age_percentages, labels=age_groups, colors=COLORS2[:5],
                                      autopct='%1.1f%%', startangle=90)
    ax2.set_title('Artist Age Distribution')
    
    plt.tight_layout()
    return fig

def generate_chart_14():
    """艺术机构类型环形图"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))
    
    # 机构类型环形图
    institution_types = ['Galleries', 'Studios', 'Museums', 'Art Centers', 'Educational', 'Commercial']
    institution_counts = [300, 400, 50, 80, 70, 100]
    
    # 创建环形图
    wedges, texts, autotexts = ax1.pie(institution_counts, labels=institution_types, 
                                      colors=COLORS, autopct='%1.1f%%', startangle=90,
                                      wedgeprops=dict(width=0.5))
    ax1.set_title('Art Institution Types Distribution')
    
    # 机构规模分布
    sizes = ['Small (1-5 people)', 'Medium (6-20)', 'Large (21-50)', 'Very Large (50+)']
    size_counts = [600, 300, 80, 20]
    
    bars = ax2.bar(sizes, size_counts, color=COLORS2[:4], alpha=0.8)
    ax2.set_title('Institution Size Distribution')
    ax2.set_ylabel('Number of Institutions')
    ax2.tick_params(axis='x', rotation=45)
    ax2.grid(True, alpha=0.3, axis='y')
    
    for bar, count in zip(bars, size_counts):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 5,
                f'{count}', ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    return fig

def generate_mass_charts():
    """批量生成图表"""
    output_dir = create_output_dir()
    chart_count = 0
    
    charts = [
        (generate_chart_10, 'chart_3_气候条件_12.png', 'Climate Conditions'),
        (generate_chart_11, 'chart_3_土地利用_13.png', 'Land Use Status'),
        (generate_chart_12, 'chart_3_艺术发展_14.png', 'Art Development Timeline'),
        (generate_chart_13, 'chart_3_艺术家分布_15.png', 'Artist Distribution'),
        (generate_chart_14, 'chart_3_机构类型_16.png', 'Institution Types')
    ]
    
    for chart_func, filename, description in charts:
        try:
            fig = chart_func()
            fig.savefig(os.path.join(output_dir, filename), dpi=300, bbox_inches='tight', facecolor='white')
            plt.close(fig)
            chart_count += 1
            print(f"✓ Chart {chart_count + 9}: {description}")
        except Exception as e:
            print(f"❌ Error generating {description}: {e}")
    
    return chart_count

if __name__ == "__main__":
    try:
        count = generate_mass_charts()
        print(f"\n✅ Successfully generated {count} additional charts!")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
