#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import matplotlib
matplotlib.use('Agg')

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 小清新亮色配色方案
COLORS = ['#FF9AA2', '#FFB7B2', '#FFDAC1', '#E2F0CB', '#B5EAD7', '#C7CEEA']
COLORS2 = ['#A8E6CF', '#DCEDC1', '#FFD3A5', '#FFA8A8', '#B5B5EA', '#F8BBD9']
COLORS3 = ['#87CEEB', '#98FB98', '#F0E68C', '#FFA07A', '#DDA0DD', '#F5DEB3']

def create_output_dir():
    output_dir = "output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/"
    os.makedirs(output_dir, exist_ok=True)
    return output_dir

def set_chart_style():
    """设置图表样式"""
    sns.set_style("whitegrid", {
        'axes.grid': True,
        'axes.edgecolor': '#E0E0E0',
        'axes.linewidth': 0.8,
        'grid.color': '#F0F0F0',
        'grid.linewidth': 0.5
    })
    plt.rcParams.update({
        'figure.facecolor': 'white',
        'axes.facecolor': 'white',
        'savefig.facecolor': 'white',
        'font.size': 10,
        'axes.titlesize': 14,
        'axes.labelsize': 12
    })

def create_complex_chart(chart_type, title, filename, chinese_data):
    """创建复杂的中文图表"""
    if chart_type == "项目实施甘特图":
        fig, ax = plt.subplots(figsize=(14, 8))
        
        tasks = ['项目规划阶段', '基础设施建设', '系统开发阶段', '试点运营阶段', 
                '正式运营阶段', '优化提升阶段']
        start_dates = [0, 3, 8, 12, 15, 20]
        durations = [3, 5, 4, 3, 5, 4]
        colors = COLORS[:len(tasks)]
        
        for i, (task, start, duration, color) in enumerate(zip(tasks, start_dates, durations, colors)):
            ax.barh(i, duration, left=start, height=0.6, color=color, alpha=0.8,
                    edgecolor='white', linewidth=2)
            ax.text(start + duration/2, i, f'{task}\n({duration}个月)', 
                    ha='center', va='center', fontweight='bold', fontsize=10)
        
        ax.set_yticks(range(len(tasks)))
        ax.set_yticklabels(tasks)
        ax.set_xlabel('时间(月)')
        ax.set_title(title, fontsize=16, pad=20)
        ax.grid(True, alpha=0.3, axis='x')
        
        # 添加里程碑
        milestones = [6, 12, 18, 24]
        milestone_labels = ['第一阶段完成', '第二阶段完成', '第三阶段完成', '项目全面完成']
        
        for milestone, label in zip(milestones, milestone_labels):
            ax.axvline(x=milestone, color='red', linestyle='--', alpha=0.7, linewidth=2)
            ax.text(milestone, len(tasks), label, rotation=90, ha='right', va='bottom',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor='yellow', alpha=0.7))
    
    elif chart_type == "政策支持雷达图":
        fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))
        
        policies = ['数字乡村战略', '乡村振兴战略', '文旅融合政策', '数字经济政策', 
                   '农业现代化政策', '创新驱动发展', '绿色发展理念', '城乡融合发展']
        scores = [4.8, 4.6, 4.5, 4.7, 4.2, 4.3, 4.4, 4.5]
        
        angles = np.linspace(0, 2*np.pi, len(policies), endpoint=False).tolist()
        scores += scores[:1]
        angles += angles[:1]
        
        ax.plot(angles, scores, 'o-', linewidth=4, color=COLORS[0], markersize=10)
        ax.fill(angles, scores, alpha=0.25, color=COLORS[0])
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(policies, fontsize=12)
        ax.set_ylim(0, 5)
        ax.set_title(title, fontsize=16, pad=40)
        ax.grid(True)
        
        # 添加分数标签
        for angle, score, policy in zip(angles[:-1], scores[:-1], policies):
            ax.text(angle, score + 0.15, f'{score}', ha='center', va='center', 
                    fontweight='bold', fontsize=11,
                    bbox=dict(boxstyle="round,pad=0.2", facecolor='white', alpha=0.8))
    
    elif chart_type == "投资回报组合分析":
        fig = plt.figure(figsize=(16, 10))
        gs = fig.add_gridspec(2, 2, hspace=0.3, wspace=0.3)
        
        # 投资构成饼图
        ax1 = fig.add_subplot(gs[0, 0])
        investment_types = ['基础设施建设', '技术系统开发', '运营推广费用', '人员培训费用', '其他费用']
        investment_amounts = [1200, 800, 300, 200, 80]
        
        wedges, texts, autotexts = ax1.pie(investment_amounts, labels=investment_types,
                                          colors=COLORS[:5], autopct='%1.1f%%',
                                          startangle=90, textprops={'fontsize': 10})
        ax1.set_title('总投资构成分析\n(总计2580万元)', fontsize=14)
        
        # 年度收益预测
        ax2 = fig.add_subplot(gs[0, 1])
        years = list(range(2025, 2030))
        revenues = [200, 450, 680, 845, 920]
        costs = [150, 280, 380, 475, 520]
        profits = [r - c for r, c in zip(revenues, costs)]
        
        x = np.arange(len(years))
        width = 0.35
        
        bars1 = ax2.bar(x - width/2, revenues, width, label='营业收入', 
                       color=COLORS[1], alpha=0.8)
        bars2 = ax2.bar(x + width/2, costs, width, label='运营成本', 
                       color=COLORS[2], alpha=0.8)
        
        # 净利润线
        ax2_twin = ax2.twinx()
        line = ax2_twin.plot(x, profits, marker='o', linewidth=3, color='red', 
                            markersize=8, label='净利润')
        
        ax2.set_xlabel('年份')
        ax2.set_ylabel('金额(万元)')
        ax2_twin.set_ylabel('净利润(万元)', color='red')
        ax2.set_title('年度收益预测分析', fontsize=14)
        ax2.set_xticks(x)
        ax2.set_xticklabels(years)
        ax2.legend(loc='upper left')
        ax2_twin.legend(loc='upper right')
        ax2.grid(True, alpha=0.3)
        
        # 投资回收期分析
        ax3 = fig.add_subplot(gs[1, 0])
        cumulative_profit = np.cumsum(profits)
        initial_investment = sum(investment_amounts)
        
        ax3.plot(years, cumulative_profit, marker='s', linewidth=4, 
                color=COLORS[3], markersize=10, label='累计净利润')
        ax3.axhline(y=initial_investment, color='red', linestyle='--', linewidth=3,
                   label=f'初始投资({initial_investment}万元)')
        ax3.fill_between(years, cumulative_profit, alpha=0.3, color=COLORS[3])
        
        # 找到回收期
        for i, cum_profit in enumerate(cumulative_profit):
            if cum_profit >= initial_investment:
                payback_year = years[i]
                ax3.axvline(x=payback_year, color='green', linestyle=':', linewidth=3,
                           label=f'投资回收期({payback_year}年)')
                break
        
        ax3.set_xlabel('年份')
        ax3.set_ylabel('累计利润(万元)')
        ax3.set_title('投资回收期分析', fontsize=14)
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 财务指标仪表盘
        ax4 = fig.add_subplot(gs[1, 1])
        indicators = ['净现值\n(NPV)', '内部收益率\n(IRR)', '投资回收期\n(年)', '投资回报率\n(ROI)']
        values = [1250, 28.5, 3.2, 25.0]
        colors_ind = COLORS2[:4]
        
        bars = ax4.bar(indicators, values, color=colors_ind, alpha=0.8,
                      edgecolor='white', linewidth=2)
        ax4.set_title('关键财务指标', fontsize=14)
        ax4.set_ylabel('数值')
        
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + height*0.02,
                    f'{value}', ha='center', va='bottom', fontweight='bold', fontsize=11)
        
        plt.suptitle(title, fontsize=18, fontweight='bold', y=0.98)
    
    elif chart_type == "数字化水平评估":
        fig = plt.figure(figsize=(16, 10))
        gs = fig.add_gridspec(2, 3, hspace=0.3, wspace=0.3)
        
        # 总体数字化水平仪表盘
        ax1 = fig.add_subplot(gs[0, 0])
        score = 3.2
        theta = np.linspace(0, np.pi, 100)
        r = np.ones_like(theta)
        ax1.plot(theta, r, 'k-', linewidth=10, alpha=0.3)
        
        score_theta = np.linspace(0, np.pi * (score/5), int(100 * score/5))
        score_r = np.ones_like(score_theta)
        ax1.plot(score_theta, score_r, color=COLORS[0], linewidth=10)
        
        pointer_angle = np.pi * (score/5)
        ax1.arrow(0, 0, 0.8*np.cos(pointer_angle), 0.8*np.sin(pointer_angle),
                 head_width=0.1, head_length=0.1, fc='red', ec='red', linewidth=3)
        
        ax1.set_xlim(-1.2, 1.2)
        ax1.set_ylim(-0.2, 1.2)
        ax1.set_aspect('equal')
        ax1.axis('off')
        ax1.text(0, -0.15, f'总体数字化水平\n{score}/5.0分', ha='center', va='top',
                fontsize=14, fontweight='bold')
        
        # 各维度评分对比
        ax2 = fig.add_subplot(gs[0, 1:])
        dimensions = ['基础设施建设', '技术应用水平', '数据管理能力', '数字化服务', '人才队伍建设']
        current_scores = [3.5, 3.0, 2.8, 3.2, 2.9]
        target_scores = [4.5, 4.2, 4.0, 4.3, 4.0]
        
        x = np.arange(len(dimensions))
        width = 0.35
        
        bars1 = ax2.bar(x - width/2, current_scores, width, label='当前水平', 
                       color=COLORS[1], alpha=0.8)
        bars2 = ax2.bar(x + width/2, target_scores, width, label='目标水平', 
                       color=COLORS[2], alpha=0.8)
        
        ax2.set_xlabel('评估维度')
        ax2.set_ylabel('评分(满分5分)')
        ax2.set_title('数字化水平各维度对比分析', fontsize=14)
        ax2.set_xticks(x)
        ax2.set_xticklabels(dimensions, rotation=45)
        ax2.legend()
        ax2.grid(True, alpha=0.3, axis='y')
        ax2.set_ylim(0, 5)
        
        # 数字化应用普及率
        ax3 = fig.add_subplot(gs[1, :])
        applications = ['移动支付', '在线预订系统', '智能导览服务', '大数据分析', 
                       'VR虚拟体验', '物联网监控', '人工智能客服', '区块链溯源']
        current_rate = [75, 45, 25, 20, 10, 15, 8, 5]
        target_rate = [95, 85, 70, 60, 45, 55, 40, 30]
        
        y_pos = np.arange(len(applications))
        
        bars1 = ax3.barh(y_pos - 0.2, current_rate, 0.4, label='当前普及率', 
                        color=COLORS3[0], alpha=0.8)
        bars2 = ax3.barh(y_pos + 0.2, target_rate, 0.4, label='目标普及率', 
                        color=COLORS3[1], alpha=0.8)
        
        ax3.set_yticks(y_pos)
        ax3.set_yticklabels(applications)
        ax3.set_xlabel('普及率(%)')
        ax3.set_title('数字化应用技术普及情况对比', fontsize=14)
        ax3.legend()
        ax3.grid(True, alpha=0.3, axis='x')
        ax3.set_xlim(0, 100)
        
        plt.suptitle(title, fontsize=18, fontweight='bold', y=0.98)
    
    plt.tight_layout()
    return fig

def generate_mass_chinese_charts():
    """批量生成中文专业图表"""
    set_chart_style()
    output_dir = create_output_dir()
    chart_count = 0
    
    # 定义要生成的图表
    charts_to_generate = [
        ("项目实施甘特图", "项目实施时间轴与里程碑规划", "chart_1_目标时间轴_4.png"),
        ("政策支持雷达图", "国家政策支持力度综合评估", "chart_2_政策支持_6.png"),
        ("投资回报组合分析", "项目投资回报综合分析", "chart_1_投资回报_5.png"),
        ("数字化水平评估", "宋庄镇数字化发展水平评估", "chart_2_数字化水平_10.png")
    ]
    
    for chart_type, title, filename in charts_to_generate:
        try:
            fig = create_complex_chart(chart_type, title, filename, {})
            fig.savefig(os.path.join(output_dir, filename), 
                       dpi=300, bbox_inches='tight', facecolor='white')
            plt.close(fig)
            chart_count += 1
            print(f"✓ 图表{chart_count}: {title}")
        except Exception as e:
            print(f"❌ 图表生成失败 {title}: {e}")
    
    return chart_count

if __name__ == "__main__":
    try:
        count = generate_mass_chinese_charts()
        print(f"\n✅ 成功生成 {count} 个专业中文组合图表!")
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()
