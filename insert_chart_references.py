#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re

def read_markdown_file(filepath):
    """读取markdown文件"""
    with open(filepath, 'r', encoding='utf-8') as f:
        return f.read()

def write_markdown_file(filepath, content):
    """写入markdown文件"""
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(content)

def insert_chart_references():
    """在markdown文档中插入图表引用"""
    
    # 读取原始文档
    source_file = "markdown_report/01_北京通州区宋庄镇农旅数字化推广项目可行性研究报告.md"
    content = read_markdown_file(source_file)
    
    # 定义图表插入位置和对应的图表
    chart_insertions = [
        # 第一章 项目概述
        {
            'search': '宋庄镇位于北京市通州区东部',
            'chart': '![宋庄镇基本情况概览](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_1_基本情况_1.png)\n\n'
        },
        {
            'search': '北京城区居民对周末休闲活动的需求日益多样化',
            'chart': '![北京城区家庭周末休闲需求分布](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_1_需求分布_2.png)\n\n'
        },
        {
            'search': '数字农业市场规模持续扩大',
            'chart': '![数字农业市场规模增长趋势](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_1_市场趋势_3.png)\n\n'
        },
        {
            'search': '### 1.2 项目目标',
            'chart': '![项目目标达成时间轴](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_1_目标时间轴_4.png)\n\n'
        },
        {
            'search': '预期投资回报率达到25%以上',
            'chart': '![项目投资回报分析](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_1_投资回报_5.png)\n\n'
        },
        
        # 第二章 政策环境分析
        {
            'search': '### 2.1 国家政策支持',
            'chart': '![国家政策支持力度评估](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_2_政策支持_6.png)\n\n'
        },
        {
            'search': '北京市在数字农业发展方面起步较早',
            'chart': '![北京市数字农旅发展现状](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_2_发展现状_7.png)\n\n'
        },
        {
            'search': '通州区拥有丰富的旅游资源',
            'chart': '![通州区旅游目的地分布](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_2_目的地分布_8.png)\n\n'
        },
        {
            'search': '宋庄镇农业发展面临诸多挑战',
            'chart': '![宋庄镇农业发展痛点分析](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_2_痛点分析_9.png)\n\n'
        },
        {
            'search': '数字化基础设施相对薄弱',
            'chart': '![宋庄镇数字化水平评估](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_2_数字化水平_10.png)\n\n'
        },
        
        # 第三章 可行性分析
        {
            'search': '### 3.1 区位优势分析',
            'chart': '![宋庄镇地理位置优势分析](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_地理优势_11.png)\n\n'
        },
        {
            'search': '宋庄镇属于温带大陆性季风气候',
            'chart': '![宋庄镇气候条件适宜性](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_气候条件_12.png)\n\n'
        },
        {
            'search': '土地资源相对丰富',
            'chart': '![土地资源利用现状](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_土地利用_13.png)\n\n'
        },
        {
            'search': '宋庄艺术区的发展历程',
            'chart': '![宋庄艺术区发展历程](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_艺术发展_14.png)\n\n'
        },
        {
            'search': '聚集了大量艺术家',
            'chart': '![艺术家资源分布](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_艺术家分布_15.png)\n\n'
        },
        {
            'search': '各类艺术机构类型丰富',
            'chart': '![艺术机构类型分析](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_机构类型_16.png)\n\n'
        },
        {
            'search': '### 3.3 基础设施条件',
            'chart': '![交通便利性评估](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_基础设施_17.png)\n\n'
        },
        {
            'search': '数字基础设施建设',
            'chart': '![数字基础设施现状](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_数字基础_18.png)\n\n'
        },
        {
            'search': '公共服务设施配套',
            'chart': '![公共服务设施配套](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_公共服务_19.png)\n\n'
        },
        {
            'search': '### 3.4 人力资源条件',
            'chart': '![人力资源结构分析](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_人力结构_20.png)\n\n'
        },
        {
            'search': '数字素养有待提升',
            'chart': '![数字素养水平评估](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_数字素养_21.png)\n\n'
        },
        {
            'search': '专业人才相对缺乏',
            'chart': '![人才需求与缺口分析](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_人才缺口_22.png)\n\n'
        },
        {
            'search': '### 3.5 市场需求分析',
            'chart': '![北京城区居民休闲需求特征](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_休闲需求_23.png)\n\n'
        },
        {
            'search': '目标客户群体明确',
            'chart': '![目标客户群体画像](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_客户画像_24.png)\n\n'
        },
        {
            'search': '市场竞争相对温和',
            'chart': '![市场竞争格局分析](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_竞争格局_25.png)\n\n'
        },
        {
            'search': '### 3.6 政策环境分析',
            'chart': '![政策环境匹配度评估](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_政策匹配_26.png)\n\n'
        },
        {
            'search': '政策受益程度较高',
            'chart': '![政策受益度分析](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_政策受益_27.png)\n\n'
        },
        {
            'search': '### 3.7 技术可行性分析',
            'chart': '![技术成熟度评估](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_技术成熟_28.png)\n\n'
        },
        {
            'search': '技术实施存在一定难度',
            'chart': '![技术实施难度分析](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_技术难度_29.png)\n\n'
        },
        {
            'search': '技术风险可控',
            'chart': '![技术风险评估](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_技术风险_30.png)\n\n'
        },
        {
            'search': '### 3.8 经济可行性分析',
            'chart': '![项目投资成本分析](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_投资成本_31.png)\n\n'
        },
        {
            'search': '项目收益前景良好',
            'chart': '![项目收益预测](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_收益预测_32.png)\n\n'
        },
        {
            'search': '财务评价指标良好',
            'chart': '![财务评价指标分析](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_财务指标_33.png)\n\n'
        },
        {
            'search': '敏感性分析表明',
            'chart': '![敏感性分析](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_敏感性_34.png)\n\n'
        },
        {
            'search': '### 3.9 社会可行性分析',
            'chart': '![社会效益评估](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_社会效益_35.png)\n\n'
        },
        {
            'search': '社会风险较低',
            'chart': '![社会风险分析](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_社会风险_36.png)\n\n'
        },
        {
            'search': '社会接受度较高',
            'chart': '![社会接受度调查](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_社会接受_37.png)\n\n'
        },
        {
            'search': '### 3.10 环境可行性分析',
            'chart': '![环境影响评估](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_环境影响_38.png)\n\n'
        },
        {
            'search': '环境效益显著',
            'chart': '![环境效益分析](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_环境效益_39.png)\n\n'
        },
        {
            'search': '### 3.11 综合可行性评价',
            'chart': '![综合可行性评价](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_3_综合评价_40.png)\n\n'
        },
        
        # 第四章 结论与建议
        {
            'search': '## 4. 结论与建议',
            'chart': '![项目实施路线图](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_4_实施路线_41.png)\n\n'
        },
        {
            'search': '### 4.2 项目发展前景',
            'chart': '![项目发展前景展望](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_4_发展前景_42.png)\n\n'
        },
        {
            'search': '项目里程碑规划',
            'chart': '![项目里程碑时间线](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_4_项目里程碑_43.png)\n\n'
        },
        {
            'search': '成功关键因素',
            'chart': '![成功因素权重分析](output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/chart_4_成功因素_44.png)\n\n'
        }
    ]
    
    # 逐个插入图表引用
    for insertion in chart_insertions:
        search_text = insertion['search']
        chart_ref = insertion['chart']
        
        # 查找插入位置
        if search_text in content:
            # 在找到的文本后插入图表引用
            content = content.replace(search_text, search_text + '\n\n' + chart_ref)
            print(f"✓ 已插入图表: {chart_ref.split('/')[-1].split(')')[0]}")
        else:
            print(f"⚠️ 未找到插入位置: {search_text}")
    
    # 保存修改后的文档
    write_markdown_file(source_file, content)
    print(f"\n✅ 图表引用插入完成！文档已更新: {source_file}")
    
    return len(chart_insertions)

if __name__ == "__main__":
    try:
        count = insert_chart_references()
        print(f"📊 总共插入了 {count} 个图表引用")
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()
