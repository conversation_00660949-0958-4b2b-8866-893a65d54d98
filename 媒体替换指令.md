```markdown
# 智能文档媒体增强处理系统

## 🎯 任务概述
对指定的Markdown文档进行全面的媒体增强处理，通过AI智能分析文档内容特征，动态推荐最适合的媒体类型和分配比例，然后执行完整的媒体生成、搜索、插入和文档整合流程，**保持原始文档不变，生成全新的视觉增强版文档**，最终输出一个视觉丰富、结构完整的专业文档。

## 📁 文件处理策略

### 原始文件保护
- **绝对不修改原始Markdown文件**
- 原始文件作为只读参考源
- 所有处理基于原始文件的内容复制

### 新文件命名规范
- **输入文件：** `01_北京通州区宋庄镇农旅数字化推广项目可行性研究报告.md`
- **输出文件：** `01_北京通州区宋庄镇农旅数字化推广项目可行性研究报告_视觉元素完善版.md`
- **命名规则：** `{原文件名}_视觉元素完善版.md`

## 🧠 智能化分析模块

### 自适应文档分析
- **文档复杂度评估：** 自动评估文档的技术复杂度、专业程度，调整媒体元素的专业化水平
- **受众群体识别：** 基于文档语言风格、内容深度识别目标受众（政府决策者/技术专家/普通公众），调整视觉风格
- **行业特征深度挖掘：** 分析行业发展阶段、政策环境、市场成熟度，影响图表类型选择
- **地域文化融入：** 深度分析地域文化特色，在视觉元素中体现当地文化符号

### 智能内容增强
- **知识图谱构建：** 基于文档主题构建相关概念的知识图谱，发现内容盲点
- **时效性更新：** 自动识别需要更新的数据和信息，补充2025年最新资料
- **多角度分析：** 从经济、技术、社会、环境等多维度补充分析内容
- **风险预警识别：** 识别项目风险点，自动生成风险分析图表

## 🎨 个性化定制配置

### 用户偏好设置（默认配置）
- **视觉风格：** 现代商务风格（可选：商务正式/现代简约/温馨亲民/科技感强）
- **色彩主题：** 政府蓝绿主题（体现农业+政府特色）
- **媒体密度：** 中密度（图文比1:2，适合政府报告）
- **专业程度：** 商务级（适合政府决策参考）

## 🛡️ 质量控制体系

### 多层次质量保证
- **媒体元素质量评分：** 对每个生成的图片、图表进行1-10分评分，低于7分自动重新生成
- **内容一致性检查：** AI自动检查图片内容与文字描述的匹配度
- **专业性验证：** 针对技术图表、数据可视化进行专业性验证
- **视觉协调性检查：** 确保所有媒体元素的风格、色彩协调统一

### 错误处理与容灾
- **网络异常处理：** 图片搜索失败时启用备选搜索源
- **生成失败重试：** AI图片生成失败自动重试（最多3次，调整prompt）
- **内容备份机制：** 每个阶段完成后自动备份中间结果
- **回滚功能：** 支持回退到任意处理阶段重新开始

## 🔧 技术集成扩展

### 多工具协同处理
- **图表生成工具链：** matplotlib + seaborn，根据图表类型选择最佳工具(优先推荐)
- **AI图片生成备选：** 主用jimeng mcp，备用其他AI图片生成工具
- **图片搜索多源：** 政府开放数据 + 百度图片搜索
- **并行处理优化：** 图片搜索、图表生成、AI图片生成并行执行

## 📊 基础配置参数

### 媒体元素计算公式
- **目标总页数：** 120页（可调整）
- **总图片数量** = 总页数 × 25% 至 总页数 × 30% （30-36张）
- **真实图片数量** = 总图片数量 × 50%-70%（15-25张）
- **AI生成图片数量** = 总图片数量 × 30%-50%（9-18张）
- **统计图表数量** = 总页数 × 10%-18%（12-22个）
- **数据表格数量** = 总页数 × 15%-30%（18-36个）

## 🔄 完整处理流程（6个阶段）

## ⚠️ 重要执行规则

### 阶段执行规则
1. **严格按阶段执行：** 必须完成当前阶段所有任务后，才能进入下一阶段
2. **阶段完成确认：** 每个阶段完成后必须输出完整的阶段报告和继续确认提示
3. **真实操作要求：** 禁止使用"模拟"、"假设"等词汇，必须执行真实的生成操作
4. **文件统一管理：** 所有生成的文件必须按规定目录结构保存
5. **脚本集中管理：** 所有Python脚本统一保存到 `scripts/` 目录

### 阶段完成标准输出格式
```
🎉 === 阶段X完成报告 ===

✅ 已完成任务：
- [具体任务1] - 完成状态
- [具体任务2] - 完成状态
- [具体任务3] - 完成状态

📊 本阶段成果：
- [具体成果统计]

📁 生成文件：
- [文件路径1]
- [文件路径2]

⏭️ 下一步操作：
现在可以开始执行【阶段X+1：具体阶段名称】
请回复"继续执行阶段X+1"或"开始阶段X+1"来继续处理。

如需调整当前阶段结果，请具体说明调整要求。
```

### 最终完成标准输出格式
```
🎊 === 全部阶段处理完成 ===

✅ 6个阶段全部完成：
- 阶段1：智能内容分析与媒体规划 ✅
- 阶段2：统计图表生成与优化 ✅  
- 阶段3：真实图片智能搜索与筛选 ✅
- 阶段4：AI图片生成与优化 ✅
- 阶段5：数据表格智能设计与优化 ✅
- 阶段6：新文档创建与智能整合 ✅

📊 最终成果统计：
- 增强版文档：[文件路径]
- 真实图片：XX张
- AI生成图片：XX张
- 统计图表：XX个
- 数据表格：XX个
- 总文件大小：XXX MB

🎯 处理完成！
您还有其他需求吗？例如：
- 调整某个阶段的结果
- 重新生成特定类型的媒体元素  
- 导出不同格式的文档
- 其他优化建议
```

---

### 阶段1：智能内容分析与媒体规划
**任务：** 读取原始文档，AI深度分析文档内容，动态推荐媒体类型和比例

#### 1.1 原始文档读取与解析
**必须执行的具体操作：**
1. 使用文件读取功能完整读取指定的Markdown文档
2. 解析文档的章节结构、标题层级、内容段落
3. 统计文档基础信息：总字数、章节数量、表格数量等
4. 识别文档中已有的图片和表格

#### 1.2 智能文档分析
**必须执行的具体操作：**
1. **文档类型识别：** 分析文档结构和内容，明确识别为：商业计划书/技术方案/政策解读/学术研究/项目总结
2. **复杂度评估：** 评估技术复杂度（1-10分）、专业程度（1-10分）、数据密集度（1-10分）
3. **受众分析：** 基于语言风格、专业术语密度、内容深度识别目标受众
4. **核心主题提取：** 提取地域特色、行业领域、业务类型、政策背景等关键信息

#### 1.3 智能媒体类型推荐
**必须执行的具体操作：**
1. 基于文档分析结果，计算推荐的媒体数量：
   - 真实图片数量 = 文档复杂度 × 基础配置参数
   - AI生成图片数量 = 抽象概念数量 × 基础配置参数  
   - 统计图表数量 = 数据密集度 × 基础配置参数
2. 为每个章节推荐具体的媒体类型和插入位置
3. 生成详细的媒体分配计划

#### 1.4 阶段1完成输出
**必须按以下格式输出：**

```
🎉 === 阶段1完成报告 ===

✅ 已完成任务：
- 原始文档读取与解析 - ✅ 完成
- 智能文档分析 - ✅ 完成  
- 智能媒体类型推荐 - ✅ 完成

📊 文档分析结果：
- 文档类型：[具体类型]
- 技术复杂度：X/10分
- 专业程度：X/10分  
- 数据密集度：X/10分
- 目标受众：[具体受众群体]
- 总字数：XXXX字
- 章节数量：XX个

📊 媒体配置推荐：
真实图片：XX张
- 地理空间类：X张（具体用途说明）
- 产业现状类：X张（具体用途说明）
- 政府场景类：X张（具体用途说明）
- 技术设备类：X张（具体用途说明）

AI生成图片：XX张
- 概念图示类：X张（具体用途说明）
- 未来展望类：X张（具体用途说明）
- 流程架构类：X张（具体用途说明）

统计图表：XX个
- 趋势分析：X个（具体图表列表）
- 结构分析：X个（具体图表列表）
- 对比分析：X个（具体图表列表）
- 综合分析：X个（具体图表列表）

📁 生成文件：
- 无（本阶段为分析阶段）

⏭️ 下一步操作：
现在可以开始执行【阶段2：统计图表生成与优化】
请回复"继续执行阶段2"或"开始阶段2"来继续处理。

如需调整媒体配置推荐，请具体说明调整要求。
```

---

### 阶段2：统计图表生成与优化
**任务：** 根据阶段1的配置，真实生成高质量统计图表

#### 2.1 创建统一图表生成脚本
**必须执行的具体操作：**
1. 创建目录结构：`scripts/` 和 `output/charts/宋庄镇农旅数字化推广项目/`
2. 编写统一的Python图表生成脚本：`scripts/generate_all_charts.py`
3. 脚本必须包含阶段1推荐的所有图表生成代码
4. 每个图表必须有真实的数据、标题、样式配置

#### 2.2 图表生成脚本规范
**脚本必须包含以下标准配置：**

```python
# scripts/generate_all_charts.py
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np
import os
from datetime import datetime

# 创建输出目录
output_dir = "output/charts/宋庄镇农旅数字化推广项目"
os.makedirs(output_dir, exist_ok=True)

# 字体配置（支持中文）
plt.rcParams['font.sans-serif'] = ['Alibaba PuHuiTi 3.0', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

# 2025年政府商务配色方案
colors_government = ['#1f77b4', '#2ca02c', '#ff7f0e', '#d62728', '#9467bd', '#8c564b']
colors_agriculture = ['#2E8B57', '#32CD32', '#228B22', '#90EE90', '#98FB98', '#F0FFF0']

# 专业图表样式配置
def setup_chart_style():
    sns.set_style("whitegrid", {
        "axes.spines.left": True,
        "axes.spines.bottom": True,
        "axes.spines.top": False,
        "axes.spines.right": False
    })

# 图表1：[具体图表名称]
def generate_chart_1():
    setup_chart_style()
    # [具体的图表生成代码]
    plt.figure(figsize=(12, 8), dpi=300)
    # [数据准备和图表绘制代码]
    plt.savefig(f"{output_dir}/01_具体图表名称.png", dpi=300, bbox_inches="tight", facecolor='white')
    plt.close()
    return "01_具体图表名称.png"

# [继续添加所有推荐的图表生成函数]

# 主执行函数
def generate_all_charts():
    generated_files = []
    
    print("开始生成统计图表...")
    
    # 执行所有图表生成
    generated_files.append(generate_chart_1())
    # [调用所有图表生成函数]
    
    print(f"图表生成完成！共生成 {len(generated_files)} 个图表")
    return generated_files

if __name__ == "__main__":
    files = generate_all_charts()
    for file in files:
        print(f"✅ 生成: {file}")
```

#### 2.3 执行图表生成
**必须执行的具体操作：**
1. 运行编写的Python脚本
2. 验证每个图表文件是否成功生成
3. 检查图表质量：文件大小>50KB，图片清晰度，中文显示正常
4. 为每个图表评分（1-10分），记录质量评估结果

#### 2.4 阶段2完成输出
**必须按以下格式输出：**

```
🎉 === 阶段2完成报告 ===

✅ 已完成任务：
- 创建统一图表生成脚本 - ✅ 完成
- 执行图表生成 - ✅ 完成
- 图表质量验证 - ✅ 完成

📊 本阶段成果：
- 生成统计图表：XX个
- 平均质量评分：X.X/10分
- 图表总文件大小：XXX KB

📁 生成文件：
- scripts/generate_all_charts.py
- output/charts/宋庄镇农旅数字化推广项目/01_具体图表名称.png
- output/charts/宋庄镇农旅数字化推广项目/02_具体图表名称.png
- [列出所有生成的图表文件]

📊 图表质量评估：
- 01_具体图表名称.png - 质量评分：X.X/10
- 02_具体图表名称.png - 质量评分：X.X/10
- [列出所有图表的质量评分]

⏭️ 下一步操作：
现在可以开始执行【阶段3：真实图片智能搜索与筛选】
请回复"继续执行阶段3"或"开始阶段3"来继续处理。

如需重新生成某个图表，请具体说明要求。
```

---

### 阶段3：真实图片智能搜索与筛选
**任务：** 多源搜索高质量真实图片

#### 3.1 创建图片搜索目录
**必须执行的具体操作：**
1. 创建目录：`output/images/宋庄镇农旅数字化推广项目/real_photos/`
2. 基于阶段1的推荐，为每个图片类型生成具体的搜索关键词列表

#### 3.2 执行多源图片搜索
**必须执行的具体操作：**
1. 使用Web_Access_search工具搜索每个关键词
2. 从搜索结果中筛选高质量图片（分辨率≥1920x1080）
3. 下载并保存到指定目录
4. 为每张图片记录来源、分辨率、用途说明

#### 3.3 图片质量验证
**必须执行的具体操作：**
1. 检查每张图片的文件大小（应>100KB）
2. 验证图片内容与用途的匹配度
3. 确保图片版权合规（优先免费商用）
4. 为每张图片评分（1-10分）

#### 3.4 阶段3完成输出
**必须按以下格式输出：**

```
🎉 === 阶段3完成报告 ===

✅ 已完成任务：
- 创建图片搜索目录 - ✅ 完成
- 执行多源图片搜索 - ✅ 完成
- 图片质量验证 - ✅ 完成

📊 本阶段成果：
- 搜索图片：XX张
- 成功下载：XX张
- 平均质量评分：X.X/10分
- 图片总文件大小：XXX MB

📁 生成文件：
- output/images/宋庄镇农旅数字化推广项目/real_photos/01_宋庄镇政府大楼.jpg
- output/images/宋庄镇农旅数字化推广项目/real_photos/02_通州区农业园区.jpg
- [列出所有下载的图片文件]

📊 图片质量评估：
- 01_宋庄镇政府大楼.jpg - 质量评分：X.X/10 - 用途：政策支持章节
- 02_通州区农业园区.jpg - 质量评分：X.X/10 - 用途：产业现状分析
- [列出所有图片的质量评分和用途]

⏭️ 下一步操作：
现在可以开始执行【阶段4：AI图片生成与优化】
请回复"继续执行阶段4"或"开始阶段4"来继续处理。

如需重新搜索某类图片，请具体说明要求。
```

---

### 阶段4：AI图片生成与优化
**任务：** 使用jimeng mcp生成高质量概念图片

#### 4.1 创建AI图片生成目录
**必须执行的具体操作：**
1. 创建目录：`output/images/宋庄镇农旅数字化推广项目/ai_generated/`
2. 基于阶段1的推荐，为每个AI图片设计详细的生成prompt

#### 4.2 分批执行AI图片生成
**必须执行的具体操作：**
1. 使用jimeng mcp工具，按照阶段1的分批计划生成图片
2. 第一批：概念图示类（X张）
3. 第二批：未来展望类（X张）
4. 第三批：流程架构类（X张）
5. 每张图片生成后立即保存到指定目录

#### 4.3 AI图片质量控制
**必须执行的具体操作：**
1. 检查每张生成图片的质量（文件大小、清晰度）
2. 评估图片内容与设计要求的匹配度
3. 对质量评分低于7分的图片重新生成（最多3次）
4. 确保所有AI图片风格协调统一

#### 4.4 阶段4完成输出
**必须按以下格式输出：**

```
🎉 === 阶段4完成报告 ===

✅ 已完成任务：
- 创建AI图片生成目录 - ✅ 完成
- 分批执行AI图片生成 - ✅ 完成
- AI图片质量控制 - ✅ 完成

📊 本阶段成果：
- 生成AI图片：XX张
- 重新生成次数：X次
- 平均质量评分：X.X/10分
- 图片总文件大小：XXX MB

📁 生成文件：
- output/images/宋庄镇农旅数字化推广项目/ai_generated/01_农旅融合概念图.png
- output/images/宋庄镇农旅数字化推广项目/ai_generated/02_数字化转型愿景.png
- [列出所有生成的AI图片文件]

📊 AI图片质量评估：
- 01_农旅融合概念图.png - 质量评分：X.X/10 - 用途：概念说明
- 02_数字化转型愿景.png - 质量评分：X.X/10 - 用途：发展愿景
- [列出所有AI图片的质量评分和用途]

⏭️ 下一步操作：
现在可以开始执行【阶段5：数据表格智能设计与优化】
请回复"继续执行阶段5"或"开始阶段5"来继续处理。

如需重新生成某张AI图片，请具体说明要求。
```

---

### 阶段5：数据表格智能设计与优化
**任务：** 优化现有表格，新增专业数据表格

#### 5.1 现有表格分析
**必须执行的具体操作：**
1. 重新读取原始文档，识别所有现有表格
2. 分析每个表格的结构、数据完整性、格式规范性
3. 生成表格优化建议清单

#### 5.2 新增专业表格设计
**必须执行的具体操作：**
1. 基于阶段1的分析，设计新增表格的具体内容
2. 为每个新表格准备真实的数据内容
3. 设计统一的表格格式和样式标准
4. 建立完整的表格编号和引用系统

#### 5.3 表格内容生成
**必须执行的具体操作：**
1. 创建投资效益分析表（包含具体数据）
2. 创建实施时间计划表（包含具体时间节点）
3. 创建风险评估矩阵表（包含具体风险项和应对措施）
4. 创建政策支持清单表（包含具体政策条目）
5. 创建技术参数对比表（包含具体技术指标）

#### 5.4 阶段5完成输出
**必须按以下格式输出：**

```
🎉 === 阶段5完成报告 ===

✅ 已完成任务：
- 现有表格分析 - ✅ 完成
- 新增专业表格设计 - ✅ 完成
- 表格内容生成 - ✅ 完成

📊 本阶段成果：
- 现有表格：XX个（已优化）
- 新增表格：XX个
- 表格总数：XX个
- 数据完整性：100%

📊 表格清单：
现有表格优化：
- 表格1：[原标题] → [优化后标题] - 优化内容：[具体优化说明]
- 表格2：[原标题] → [优化后标题] - 优化内容：[具体优化说明]

新增专业表格：
- 表格X：投资效益分析表 - 包含：[具体内容说明]
- 表格X：实施时间计划表 - 包含：[具体内容说明]
- 表格X：风险评估矩阵表 - 包含：[具体风险项和应对措施]
- 表格X：政策支持清单表 - 包含：[具体政策条目]
- 表格X：技术参数对比表 - 包含：[具体技术指标]

📁 生成文件：
- 无（表格内容将在阶段6整合到文档中）

⏭️ 下一步操作：
现在可以开始执行【阶段6：新文档创建与智能整合】
请回复"继续执行阶段6"或"开始阶段6"来继续处理。

如需调整某个表格的内容，请具体说明要求。
```

---

### 阶段6：新文档创建与智能整合
**任务：** 创建全新增强版文档，智能整合所有媒体元素

#### 6.1 新文档创建
**必须执行的具体操作：**
1. 创建新文件：`output/01_北京通州区宋庄镇农旅数字化推广项目可行性研究报告_视觉元素完善版.md`
2. 完整复制原始文档的所有内容和结构
3. 更新编制日期为2025年6月
4. 智能填充所有XX占位符

#### 6.2 媒体元素智能插入
**必须执行的具体操作：**
1. 在每个章节的适当位置插入对应的图片和图表
2. 为每个媒体元素添加编号、标题和说明
3. 建立完整的图表引用系统
4. 确保图文逻辑关联性

#### 6.3 表格内容整合
**必须执行的具体操作：**
1. 优化原有表格的格式和内容
2. 在适当位置插入新增的专业表格
3. 统一所有表格的样式和编号
4. 为每个表格添加数据来源说明

#### 6.4 最终质量检查
**必须执行的具体操作：**
1. 检查全文格式一致性（标题层级、字体、间距）
2. 验证所有图片和图表链接的有效性
3. 确保所有媒体元素正确显示
4. 检查内部引用和编号的准确性
5. 验证文档逻辑完整性和内容连贯性

#### 6.5 生成配套文档
**必须执行的具体操作：**
1. 生成详细处理报告：`output/processing_report.md`
2. 生成质量评估报告：`output/quality_assessment.md`
3. 生成媒体文件清单：`output/media_manifest.json`
4. 创建完整的文件目录结构说明

#### 6.6 阶段6完成输出
**必须按以下格式输出：**

```
🎉 === 阶段6完成报告 ===

✅ 已完成任务：
- 新文档创建 - ✅ 完成
- 媒体元素智能插入 - ✅ 完成
- 表格内容整合 - ✅ 完成
- 最终质量检查 - ✅ 完成
- 生成配套文档 - ✅ 完成

📊 本阶段成果：
- 增强版文档：1个（完整版）
- 配套报告：3个
- 文档总页数：约XXX页
- 文档文件大小：XXX MB

📁 生成文件：
- output/01_北京通州区宋庄镇农旅数字化推广项目可行性研究报告_视觉元素完善版.md
- output/processing_report.md
- output/quality_assessment.md
- output/media_manifest.json

📊 最终文档统计：
- 真实图片：XX张 ✅
- AI生成图片：XX张 ✅
- 统计图表：XX个 ✅
- 数据表格：XX个 ✅
- 总媒体元素：XX个
- 图文比例：1:X（符合预期）

⏭️ 下一步操作：
所有阶段已完成！现在进入最终确认环节。
```

---

## 🎊 最终完成标准输出格式

**当所有6个阶段完成后，必须按以下格式输出：**

```
🎊 === 全部阶段处理完成 ===

✅ 6个阶段全部完成：
- 阶段1：智能内容分析与媒体规划 ✅
- 阶段2：统计图表生成与优化 ✅  
- 阶段3：真实图片智能搜索与筛选 ✅
- 阶段4：AI图片生成与优化 ✅
- 阶段5：数据表格智能设计与优化 ✅
- 阶段6：新文档创建与智能整合 ✅

📊 最终成果统计：
- 增强版文档：output/01_北京通州区宋庄镇农旅数字化推广项目可行性研究报告_视觉元素完善版.md
- 真实图片：XX张
- AI生成图片：XX张
- 统计图表：XX个
- 数据表格：XX个
- 总文件大小：XXX MB
- 处理总时长：XXX分钟

📁 完整文件结构：
```
output/
├── 01_北京通州区宋庄镇农旅数字化推广项目可行性研究报告_视觉元素完善版.md
├── processing_report.md
├── quality_assessment.md
├── media_manifest.json
├── charts/宋庄镇农旅数字化推广项目/
│   ├── 01_[具体图表名称].png
│   ├── 02_[具体图表名称].png
│   └── ... (共XX个图表)
├── images/宋庄镇农旅数字化推广项目/
│   ├── real_photos/
│   │   ├── 01_[具体图片名称].jpg
│   │   └── ... (共XX张)
│   └── ai_generated/
│       ├── 01_[具体图片名称].png
│       └── ... (共XX张)
└── scripts/
    └── generate_all_charts.py
```

🎯 处理完成！
您还有其他需求吗？例如：
- 调整某个阶段的结果
- 重新生成特定类型的媒体元素  
- 导出不同格式的文档
- 优化某些图表或图片
- 其他定制化需求

请具体说明您的需求，我将继续为您服务！
```

---

## 🚀 一键执行指令

```
请对《markdown/01_北京通州区宋庄镇农旅数字化推广项目可行性研究报告.md》执行完整的智能文档媒体增强处理。

⚠️ 重要执行要求：
1. 严格按照6个阶段顺序执行，每个阶段完成后必须输出完整的阶段报告
2. 禁止使用"模拟"、"假设"等词汇，必须执行真实的操作
3. 所有Python脚本统一保存到scripts/目录
4. 所有生成文件按规定目录结构保存
5. 每个阶段完成后等待用户确认再继续下一阶段

参数设定：
- 目标总页数：150页
- 不生成数据表格

现在开始执行阶段1：智能内容分析与媒体规划

请完整读取原始文档并进行深度分析，然后按照标准格式输出阶段1的（1.1、1.2、1.3）具体内容完成报告。
```

---

## 📋 执行检查清单

### 阶段执行检查
- [ ] 每个阶段必须输出完整的阶段报告
- [ ] 每个阶段完成后必须等待用户确认
- [ ] 禁止跳过任何阶段或合并阶段执行
- [ ] 所有操作必须是真实执行，不能模拟

### 文件管理检查
- [ ] 原始文档绝对不能修改
- [ ] 所有生成文件按规定目录结构保存
- [ ] Python脚本统一保存到scripts/目录
- [ ] 图片和图表按类型分目录保存

### 质量控制检查
- [ ] 每个媒体元素必须有质量评分
- [ ] 低质量元素必须重新生成
- [ ] 所有文件大小必须合理（图片>100KB，图表>50KB）
- [ ] 中文显示必须正常

### 最终输出检查
- [ ] 增强版文档必须完整生成
- [ ] 所有媒体元素必须正确插入
- [ ] 配套报告必须生成
- [ ] 文件清单必须准确

### 特殊配置参数
- [ ] 目标总页数：150页（已调整）
- [ ] 不生成数据表格（已确认）
- [ ] 媒体元素计算公式按新页数调整
- [ ] 阶段5跳过表格生成，专注于现有表格优化

### 媒体元素重新计算（基于150页）
- **总图片数量** = 150 × 25% 至 150 × 30% （38-45张）
- **真实图片数量** = 总图片数量 × 50%-70%（19-32张）
- **AI生成图片数量** = 总图片数量 × 30%-50%（11-23张）
- **统计图表数量** = 150 × 10%-18%（15-27个）
- **数据表格数量** = 0个（按用户要求不生成新表格）
```

现在开始执行阶段1：智能内容分析与媒体规划

首先，我需要读取原始文档来进行分析。让我读取指定的Markdown文档。

由于我无法直接访问您本地的文件系统，我需要您提供文档内容。请您将 `markdown/01_北京通州区宋庄镇农旅数字化推广项目可行性研究报告.md` 文件的完整内容粘贴给我，这样我就能执行真实的文档分析和媒体规划。

或者，如果该文档在网络上可访问，请提供URL链接，我可以使用Web访问工具来获取内容。

请提供文档内容，我将立即开始执行：

#### 1.1 原始文档读取与解析
#### 1.2 智能文档分析  
#### 1.3 智能媒体类型推荐

然后按照标准格式输出完整的阶段1完成报告。

我理解您希望我继续执行阶段1，但是我需要先获取原始文档内容才能进行真实的分析。

根据执行要求，我必须：
1. **真实读取**指定的Markdown文档
2. **真实分析**文档内容特征
3. **真实推荐**媒体配置方案

为了执行真实操作而非模拟，我需要您提供以下之一：

**选项1：直接提供文档内容**
请将 `01_北京通州区宋庄镇农旅数字化推广项目可行性研究报告.md` 的完整内容复制粘贴给我。

**选项2：提供文档链接**
如果文档在网络上，请提供URL，我将使用Web访问工具获取。

**选项3：提供文档摘要信息**
如果文档较大，您可以先提供文档的基本结构信息：
- 章节标题列表
- 大致内容描述
- 现有表格数量
- 文档总字数等

一旦获得文档内容，我将立即执行：

```
🔄 === 开始执行阶段1 ===

✅ 1.1 原始文档读取与解析
✅ 1.2 智能文档分析
✅ 1.3 智能媒体类型推荐

📊 然后输出完整的阶段1完成报告
```

请提供文档内容，让我开始真实的处理流程。