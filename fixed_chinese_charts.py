#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import matplotlib
matplotlib.use('Agg')

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import os
from matplotlib import font_manager
import warnings
warnings.filterwarnings('ignore')

# 查找阿里巴巴字体
font_list = font_manager.findSystemFonts(fontpaths=None, fontext='ttf')
alibaba_fonts = [f for f in font_list if 'Alibaba' in f]
print("找到的阿里巴巴字体:")
for font in alibaba_fonts:
    print(f"  - {os.path.basename(font)}")

# 创建字体对象
alibaba_font = None
if alibaba_fonts:
    try:
        alibaba_font = font_manager.FontProperties(fname=alibaba_fonts[0])
        print(f"\n成功创建字体对象，使用字体: {alibaba_fonts[0]}")
    except Exception as e:
        print(f"创建字体对象失败: {e}")

# 手动添加阿里巴巴字体
for font_path in alibaba_fonts:
    try:
        font_manager.fontManager.addfont(font_path)
    except Exception as e:
        print(f"添加字体失败: {font_path}, 错误: {e}")

# 刷新字体缓存
try:
    font_manager.fontManager.rebuild()
except AttributeError:
    try:
        font_manager._rebuild()
    except AttributeError:
        print("无法刷新字体缓存，继续执行")

# 设置字体
plt.rcParams['font.sans-serif'] = ['AlibabaPuHuiTi-3-55-Regular', 'AlibabaPuHuiTi-3-65-Medium', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

# 如果没有成功创建字体对象，尝试使用名称创建
if not alibaba_font:
    try:
        alibaba_font = font_manager.FontProperties(family='AlibabaPuHuiTi-3-55-Regular')
        print("使用字体名称创建字体对象成功")
    except Exception as e:
        print(f"使用字体名称创建字体对象失败: {e}")
        try:
            alibaba_font = font_manager.FontProperties(family='SimHei')
            print("使用SimHei字体作为备选")
        except:
            print("无法创建任何中文字体对象，图表中文可能无法正确显示")

# 设置全局字体样式
plt.rcParams['font.family'] = 'sans-serif'

# 设置matplotlib的字体缓存目录权限
os.environ['MPLCONFIGDIR'] = os.path.expanduser('~/.matplotlib')
os.makedirs(os.environ['MPLCONFIGDIR'], exist_ok=True)

# 小清新亮色配色方案
COLORS = ['#FF9AA2', '#FFB7B2', '#FFDAC1', '#E2F0CB', '#B5EAD7', '#C7CEEA']
COLORS2 = ['#A8E6CF', '#DCEDC1', '#FFD3A5', '#FFA8A8', '#B5B5EA', '#F8BBD9']
COLORS3 = ['#87CEEB', '#98FB98', '#F0E68C', '#FFA07A', '#DDA0DD', '#F5DEB3']

def create_output_dir():
    output_dir = "output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/"
    os.makedirs(output_dir, exist_ok=True)
    return output_dir

def set_chinese_text(ax, title, xlabel='', ylabel=''):
    """设置中文文本，确保正确显示"""
    if alibaba_font:
        ax.set_title(title, fontsize=14, fontproperties=alibaba_font)
        if xlabel:
            ax.set_xlabel(xlabel, fontproperties=alibaba_font)
        if ylabel:
            ax.set_ylabel(ylabel, fontproperties=alibaba_font)
        # 设置刻度标签字体
        for label in ax.get_xticklabels() + ax.get_yticklabels():
            label.set_fontproperties(alibaba_font)
    else:
        ax.set_title(title, fontsize=14)
        if xlabel:
            ax.set_xlabel(xlabel)
        if ylabel:
            ax.set_ylabel(ylabel)

def generate_fixed_chart_1():
    """宋庄镇基本情况综合分析 - 修复中文显示"""
    fig = plt.figure(figsize=(16, 10))
    gs = fig.add_gridspec(3, 4, hspace=0.3, wspace=0.3)
    
    # 主要指标柱状图
    ax1 = fig.add_subplot(gs[0, :2])
    categories = ['总面积\n(平方公里)', '常住人口\n(万人)', '耕地面积\n(万亩)', '艺术家\n(千人)', '年游客量\n(万人次)']
    # 确保中文标签正确处理
    categories = [cat.encode('utf-8').decode('utf-8') for cat in categories]
    values = [40, 5, 1.5, 5, 120]
    
    bars = ax1.bar(categories, values, color=COLORS[:5], alpha=0.8, edgecolor='white', linewidth=2)
    set_chinese_text(ax1, '宋庄镇基本情况概览', ylabel='数值')
    
    for bar, value in zip(bars, values):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + height*0.02,
                f'{value}', ha='center', va='bottom', fontweight='bold', fontsize=11)
    
    # 交通便利性雷达图
    ax2 = fig.add_subplot(gs[0, 2:], projection='polar')
    transport_aspects = ['高速公路', '公共交通', '地铁规划', '共享出行', '停车便利', '可达性']
    transport_aspects = [asp.encode('utf-8').decode('utf-8') for asp in transport_aspects]
    transport_scores = [4.5, 3.8, 3.2, 4.0, 3.5, 4.2]
    
    angles = np.linspace(0, 2*np.pi, len(transport_aspects), endpoint=False).tolist()
    transport_scores += transport_scores[:1]
    angles += angles[:1]
    
    ax2.plot(angles, transport_scores, 'o-', linewidth=3, color=COLORS[0], markersize=8)
    ax2.fill(angles, transport_scores, alpha=0.25, color=COLORS[0])
    ax2.set_xticks(angles[:-1])
    ax2.set_xticklabels(transport_aspects, fontsize=10)
    if alibaba_font:
        for label in ax2.get_xticklabels():
            label.set_fontproperties(alibaba_font)
    ax2.set_ylim(0, 5)
    if alibaba_font:
        ax2.set_title('交通便利性评估', fontsize=14, pad=30, fontproperties=alibaba_font)
    else:
        ax2.set_title('交通便利性评估', fontsize=14, pad=30)
    ax2.grid(True)
    
    # 产业结构饼图
    ax3 = fig.add_subplot(gs[1, :2])
    industries = ['农业生产', '艺术创意', '旅游服务', '商业服务', '其他产业']
    industries = [ind.encode('utf-8').decode('utf-8') for ind in industries]
    industry_values = [30, 35, 20, 10, 5]
    
    wedges, texts, autotexts = ax3.pie(industry_values, labels=industries, 
                                      colors=COLORS2[:5], autopct='%1.1f%%',
                                      startangle=90, textprops={'fontsize': 10})
    
    # 设置饼图标签字体
    if alibaba_font:
        for text in texts:
            text.set_fontproperties(alibaba_font)
        for autotext in autotexts:
            autotext.set_fontproperties(alibaba_font)
    
    set_chinese_text(ax3, '产业结构分布')
    
    # 发展趋势线图
    ax4 = fig.add_subplot(gs[1, 2:])
    years = list(range(2018, 2024))
    visitors = [80, 85, 92, 75, 88, 120]
    revenue = [1200, 1350, 1580, 1200, 1450, 1800]
    
    ax4_twin = ax4.twinx()
    
    line1 = ax4.plot(years, visitors, marker='o', linewidth=3, color=COLORS[1], 
                     markersize=8, label='年游客量')
    ax4.fill_between(years, visitors, alpha=0.3, color=COLORS[1])
    
    line2 = ax4_twin.plot(years, revenue, marker='s', linewidth=3, color=COLORS[3], 
                          markersize=8, label='旅游收入')
    
    set_chinese_text(ax4, '旅游发展趋势', xlabel='年份', ylabel='游客量(万人次)')
    set_chinese_text(ax4_twin, '', ylabel='收入(万元)')
    ax4.grid(True, alpha=0.3)
    
    # 合并图例
    lines1, labels1 = ax4.get_legend_handles_labels()
    lines2, labels2 = ax4_twin.get_legend_handles_labels()
    if alibaba_font:
        ax4.legend(lines1 + lines2, labels1 + labels2, loc='upper left', prop=alibaba_font)
    else:
        ax4.legend(lines1 + lines2, labels1 + labels2, loc='upper left')
    
    # 资源禀赋对比
    ax5 = fig.add_subplot(gs[2, :])
    resources = ['自然资源', '文化资源', '人力资源', '基础设施', '政策支持', '区位优势']
    resources = [res.encode('utf-8').decode('utf-8') for res in resources]
    songzhuang_scores = [3.8, 4.5, 3.5, 3.2, 4.2, 4.0]
    beijing_avg = [4.0, 3.8, 4.2, 4.5, 4.0, 3.5]
    
    x = np.arange(len(resources))
    width = 0.35
    
    bars1 = ax5.bar(x - width/2, songzhuang_scores, width, label='宋庄镇', 
                    color=COLORS[0], alpha=0.8, edgecolor='white', linewidth=1)
    bars2 = ax5.bar(x + width/2, beijing_avg, width, label='北京市平均', 
                    color=COLORS2[0], alpha=0.8, edgecolor='white', linewidth=1)
    
    set_chinese_text(ax5, '资源禀赋对比分析', xlabel='资源类型', ylabel='评分(满分5分)')
    ax5.set_xticks(x)
    ax5.set_xticklabels(resources, rotation=45)
    if alibaba_font:
        ax5.legend(prop=alibaba_font)
    else:
        ax5.legend()
    ax5.grid(True, alpha=0.3, axis='y')
    ax5.set_ylim(0, 5)
    
    # 添加数值标签
    for bars in [bars1, bars2]:
        for bar in bars:
            height = bar.get_height()
            ax5.text(bar.get_x() + bar.get_width()/2., height + 0.05,
                    f'{height}', ha='center', va='bottom', fontweight='bold', fontsize=9)
    
    # 设置整体标题
    if alibaba_font:
        plt.suptitle('宋庄镇农旅数字化项目基础条件综合分析', fontsize=18, fontweight='bold', y=0.98, fontproperties=alibaba_font)
    else:
        plt.suptitle('宋庄镇农旅数字化项目基础条件综合分析', fontsize=18, fontweight='bold', y=0.98)
    
    plt.tight_layout()
    return fig

def generate_fixed_charts():
    """生成修复中文显示的图表"""
    output_dir = create_output_dir()
    chart_count = 0
    
    # 清空现有图表
    for file in os.listdir(output_dir):
        if file.endswith('.png'):
            os.remove(os.path.join(output_dir, file))
    print("已清空现有图表，开始生成修复版中文图表...")
    
    # 生成修复版图表1
    try:
        fig1 = generate_fixed_chart_1()
        fig1.savefig(os.path.join(output_dir, 'chart_1_基本情况_1.png'), 
                    dpi=300, bbox_inches='tight', facecolor='white')
        plt.close(fig1)
        chart_count += 1
        print("✓ 图表1: 宋庄镇基本情况综合分析 (中文修复版)")
    except Exception as e:
        print(f"❌ 图表1生成失败: {e}")
    
    return chart_count

if __name__ == "__main__":
    try:
        count = generate_fixed_charts()
        print(f"\n✅ 成功生成 {count} 个修复版中文图表!")
        print("📝 请检查图表中的中文是否正确显示")
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()
