#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import os
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

# 小清新亮色配色方案
COLORS = {
    'primary': ['#FF9AA2', '#FFB7B2', '#FFDAC1', '#E2F0CB', '#B5EAD7', '#C7CEEA'],
    'secondary': ['#A8E6CF', '#DCEDC1', '#FFD3A5', '#FFA8A8', '#B5B5EA', '#F8BBD9'],
    'accent': ['#87CEEB', '#98FB98', '#F0E68C', '#FFA07A', '#DDA0DD', '#F5DEB3']
}

def create_output_dir():
    """创建输出目录"""
    output_dir = "output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/"
    os.makedirs(output_dir, exist_ok=True)
    return output_dir

def set_chart_style():
    """设置图表样式"""
    sns.set_style("whitegrid")
    plt.rcParams.update({
        'figure.facecolor': 'white',
        'axes.facecolor': 'white',
        'savefig.facecolor': 'white',
        'font.size': 10,
        'axes.titlesize': 14,
        'axes.labelsize': 12
    })

def generate_basic_charts():
    """生成基础图表"""
    set_chart_style()
    output_dir = create_output_dir()
    
    # 图表1: 宋庄镇基本情况
    fig1, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 8))
    
    # 基本数据柱状图
    categories = ['总面积\n(平方公里)', '常住人口\n(万人)', '耕地面积\n(万亩)', '艺术家\n(人)']
    values = [40, 5, 1.5, 5000]
    bars = ax1.bar(categories, values, color=COLORS['primary'][:4], alpha=0.8)
    ax1.set_title('宋庄镇基本情况概览', fontsize=14, pad=20)
    for bar, value in zip(bars, values):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                f'{value}', ha='center', va='bottom', fontweight='bold')
    
    # 交通时间
    transport = ['驾车', '公交', '地铁(规划)']
    time_data = [40, 60, 30]
    ax2.barh(transport, time_data, color=COLORS['secondary'][:3], alpha=0.8)
    ax2.set_title('到北京城区交通时间(分钟)', fontsize=14)
    ax2.set_xlabel('时间(分钟)')
    
    # 产业结构饼图
    industries = ['农业', '艺术创意', '旅游服务', '其他']
    industry_values = [30, 40, 20, 10]
    ax3.pie(industry_values, labels=industries, colors=COLORS['accent'][:4], 
            autopct='%1.1f%%', startangle=90)
    ax3.set_title('产业结构分布', fontsize=14)
    
    # 发展优势雷达图
    advantages = ['区位优势', '资源优势', '政策优势', '文化优势', '交通优势']
    scores = [4.5, 4.2, 4.8, 4.6, 4.0]
    angles = np.linspace(0, 2*np.pi, len(advantages), endpoint=False).tolist()
    scores += scores[:1]
    angles += angles[:1]
    ax4.plot(angles, scores, 'o-', linewidth=2, color=COLORS['primary'][0])
    ax4.fill(angles, scores, alpha=0.25, color=COLORS['primary'][0])
    ax4.set_xticks(angles[:-1])
    ax4.set_xticklabels(advantages)
    ax4.set_ylim(0, 5)
    ax4.set_title('发展优势评估(满分5分)', fontsize=14)
    ax4.grid(True)
    
    plt.tight_layout()
    fig1.savefig(os.path.join(output_dir, 'chart_1_基本情况_1.png'), 
                dpi=300, bbox_inches='tight', facecolor='white')
    plt.close(fig1)
    print("已生成: chart_1_基本情况_1.png")
    
    # 图表2: 需求分布
    fig2, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 8))
    
    needs = ['亲子农事体验', '文化创意活动', '健康饮食体验', '自然生态体验', '教育研学活动', '其他']
    percentages = [28, 25, 20, 15, 8, 4]
    ax1.pie(percentages, labels=needs, colors=COLORS['primary'] + COLORS['secondary'],
            autopct='%1.1f%%', startangle=90)
    ax1.set_title('城区家庭周末休闲需求分布', fontsize=14)
    
    frequency = ['每周1次', '每月2-3次', '每月1次', '季度1次', '不定期']
    freq_values = [15, 35, 30, 12, 8]
    bars = ax2.bar(frequency, freq_values, color=COLORS['accent'][:5], alpha=0.8)
    ax2.set_title('周末郊区休闲频次分布(%)', fontsize=14)
    ax2.set_ylabel('占比(%)')
    ax2.tick_params(axis='x', rotation=45)
    for bar, value in zip(bars, freq_values):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{value}%', ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    fig2.savefig(os.path.join(output_dir, 'chart_1_需求分布_2.png'), 
                dpi=300, bbox_inches='tight', facecolor='white')
    plt.close(fig2)
    print("已生成: chart_1_需求分布_2.png")
    
    # 图表3: 市场趋势
    fig3, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
    
    years = list(range(2018, 2026))
    market_size = [1200, 1450, 1750, 2100, 2500, 3000, 3600, 4300]
    growth_rate = [0, 20.8, 20.7, 20.0, 19.0, 20.0, 20.0, 19.4]
    
    ax1.plot(years, market_size, marker='o', linewidth=3, color=COLORS['primary'][0], markersize=8)
    ax1.fill_between(years, market_size, alpha=0.3, color=COLORS['primary'][0])
    ax1.set_title('中国数字农业市场规模增长趋势', fontsize=14)
    ax1.set_ylabel('市场规模(亿元)')
    ax1.grid(True, alpha=0.3)
    
    ax1_twin = ax1.twinx()
    ax1_twin.bar(years[1:], growth_rate[1:], alpha=0.6, color=COLORS['secondary'][1], width=0.6)
    ax1_twin.set_ylabel('增长率(%)')
    
    tourism_years = list(range(2020, 2026))
    penetration = [35, 45, 58, 68, 75, 82]
    ax2.plot(tourism_years, penetration, marker='s', linewidth=3, color=COLORS['accent'][2], markersize=8)
    ax2.fill_between(tourism_years, penetration, alpha=0.3, color=COLORS['accent'][2])
    ax2.set_title('智慧旅游渗透率发展趋势', fontsize=14)
    ax2.set_xlabel('年份')
    ax2.set_ylabel('渗透率(%)')
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    fig3.savefig(os.path.join(output_dir, 'chart_1_市场趋势_3.png'), 
                dpi=300, bbox_inches='tight', facecolor='white')
    plt.close(fig3)
    print("已生成: chart_1_市场趋势_3.png")
    
    return 3

if __name__ == "__main__":
    count = generate_basic_charts()
    print(f"\n总共生成了 {count} 个基础图表")
