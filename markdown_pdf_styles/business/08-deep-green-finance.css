/*
 * 商务系列 08 - 深绿金融
 * Deep Green Finance Style
 * 
 * 特色：深绿色主题，金融专业感，适合银行、投资、财务报告
 * 设计理念：深绿色的稳重与金色的高贵，营造财富与信任感
 */

@import url('./common/base.css');
@import url('./common/variables.css');

:root {
  /* 主题色彩 */
  --primary-color: #1b5e20;
  --secondary-color: #2e7d32;
  --accent-color: #4caf50;
  --gold-accent: #ffc107;
  --text-primary: #1b5e20;
  --text-secondary: #388e3c;
  --background-color: #fefffe;
  --surface-color: #f1f8e9;
  --border-color: #c8e6c9;
}

/* 页面整体样式 */
body {
  background-color: var(--background-color);
  color: var(--text-primary);
  font-weight: var(--font-weight-normal);
  letter-spacing: 0.01em;
  line-height: 1.65;
}

/* 标题样式系统 */
h1 {
  color: var(--primary-color);
  font-weight: var(--font-weight-bold);
  font-size: 2.8rem;
  margin-bottom: var(--spacing-2xl);
  position: relative;
  text-align: center;
  padding: var(--spacing-lg) 0;
}

h1::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 150px;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--gold-accent), transparent);
}

h1::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--accent-color), transparent);
}

h2 {
  color: var(--secondary-color);
  font-weight: var(--font-weight-medium);
  background: linear-gradient(135deg, var(--surface-color), rgba(76, 175, 80, 0.05));
  padding: var(--spacing-lg) var(--spacing-2xl);
  border-radius: var(--radius-lg);
  margin: var(--spacing-2xl) 0 var(--spacing-xl) 0;
  position: relative;
  border-left: 5px solid var(--accent-color);
  box-shadow: 0 3px 15px rgba(76, 175, 80, 0.1);
}

h2::before {
  content: '$';
  position: absolute;
  right: var(--spacing-lg);
  top: 50%;
  transform: translateY(-50%);
  color: var(--gold-accent);
  font-size: 1.5em;
  font-weight: bold;
}

h3 {
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
  position: relative;
  padding-left: var(--spacing-lg);
  margin-top: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
}

h3::before {
  content: '▲';
  position: absolute;
  left: 0;
  color: var(--accent-color);
  font-size: 0.8em;
}

h4 {
  color: var(--accent-color);
  font-weight: var(--font-weight-medium);
  border-bottom: 2px solid var(--gold-accent);
  padding-bottom: var(--spacing-xs);
  display: inline-block;
  margin-bottom: var(--spacing-md);
}

h5, h6 {
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

/* 段落优化 */
p {
  line-height: 1.75;
  margin-bottom: var(--spacing-lg);
  text-align: justify;
}

/* 强调文本 */
strong, b {
  color: var(--primary-color);
  font-weight: var(--font-weight-bold);
}

em, i {
  color: var(--gold-accent);
  font-style: italic;
}

/* 列表样式 */
ul {
  list-style: none;
  padding-left: 0;
}

ul li {
  position: relative;
  padding-left: var(--spacing-xl);
  margin-bottom: var(--spacing-md);
  line-height: 1.7;
}

ul li::before {
  content: '●';
  position: absolute;
  left: var(--spacing-sm);
  color: var(--accent-color);
  font-size: 1.2em;
}

ol {
  counter-reset: item;
  padding-left: 0;
}

ol li {
  position: relative;
  padding-left: var(--spacing-2xl);
  margin-bottom: var(--spacing-md);
  counter-increment: item;
  line-height: 1.7;
}

ol li::before {
  content: counter(item);
  position: absolute;
  left: 0;
  top: 0;
  background: linear-gradient(135deg, var(--accent-color), var(--secondary-color));
  color: white;
  width: 26px;
  height: 26px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-bold);
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
  border: 2px solid var(--gold-accent);
}

/* 引用块样式 */
blockquote {
  background: linear-gradient(135deg, var(--surface-color), rgba(76, 175, 80, 0.03));
  border: none;
  border-left: 5px solid var(--accent-color);
  border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
  padding: var(--spacing-xl) var(--spacing-2xl);
  margin: var(--spacing-2xl) 0;
  position: relative;
  box-shadow: 0 4px 20px rgba(76, 175, 80, 0.1);
}

blockquote::before {
  content: '💰';
  position: absolute;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  font-size: 1.5rem;
  opacity: 0.4;
}

blockquote p {
  margin-bottom: 0;
  font-style: italic;
  color: var(--text-secondary);
  line-height: 1.6;
}

/* 代码样式 */
code {
  background: rgba(76, 175, 80, 0.08);
  color: var(--primary-color);
  padding: 0.2em 0.5em;
  border-radius: var(--radius-md);
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.9em;
  border: 1px solid rgba(76, 175, 80, 0.2);
}

pre {
  background: linear-gradient(135deg, var(--surface-color), rgba(76, 175, 80, 0.02));
  border: 1px solid rgba(76, 175, 80, 0.2);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  overflow-x: auto;
  margin: var(--spacing-2xl) 0;
  box-shadow: 0 3px 15px rgba(76, 175, 80, 0.1);
  position: relative;
}

pre::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--accent-color), var(--gold-accent));
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

pre code {
  background: transparent;
  border: none;
  padding: 0;
  color: var(--text-primary);
}

/* 表格样式 */
table {
  border-collapse: separate;
  border-spacing: 0;
  background: var(--background-color);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: 0 6px 25px rgba(76, 175, 80, 0.15);
  margin: var(--spacing-2xl) 0;
}

th {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-lg);
  text-align: left;
  border: none;
  position: relative;
}

th::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--gold-accent);
}

td {
  padding: var(--spacing-lg);
  border-bottom: 1px solid rgba(76, 175, 80, 0.1);
  border-right: 1px solid rgba(76, 175, 80, 0.1);
}

td:last-child {
  border-right: none;
}

tr:last-child td {
  border-bottom: none;
}

tr:nth-child(even) {
  background: rgba(76, 175, 80, 0.03);
}

/* 数字高亮 */
td:has-text('$'), td:has-text('¥'), td:has-text('%') {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
}

/* 链接样式 */
a {
  color: var(--accent-color);
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: var(--transition-normal);
}

a:hover {
  color: var(--gold-accent);
  border-bottom-color: var(--gold-accent);
}

/* 分隔线 */
hr {
  border: none;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--accent-color), var(--gold-accent), transparent);
  margin: var(--spacing-3xl) 0;
  border-radius: var(--radius-full);
}

/* 图片样式 */
img {
  border-radius: var(--radius-lg);
  box-shadow: 0 6px 25px rgba(76, 175, 80, 0.2);
  margin: var(--spacing-xl) 0;
}

/* 打印优化 */
@media print {
  body {
    color: #000000;
    background: #ffffff;
  }
  
  h1 {
    color: #000000;
  }
  
  h1::before,
  h1::after {
    background: linear-gradient(90deg, transparent, #666666, transparent);
  }
  
  h2 {
    color: #000000;
    background: #f5f5f5;
    border-left: 3pt solid #666666;
    box-shadow: none;
  }
  
  h2::before {
    color: #999999;
  }
  
  h3::before {
    color: #666666;
  }
  
  h4 {
    color: #333333;
    border-bottom-color: #999999;
  }
  
  ul li::before {
    color: #666666;
  }
  
  ol li::before {
    background: linear-gradient(135deg, #666666, #333333);
    border-color: #999999;
    box-shadow: none;
  }
  
  blockquote {
    background: #f8f8f8;
    border-left: 3pt solid #666666;
    box-shadow: none;
  }
  
  blockquote::before {
    content: '';
  }
  
  code {
    background: #f0f0f0;
    color: #000000;
    border: 1pt solid #cccccc;
  }
  
  pre {
    background: #f8f8f8;
    border: 1pt solid #cccccc;
    box-shadow: none;
  }
  
  pre::before {
    background: linear-gradient(90deg, #666666, #999999);
  }
  
  table {
    box-shadow: none;
  }
  
  th {
    background: #e0e0e0;
    color: #000000;
  }
  
  th::after {
    background: #999999;
  }
  
  tr:nth-child(even) {
    background: #f5f5f5;
  }
  
  a {
    color: #000000;
    border-bottom: 1pt solid #666666;
  }
  
  hr {
    background: linear-gradient(90deg, transparent, #666666, #999999, transparent);
  }
  
  img {
    box-shadow: none;
  }
}

