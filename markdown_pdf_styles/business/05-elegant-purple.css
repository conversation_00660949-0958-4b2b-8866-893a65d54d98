/*
 * 商务系列 05 - 优雅紫调
 * Elegant Purple Business Style
 * 
 * 特色：优雅紫色主题，高端奢华感，适合高端服务、咨询行业
 * 设计理念：紫色的神秘与优雅，营造高端商务氛围
 */

@import url('./common/base.css');
@import url('./common/variables.css');

:root {
  /* 主题色彩 */
  --primary-color: #663399;
  --secondary-color: #8e44ad;
  --accent-color: #9b59b6;
  --luxury-gold: #f39c12;
  --text-primary: #2c3e50;
  --text-secondary: #7f8c8d;
  --background-color: #fefefe;
  --surface-color: #f8f6ff;
  --border-color: #e8e2f0;
}

/* 页面整体样式 */
body {
  background-color: var(--background-color);
  color: var(--text-primary);
  font-weight: var(--font-weight-normal);
  letter-spacing: 0.02em;
  line-height: 1.65;
}

/* 标题样式系统 */
h1 {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  font-size: 3rem;
  text-align: center;
  margin-bottom: var(--spacing-3xl);
  position: relative;
  padding: var(--spacing-xl) 0;
}

h1::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 120px;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--accent-color), transparent);
}

h1::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--luxury-gold), transparent);
}

h2 {
  color: var(--secondary-color);
  font-weight: var(--font-weight-medium);
  background: linear-gradient(135deg, var(--surface-color), rgba(155, 89, 182, 0.05));
  padding: var(--spacing-lg) var(--spacing-2xl);
  border-radius: var(--radius-xl);
  margin: var(--spacing-2xl) 0 var(--spacing-xl) 0;
  position: relative;
  box-shadow: 0 4px 20px rgba(155, 89, 182, 0.1);
}

h2::before {
  content: '◆';
  position: absolute;
  left: var(--spacing-lg);
  top: 50%;
  transform: translateY(-50%);
  color: var(--luxury-gold);
  font-size: 0.8em;
}

h3 {
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
  position: relative;
  padding-left: var(--spacing-xl);
  margin-top: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
}

h3::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 12px;
  height: 12px;
  background: linear-gradient(45deg, var(--accent-color), var(--luxury-gold));
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(155, 89, 182, 0.3);
}

h4 {
  color: var(--accent-color);
  font-weight: var(--font-weight-medium);
  font-style: italic;
  border-bottom: 1px solid var(--accent-color);
  padding-bottom: var(--spacing-xs);
  display: inline-block;
}

h5, h6 {
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
  font-variant: small-caps;
  letter-spacing: 0.05em;
}

/* 段落优化 */
p {
  line-height: 1.75;
  margin-bottom: var(--spacing-lg);
  text-align: justify;
}

/* 强调文本 */
strong, b {
  color: var(--primary-color);
  font-weight: var(--font-weight-bold);
}

em, i {
  color: var(--luxury-gold);
  font-style: italic;
}

/* 列表样式 */
ul {
  list-style: none;
  padding-left: 0;
}

ul li {
  position: relative;
  padding-left: var(--spacing-xl);
  margin-bottom: var(--spacing-md);
  line-height: 1.7;
}

ul li::before {
  content: '◇';
  position: absolute;
  left: var(--spacing-sm);
  color: var(--accent-color);
  font-size: 1.1em;
}

ol {
  counter-reset: item;
  padding-left: 0;
}

ol li {
  position: relative;
  padding-left: var(--spacing-2xl);
  margin-bottom: var(--spacing-md);
  counter-increment: item;
  line-height: 1.7;
}

ol li::before {
  content: counter(item);
  position: absolute;
  left: 0;
  top: 0;
  background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
  color: white;
  width: 26px;
  height: 26px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-medium);
  box-shadow: 0 3px 10px rgba(155, 89, 182, 0.3);
  border: 2px solid var(--luxury-gold);
}

/* 引用块样式 */
blockquote {
  background: linear-gradient(135deg, var(--surface-color), rgba(155, 89, 182, 0.03));
  border: none;
  border-left: 4px solid var(--accent-color);
  border-radius: 0 var(--radius-xl) var(--radius-xl) 0;
  padding: var(--spacing-xl) var(--spacing-2xl);
  margin: var(--spacing-2xl) 0;
  position: relative;
  box-shadow: 0 6px 25px rgba(155, 89, 182, 0.1);
}

blockquote::before {
  content: '"';
  position: absolute;
  top: -5px;
  left: var(--spacing-lg);
  font-size: 4rem;
  color: var(--luxury-gold);
  opacity: 0.3;
  font-family: Georgia, serif;
  line-height: 1;
}

blockquote::after {
  content: '';
  position: absolute;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  width: 30px;
  height: 30px;
  background: radial-gradient(circle, var(--luxury-gold), transparent);
  border-radius: 50%;
  opacity: 0.2;
}

blockquote p {
  margin-bottom: 0;
  font-style: italic;
  color: var(--text-secondary);
  line-height: 1.6;
  position: relative;
  z-index: 1;
}

/* 代码样式 */
code {
  background: rgba(155, 89, 182, 0.08);
  color: var(--primary-color);
  padding: 0.25em 0.5em;
  border-radius: var(--radius-md);
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.9em;
  border: 1px solid rgba(155, 89, 182, 0.2);
}

pre {
  background: linear-gradient(135deg, var(--surface-color), rgba(155, 89, 182, 0.02));
  border: 1px solid rgba(155, 89, 182, 0.2);
  border-radius: var(--radius-xl);
  padding: var(--spacing-2xl);
  overflow-x: auto;
  margin: var(--spacing-2xl) 0;
  box-shadow: 0 4px 20px rgba(155, 89, 182, 0.1);
  position: relative;
}

pre::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--accent-color), var(--luxury-gold), var(--accent-color));
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

pre code {
  background: transparent;
  border: none;
  padding: 0;
  color: var(--text-primary);
}

/* 表格样式 */
table {
  border-collapse: separate;
  border-spacing: 0;
  background: var(--background-color);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: 0 8px 30px rgba(155, 89, 182, 0.15);
  margin: var(--spacing-2xl) 0;
}

th {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-lg) var(--spacing-xl);
  text-align: left;
  border: none;
  position: relative;
}

th::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--luxury-gold);
}

td {
  padding: var(--spacing-lg) var(--spacing-xl);
  border-bottom: 1px solid rgba(155, 89, 182, 0.1);
  border-right: 1px solid rgba(155, 89, 182, 0.1);
}

td:last-child {
  border-right: none;
}

tr:last-child td {
  border-bottom: none;
}

tr:nth-child(even) {
  background: rgba(155, 89, 182, 0.03);
}

/* 链接样式 */
a {
  color: var(--accent-color);
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: var(--transition-normal);
  position: relative;
}

a:hover {
  color: var(--luxury-gold);
  border-bottom-color: var(--luxury-gold);
}

/* 分隔线 */
hr {
  border: none;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--accent-color), var(--luxury-gold), var(--accent-color), transparent);
  margin: var(--spacing-3xl) 0;
  position: relative;
}

hr::before {
  content: '◆';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  background: var(--background-color);
  color: var(--luxury-gold);
  padding: 0 var(--spacing-sm);
  font-size: 0.8em;
}

/* 图片样式 */
img {
  border-radius: var(--radius-xl);
  box-shadow: 0 8px 30px rgba(155, 89, 182, 0.2);
  margin: var(--spacing-xl) 0;
  border: 3px solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(45deg, var(--accent-color), var(--luxury-gold)) border-box;
}

/* 打印优化 */
@media print {
  body {
    color: #000000;
    background: #ffffff;
  }
  
  h1 {
    color: #000000;
  }
  
  h1::before,
  h1::after {
    background: linear-gradient(90deg, transparent, #666666, transparent);
  }
  
  h2 {
    color: #000000;
    background: #f5f5f5;
    box-shadow: none;
  }
  
  h2::before {
    color: #666666;
  }
  
  h3::before {
    background: linear-gradient(45deg, #666666, #999999);
    box-shadow: none;
  }
  
  h4 {
    color: #333333;
    border-bottom-color: #666666;
  }
  
  ul li::before {
    color: #666666;
  }
  
  ol li::before {
    background: linear-gradient(135deg, #666666, #333333);
    border-color: #999999;
    box-shadow: none;
  }
  
  blockquote {
    background: #f8f8f8;
    border-left: 3pt solid #666666;
    box-shadow: none;
  }
  
  blockquote::before {
    color: #cccccc;
  }
  
  blockquote::after {
    display: none;
  }
  
  code {
    background: #f0f0f0;
    color: #000000;
    border: 1pt solid #cccccc;
  }
  
  pre {
    background: #f8f8f8;
    border: 1pt solid #cccccc;
    box-shadow: none;
  }
  
  pre::before {
    background: linear-gradient(90deg, #666666, #999999, #666666);
  }
  
  table {
    box-shadow: none;
  }
  
  th {
    background: #e0e0e0;
    color: #000000;
  }
  
  th::after {
    background: #999999;
  }
  
  tr:nth-child(even) {
    background: #f5f5f5;
  }
  
  a {
    color: #000000;
    border-bottom: 1pt solid #666666;
  }
  
  hr {
    background: linear-gradient(90deg, transparent, #666666, #999999, #666666, transparent);
  }
  
  hr::before {
    background: #ffffff;
    color: #999999;
  }
  
  img {
    box-shadow: none;
    border: 2pt solid #cccccc;
    background: none;
  }
}

