/*
 * 商务系列 02 - 极简灰度
 * Minimal Grayscale Business Style
 * 
 * 特色：极简主义设计，灰度色彩，强调内容本身
 * 设计理念：少即是多，通过留白和层次营造高级感
 */

@import url('./common/base.css');
@import url('./common/variables.css');

:root {
  /* 主题色彩 */
  --primary-color: var(--minimal-black);
  --secondary-color: var(--minimal-dark-gray);
  --accent-color: var(--minimal-medium-gray);
  --text-primary: var(--minimal-black);
  --text-secondary: var(--minimal-dark-gray);
  --background-color: var(--minimal-white);
  --surface-color: var(--minimal-off-white);
  --border-color: var(--minimal-light-gray);
}

/* 页面整体样式 */
body {
  background-color: var(--background-color);
  color: var(--text-primary);
  font-weight: var(--font-weight-light);
  letter-spacing: 0.02em;
  line-height: 1.8;
}

/* 标题样式系统 */
h1 {
  color: var(--primary-color);
  font-weight: var(--font-weight-light);
  font-size: 3rem;
  letter-spacing: -0.02em;
  margin-bottom: var(--spacing-3xl);
  text-align: center;
  position: relative;
}

h1::after {
  content: '';
  position: absolute;
  bottom: -var(--spacing-lg);
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 1px;
  background: var(--accent-color);
}

h2 {
  color: var(--primary-color);
  font-weight: var(--font-weight-normal);
  font-size: 2.2rem;
  letter-spacing: -0.01em;
  margin-top: var(--spacing-3xl);
  margin-bottom: var(--spacing-2xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

h3 {
  color: var(--secondary-color);
  font-weight: var(--font-weight-normal);
  font-size: 1.6rem;
  margin-top: var(--spacing-2xl);
  margin-bottom: var(--spacing-xl);
}

h4 {
  color: var(--secondary-color);
  font-weight: var(--font-weight-medium);
  font-size: 1.3rem;
  margin-top: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
}

h5, h6 {
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.1em;
  font-size: 1rem;
}

/* 段落优化 */
p {
  line-height: 1.9;
  margin-bottom: var(--spacing-xl);
  color: var(--text-primary);
}

/* 强调文本 */
strong, b {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
}

em, i {
  color: var(--text-secondary);
  font-style: italic;
}

/* 列表样式 */
ul {
  list-style: none;
  padding-left: 0;
}

ul li {
  position: relative;
  padding-left: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  line-height: 1.8;
}

ul li::before {
  content: '—';
  position: absolute;
  left: 0;
  color: var(--accent-color);
  font-weight: normal;
}

ol {
  counter-reset: item;
  padding-left: 0;
}

ol li {
  position: relative;
  padding-left: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
  counter-increment: item;
  line-height: 1.8;
}

ol li::before {
  content: counter(item, decimal-leading-zero);
  position: absolute;
  left: 0;
  top: 0;
  color: var(--accent-color);
  font-weight: var(--font-weight-light);
  font-size: var(--font-size-small);
}

/* 引用块样式 */
blockquote {
  background: transparent;
  border: none;
  border-left: 2px solid var(--accent-color);
  padding: 0 0 0 var(--spacing-2xl);
  margin: var(--spacing-3xl) 0;
  font-style: italic;
  font-size: 1.1rem;
  line-height: 1.7;
  color: var(--text-secondary);
}

blockquote p {
  margin-bottom: 0;
}

/* 代码样式 */
code {
  background: var(--surface-color);
  color: var(--text-primary);
  padding: 0.2em 0.5em;
  border-radius: var(--radius-sm);
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.9em;
  font-weight: var(--font-weight-normal);
}

pre {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-2xl);
  overflow-x: auto;
  margin: var(--spacing-2xl) 0;
}

pre code {
  background: transparent;
  padding: 0;
  color: var(--text-primary);
}

/* 表格样式 */
table {
  border-collapse: collapse;
  margin: var(--spacing-2xl) 0;
  width: 100%;
}

th {
  background: transparent;
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-lg) var(--spacing-md);
  text-align: left;
  border-bottom: 2px solid var(--border-color);
  font-size: var(--font-size-small);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

td {
  padding: var(--spacing-lg) var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  color: var(--text-primary);
}

tr:last-child td {
  border-bottom: none;
}

/* 链接样式 */
a {
  color: var(--text-primary);
  text-decoration: none;
  border-bottom: 1px solid var(--accent-color);
  transition: var(--transition-normal);
}

a:hover {
  border-bottom-color: var(--primary-color);
}

/* 分隔线 */
hr {
  border: none;
  height: 1px;
  background: var(--border-color);
  margin: var(--spacing-3xl) auto;
  width: 50%;
}

/* 图片样式 */
img {
  border-radius: 0;
  margin: var(--spacing-2xl) 0;
  filter: grayscale(20%);
}

/* 打印优化 */
@media print {
  body {
    color: #000000;
    background: #ffffff;
    font-weight: 400;
  }
  
  h1 {
    color: #000000;
    font-weight: 300;
  }
  
  h1::after {
    background: #cccccc;
  }
  
  h2 {
    color: #000000;
    border-bottom: 1pt solid #cccccc;
  }
  
  h3, h4 {
    color: #333333;
  }
  
  h5, h6 {
    color: #666666;
  }
  
  ul li::before {
    color: #666666;
  }
  
  ol li::before {
    color: #666666;
  }
  
  blockquote {
    border-left: 2pt solid #cccccc;
    color: #666666;
  }
  
  code {
    background: #f5f5f5;
    color: #000000;
  }
  
  pre {
    background: #f8f8f8;
    border: 1pt solid #cccccc;
  }
  
  th {
    border-bottom: 2pt solid #cccccc;
    color: #000000;
  }
  
  td {
    border-bottom: 1pt solid #cccccc;
    color: #000000;
  }
  
  a {
    color: #000000;
    border-bottom: 1pt solid #666666;
  }
  
  hr {
    background: #cccccc;
  }
  
  img {
    filter: none;
  }
}

