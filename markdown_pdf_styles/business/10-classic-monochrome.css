/*
 * 商务系列 10 - 经典黑白
 * Classic Monochrome Business Style
 * 
 * 特色：经典黑白配色，永恒优雅，适合正式文档、法律、学术报告
 * 设计理念：黑白的经典永恒，通过排版层次营造专业权威感
 */

@import url('./common/base.css');
@import url('./common/variables.css');

:root {
  /* 主题色彩 */
  --primary-color: #000000;
  --secondary-color: #333333;
  --accent-color: #666666;
  --light-gray: #999999;
  --text-primary: #000000;
  --text-secondary: #333333;
  --background-color: #ffffff;
  --surface-color: #fafafa;
  --border-color: #e0e0e0;
}

/* 页面整体样式 */
body {
  background-color: var(--background-color);
  color: var(--text-primary);
  font-weight: var(--font-weight-normal);
  letter-spacing: 0.02em;
  line-height: 1.75;
}

/* 标题样式系统 */
h1 {
  color: var(--primary-color);
  font-weight: var(--font-weight-bold);
  font-size: 3.5rem;
  margin-bottom: var(--spacing-3xl);
  text-align: center;
  position: relative;
  padding: var(--spacing-2xl) 0;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

h1::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 200px;
  height: 2px;
  background: var(--primary-color);
}

h1::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 2px;
  background: var(--accent-color);
}

h2 {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  font-size: 2.2rem;
  margin: var(--spacing-3xl) 0 var(--spacing-xl) 0;
  position: relative;
  padding-bottom: var(--spacing-lg);
  border-bottom: 3px solid var(--primary-color);
}

h2::after {
  content: '';
  position: absolute;
  bottom: -3px;
  right: 0;
  width: 30%;
  height: 3px;
  background: var(--accent-color);
}

h3 {
  color: var(--secondary-color);
  font-weight: var(--font-weight-medium);
  font-size: 1.6rem;
  margin-top: var(--spacing-2xl);
  margin-bottom: var(--spacing-lg);
  position: relative;
  padding-left: var(--spacing-lg);
}

h3::before {
  content: '■';
  position: absolute;
  left: 0;
  color: var(--primary-color);
  font-size: 0.8em;
}

h4 {
  color: var(--secondary-color);
  font-weight: var(--font-weight-medium);
  font-size: 1.3rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid var(--accent-color);
  padding-bottom: var(--spacing-xs);
  display: inline-block;
  margin-bottom: var(--spacing-md);
}

h5 {
  color: var(--accent-color);
  font-weight: var(--font-weight-medium);
  font-size: 1.1rem;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

h6 {
  color: var(--light-gray);
  font-weight: var(--font-weight-medium);
  font-size: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.15em;
}

/* 段落优化 */
p {
  line-height: 1.8;
  margin-bottom: var(--spacing-lg);
  text-align: justify;
  text-indent: 2em;
}

p:first-of-type {
  text-indent: 0;
}

/* 强调文本 */
strong, b {
  color: var(--primary-color);
  font-weight: var(--font-weight-bold);
  background: linear-gradient(180deg, transparent 60%, #f0f0f0 60%);
}

em, i {
  color: var(--secondary-color);
  font-style: italic;
  border-bottom: 1px dotted var(--accent-color);
}

/* 列表样式 */
ul {
  list-style: none;
  padding-left: 0;
}

ul li {
  position: relative;
  padding-left: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
  line-height: 1.7;
}

ul li::before {
  content: '▪';
  position: absolute;
  left: var(--spacing-sm);
  color: var(--primary-color);
  font-size: 1.5em;
  line-height: 1;
}

ol {
  counter-reset: item;
  padding-left: 0;
}

ol li {
  position: relative;
  padding-left: var(--spacing-2xl);
  margin-bottom: var(--spacing-lg);
  counter-increment: item;
  line-height: 1.7;
}

ol li::before {
  content: counter(item, upper-roman) '.';
  position: absolute;
  left: 0;
  top: 0;
  color: var(--primary-color);
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-small);
  min-width: 24px;
}

/* 引用块样式 */
blockquote {
  background: transparent;
  border: none;
  border-left: 5px solid var(--primary-color);
  padding: 0 0 0 var(--spacing-2xl);
  margin: var(--spacing-3xl) 0;
  position: relative;
}

blockquote::before {
  content: '"';
  position: absolute;
  top: -20px;
  left: var(--spacing-lg);
  font-size: 4rem;
  color: var(--accent-color);
  opacity: 0.3;
  font-family: Georgia, serif;
  line-height: 1;
}

blockquote p {
  margin-bottom: 0;
  font-style: italic;
  color: var(--secondary-color);
  line-height: 1.6;
  font-size: 1.1rem;
  text-indent: 0;
}

/* 代码样式 */
code {
  background: var(--surface-color);
  color: var(--primary-color);
  padding: 0.2em 0.4em;
  border-radius: 0;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.9em;
  border: 1px solid var(--border-color);
  font-weight: var(--font-weight-medium);
}

pre {
  background: var(--surface-color);
  border: 2px solid var(--primary-color);
  border-radius: 0;
  padding: var(--spacing-xl);
  overflow-x: auto;
  margin: var(--spacing-2xl) 0;
  position: relative;
}

pre::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  height: 6px;
  background: var(--primary-color);
}

pre code {
  background: transparent;
  border: none;
  padding: 0;
  color: var(--text-primary);
}

/* 表格样式 */
table {
  border-collapse: collapse;
  margin: var(--spacing-2xl) 0;
  width: 100%;
  background: var(--background-color);
  border: 2px solid var(--primary-color);
}

th {
  background: var(--primary-color);
  color: white;
  font-weight: var(--font-weight-bold);
  padding: var(--spacing-lg);
  text-align: left;
  border: none;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

td {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  border-right: 1px solid var(--border-color);
}

td:last-child {
  border-right: none;
}

tr:last-child td {
  border-bottom: none;
}

tr:nth-child(even) {
  background: var(--surface-color);
}

/* 链接样式 */
a {
  color: var(--primary-color);
  text-decoration: none;
  border-bottom: 2px solid var(--primary-color);
  transition: var(--transition-normal);
  font-weight: var(--font-weight-medium);
}

a:hover {
  background: var(--primary-color);
  color: white;
}

/* 分隔线 */
hr {
  border: none;
  height: 3px;
  background: var(--primary-color);
  margin: var(--spacing-3xl) 0;
  position: relative;
}

hr::before {
  content: '◆';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  background: var(--background-color);
  color: var(--primary-color);
  padding: 0 var(--spacing-md);
  font-size: 1.2em;
}

/* 图片样式 */
img {
  border-radius: 0;
  margin: var(--spacing-2xl) 0;
  border: 3px solid var(--primary-color);
  filter: grayscale(100%);
}

/* 特殊元素 */
.signature {
  text-align: right;
  margin-top: var(--spacing-3xl);
  padding-top: var(--spacing-lg);
  border-top: 2px solid var(--primary-color);
  font-style: italic;
  color: var(--secondary-color);
}

/* 打印优化 */
@media print {
  body {
    color: #000000;
    background: #ffffff;
  }
  
  h1::before {
    background: #000000;
  }
  
  h1::after {
    background: #666666;
  }
  
  h2 {
    border-bottom-color: #000000;
  }
  
  h2::after {
    background: #666666;
  }
  
  h3::before {
    color: #000000;
  }
  
  h4 {
    border-bottom-color: #666666;
  }
  
  strong, b {
    background: linear-gradient(180deg, transparent 60%, #f0f0f0 60%);
  }
  
  em, i {
    border-bottom: 1pt dotted #666666;
  }
  
  ul li::before {
    color: #000000;
  }
  
  ol li::before {
    color: #000000;
  }
  
  blockquote {
    border-left: 3pt solid #000000;
  }
  
  blockquote::before {
    color: #cccccc;
  }
  
  code {
    background: #f5f5f5;
    border: 1pt solid #cccccc;
  }
  
  pre {
    background: #f8f8f8;
    border: 2pt solid #000000;
  }
  
  pre::before {
    background: #000000;
  }
  
  table {
    border: 2pt solid #000000;
  }
  
  th {
    background: #000000;
    color: #ffffff;
  }
  
  td {
    border-bottom: 1pt solid #cccccc;
    border-right: 1pt solid #cccccc;
  }
  
  tr:nth-child(even) {
    background: #f5f5f5;
  }
  
  a {
    color: #000000;
    border-bottom: 2pt solid #000000;
  }
  
  a:hover {
    background: none;
    color: #000000;
  }
  
  hr {
    background: #000000;
  }
  
  hr::before {
    background: #ffffff;
    color: #000000;
  }
  
  img {
    border: 2pt solid #000000;
    filter: grayscale(100%);
  }
}

