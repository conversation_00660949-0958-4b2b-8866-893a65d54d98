/*
 * 商务系列 07 - 暖灰石墨
 * Warm Charcoal Business Style
 * 
 * 特色：暖灰色调，石墨质感，现代简约
 * 设计理念：温暖的灰色调营造亲和力，同时保持专业感
 */

@import url('./common/base.css');
@import url('./common/variables.css');

:root {
  /* 主题色彩 */
  --primary-color: #424242;
  --secondary-color: #616161;
  --accent-color: #ff7043;
  --warm-gray: #78909c;
  --text-primary: #212121;
  --text-secondary: #757575;
  --background-color: #fefefe;
  --surface-color: #f9f9f9;
  --border-color: #e0e0e0;
}

/* 页面整体样式 */
body {
  background-color: var(--background-color);
  color: var(--text-primary);
  font-weight: var(--font-weight-normal);
  letter-spacing: 0.015em;
  line-height: 1.7;
}

/* 标题样式系统 */
h1 {
  color: var(--primary-color);
  font-weight: var(--font-weight-light);
  font-size: 3.2rem;
  margin-bottom: var(--spacing-3xl);
  position: relative;
  padding-left: var(--spacing-lg);
}

h1::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(180deg, var(--accent-color), var(--warm-gray));
  border-radius: var(--radius-full);
}

h2 {
  color: var(--secondary-color);
  font-weight: var(--font-weight-normal);
  background: var(--surface-color);
  padding: var(--spacing-lg) var(--spacing-xl);
  border-radius: var(--radius-lg);
  margin: var(--spacing-2xl) 0 var(--spacing-xl) 0;
  position: relative;
  border-top: 3px solid var(--accent-color);
}

h2::after {
  content: '';
  position: absolute;
  top: -3px;
  right: 0;
  width: 30%;
  height: 3px;
  background: var(--warm-gray);
}

h3 {
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
  position: relative;
  margin-top: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-xs);
  border-bottom: 1px solid var(--border-color);
}

h3::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 40px;
  height: 1px;
  background: var(--accent-color);
}

h4 {
  color: var(--warm-gray);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.1em;
  font-size: 1rem;
  margin-bottom: var(--spacing-md);
}

h5, h6 {
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

/* 段落优化 */
p {
  line-height: 1.8;
  margin-bottom: var(--spacing-lg);
  text-align: justify;
}

/* 强调文本 */
strong, b {
  color: var(--primary-color);
  font-weight: var(--font-weight-bold);
}

em, i {
  color: var(--accent-color);
  font-style: italic;
}

/* 列表样式 */
ul {
  list-style: none;
  padding-left: 0;
}

ul li {
  position: relative;
  padding-left: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  line-height: 1.7;
}

ul li::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0.8em;
  width: 4px;
  height: 4px;
  background: var(--accent-color);
  border-radius: 50%;
}

ol {
  counter-reset: item;
  padding-left: 0;
}

ol li {
  position: relative;
  padding-left: var(--spacing-xl);
  margin-bottom: var(--spacing-md);
  counter-increment: item;
  line-height: 1.7;
}

ol li::before {
  content: counter(item, decimal-leading-zero);
  position: absolute;
  left: 0;
  top: 0;
  color: var(--warm-gray);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-small);
  min-width: 20px;
}

/* 引用块样式 */
blockquote {
  background: transparent;
  border: none;
  border-left: 3px solid var(--accent-color);
  padding: 0 0 0 var(--spacing-xl);
  margin: var(--spacing-2xl) 0;
  position: relative;
}

blockquote::before {
  content: '';
  position: absolute;
  left: -6px;
  top: 0;
  width: 6px;
  height: 6px;
  background: var(--accent-color);
  border-radius: 50%;
}

blockquote::after {
  content: '';
  position: absolute;
  left: -6px;
  bottom: 0;
  width: 6px;
  height: 6px;
  background: var(--warm-gray);
  border-radius: 50%;
}

blockquote p {
  margin-bottom: 0;
  font-style: italic;
  color: var(--text-secondary);
  line-height: 1.6;
  font-size: 1.1rem;
}

/* 代码样式 */
code {
  background: var(--surface-color);
  color: var(--primary-color);
  padding: 0.2em 0.4em;
  border-radius: var(--radius-sm);
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.9em;
  border: 1px solid var(--border-color);
}

pre {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-xl);
  overflow-x: auto;
  margin: var(--spacing-2xl) 0;
  position: relative;
}

pre::before {
  content: '';
  position: absolute;
  top: var(--spacing-sm);
  left: var(--spacing-sm);
  width: 8px;
  height: 8px;
  background: var(--accent-color);
  border-radius: 50%;
  box-shadow: 16px 0 var(--warm-gray), 32px 0 var(--border-color);
}

pre code {
  background: transparent;
  border: none;
  padding: 0;
  color: var(--text-primary);
}

/* 表格样式 */
table {
  border-collapse: collapse;
  margin: var(--spacing-2xl) 0;
  width: 100%;
  background: var(--background-color);
}

th {
  background: var(--surface-color);
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-lg);
  text-align: left;
  border-bottom: 2px solid var(--accent-color);
  position: relative;
}

th::after {
  content: '';
  position: absolute;
  bottom: -2px;
  right: 0;
  width: 30%;
  height: 2px;
  background: var(--warm-gray);
}

td {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

tr:last-child td {
  border-bottom: none;
}

tr:nth-child(even) {
  background: rgba(255, 112, 67, 0.02);
}

/* 链接样式 */
a {
  color: var(--accent-color);
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: var(--transition-normal);
}

a:hover {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
}

/* 分隔线 */
hr {
  border: none;
  height: 1px;
  background: linear-gradient(90deg, var(--accent-color), var(--warm-gray), transparent);
  margin: var(--spacing-3xl) 0;
  position: relative;
}

hr::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  background: var(--accent-color);
  border-radius: 50%;
}

/* 图片样式 */
img {
  border-radius: var(--radius-md);
  margin: var(--spacing-xl) 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 打印优化 */
@media print {
  body {
    color: #000000;
    background: #ffffff;
  }
  
  h1 {
    color: #000000;
  }
  
  h1::before {
    background: linear-gradient(180deg, #666666, #999999);
  }
  
  h2 {
    color: #000000;
    background: #f5f5f5;
    border-top-color: #666666;
  }
  
  h2::after {
    background: #999999;
  }
  
  h3 {
    color: #000000;
    border-bottom-color: #cccccc;
  }
  
  h3::after {
    background: #666666;
  }
  
  h4 {
    color: #666666;
  }
  
  ul li::before {
    background: #666666;
  }
  
  ol li::before {
    color: #666666;
  }
  
  blockquote {
    border-left-color: #666666;
  }
  
  blockquote::before {
    background: #666666;
  }
  
  blockquote::after {
    background: #999999;
  }
  
  code {
    background: #f0f0f0;
    color: #000000;
    border: 1pt solid #cccccc;
  }
  
  pre {
    background: #f8f8f8;
    border: 1pt solid #cccccc;
  }
  
  pre::before {
    background: #666666;
    box-shadow: 16px 0 #999999, 32px 0 #cccccc;
  }
  
  th {
    background: #f0f0f0;
    color: #000000;
    border-bottom-color: #666666;
  }
  
  th::after {
    background: #999999;
  }
  
  td {
    border-bottom-color: #cccccc;
  }
  
  tr:nth-child(even) {
    background: #f8f8f8;
  }
  
  a {
    color: #000000;
    border-bottom: 1pt solid #666666;
  }
  
  hr {
    background: linear-gradient(90deg, #666666, #999999, transparent);
  }
  
  hr::before {
    background: #666666;
  }
  
  img {
    box-shadow: none;
  }
}

