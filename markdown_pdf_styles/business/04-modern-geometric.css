/*
 * 商务系列 04 - 现代几何
 * Modern Geometric Business Style
 * 
 * 特色：几何元素，现代感强，适合科技公司、创新企业
 * 设计理念：几何美学与商务专业的完美融合
 */

@import url('./common/base.css');
@import url('./common/variables.css');

:root {
  /* 主题色彩 */
  --primary-color: #2c3e50;
  --secondary-color: #34495e;
  --accent-color: #3498db;
  --geometric-accent: #e74c3c;
  --text-primary: #2c3e50;
  --text-secondary: #7f8c8d;
  --background-color: #ffffff;
  --surface-color: #f8f9fa;
  --border-color: #ecf0f1;
}

/* 页面整体样式 */
body {
  background-color: var(--background-color);
  color: var(--text-primary);
  font-weight: var(--font-weight-normal);
  letter-spacing: 0.01em;
  line-height: 1.6;
}

/* 标题样式系统 */
h1 {
  color: var(--primary-color);
  font-weight: var(--font-weight-bold);
  font-size: 2.8rem;
  margin-bottom: var(--spacing-2xl);
  position: relative;
  padding-left: var(--spacing-xl);
}

h1::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 6px;
  height: 100%;
  background: linear-gradient(180deg, var(--accent-color), var(--geometric-accent));
  border-radius: var(--radius-full);
}

h1::after {
  content: '';
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid var(--accent-color);
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
}

h2 {
  color: var(--secondary-color);
  font-weight: var(--font-weight-medium);
  position: relative;
  padding: var(--spacing-lg) var(--spacing-xl) var(--spacing-lg) var(--spacing-2xl);
  margin: var(--spacing-2xl) 0 var(--spacing-xl) 0;
  background: var(--surface-color);
  border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
}

h2::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 0;
  height: 0;
  border-right: 20px solid var(--surface-color);
  border-top: 50% solid transparent;
  border-bottom: 50% solid transparent;
}

h2::after {
  content: '';
  position: absolute;
  left: var(--spacing-sm);
  top: 50%;
  transform: translateY(-50%);
  width: 12px;
  height: 12px;
  background: var(--accent-color);
  border-radius: 50%;
}

h3 {
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
  position: relative;
  padding-left: var(--spacing-lg);
  margin-top: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
}

h3::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
  height: 8px;
  background: var(--geometric-accent);
  transform: translateY(-50%) rotate(45deg);
}

h4 {
  color: var(--accent-color);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.1em;
  font-size: 1.1rem;
  border-bottom: 2px solid var(--accent-color);
  padding-bottom: var(--spacing-xs);
  display: inline-block;
}

h5, h6 {
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* 段落优化 */
p {
  line-height: 1.7;
  margin-bottom: var(--spacing-lg);
}

/* 强调文本 */
strong, b {
  color: var(--primary-color);
  font-weight: var(--font-weight-bold);
}

em, i {
  color: var(--geometric-accent);
  font-style: italic;
}

/* 列表样式 */
ul {
  list-style: none;
  padding-left: 0;
}

ul li {
  position: relative;
  padding-left: var(--spacing-xl);
  margin-bottom: var(--spacing-md);
  line-height: 1.6;
}

ul li::before {
  content: '';
  position: absolute;
  left: var(--spacing-xs);
  top: 0.7em;
  width: 6px;
  height: 6px;
  background: var(--accent-color);
  transform: rotate(45deg);
}

ol {
  counter-reset: item;
  padding-left: 0;
}

ol li {
  position: relative;
  padding-left: var(--spacing-2xl);
  margin-bottom: var(--spacing-md);
  counter-increment: item;
  line-height: 1.6;
}

ol li::before {
  content: counter(item);
  position: absolute;
  left: 0;
  top: 0;
  background: var(--accent-color);
  color: white;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-bold);
  clip-path: polygon(0% 0%, 75% 0%, 100% 50%, 75% 100%, 0% 100%);
}

/* 引用块样式 */
blockquote {
  background: var(--surface-color);
  border: none;
  padding: var(--spacing-xl);
  margin: var(--spacing-2xl) 0;
  position: relative;
  border-radius: var(--radius-lg);
  clip-path: polygon(0 0, calc(100% - 20px) 0, 100% 20px, 100% 100%, 0 100%);
}

blockquote::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 20px;
  height: 20px;
  background: var(--accent-color);
  clip-path: polygon(0 0, 100% 0, 100% 100%);
}

blockquote p {
  margin-bottom: 0;
  font-style: italic;
  color: var(--text-secondary);
  position: relative;
  z-index: 1;
}

/* 代码样式 */
code {
  background: var(--surface-color);
  color: var(--primary-color);
  padding: 0.2em 0.5em;
  border-radius: var(--radius-sm);
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.9em;
  border-left: 3px solid var(--accent-color);
}

pre {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  overflow-x: auto;
  margin: var(--spacing-2xl) 0;
  position: relative;
}

pre::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--accent-color), var(--geometric-accent));
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

pre code {
  background: transparent;
  border: none;
  padding: 0;
  color: var(--text-primary);
}

/* 表格样式 */
table {
  border-collapse: separate;
  border-spacing: 0;
  background: var(--background-color);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.1);
  margin: var(--spacing-2xl) 0;
}

th {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-lg);
  text-align: left;
  border: none;
  position: relative;
}

th::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--accent-color), var(--geometric-accent));
}

td {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  border-right: 1px solid var(--border-color);
  position: relative;
}

td:last-child {
  border-right: none;
}

tr:last-child td {
  border-bottom: none;
}

tr:nth-child(even) {
  background: var(--surface-color);
}

/* 链接样式 */
a {
  color: var(--accent-color);
  text-decoration: none;
  border-bottom: 2px solid transparent;
  transition: var(--transition-normal);
  position: relative;
}

a:hover {
  color: var(--geometric-accent);
  border-bottom-color: var(--geometric-accent);
}

/* 分隔线 */
hr {
  border: none;
  height: 3px;
  background: linear-gradient(90deg, var(--accent-color), var(--geometric-accent), transparent);
  margin: var(--spacing-3xl) 0;
  position: relative;
}

hr::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 10px solid var(--geometric-accent);
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
}

/* 图片样式 */
img {
  border-radius: var(--radius-lg);
  margin: var(--spacing-xl) 0;
  position: relative;
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.2);
}

/* 打印优化 */
@media print {
  body {
    color: #000000;
    background: #ffffff;
  }
  
  h1 {
    color: #000000;
  }
  
  h1::before {
    background: linear-gradient(180deg, #666666, #999999);
  }
  
  h1::after {
    border-left-color: #666666;
  }
  
  h2 {
    color: #000000;
    background: #f5f5f5;
  }
  
  h2::before {
    border-right-color: #f5f5f5;
  }
  
  h2::after {
    background: #666666;
  }
  
  h3::before {
    background: #999999;
  }
  
  h4 {
    color: #333333;
    border-bottom-color: #666666;
  }
  
  ul li::before {
    background: #666666;
  }
  
  ol li::before {
    background: #666666;
  }
  
  blockquote {
    background: #f8f8f8;
    clip-path: none;
    border-left: 3pt solid #666666;
  }
  
  blockquote::before {
    display: none;
  }
  
  code {
    background: #f0f0f0;
    color: #000000;
    border-left-color: #666666;
  }
  
  pre {
    background: #f8f8f8;
    border: 1pt solid #cccccc;
  }
  
  pre::before {
    background: linear-gradient(90deg, #666666, #999999);
  }
  
  table {
    box-shadow: none;
  }
  
  th {
    background: #e0e0e0;
    color: #000000;
  }
  
  th::after {
    background: linear-gradient(90deg, #666666, #999999);
  }
  
  tr:nth-child(even) {
    background: #f5f5f5;
  }
  
  a {
    color: #000000;
    border-bottom: 1pt solid #666666;
  }
  
  hr {
    background: linear-gradient(90deg, #666666, #999999, transparent);
  }
  
  hr::after {
    border-left-color: #999999;
  }
  
  img {
    box-shadow: none;
  }
}

