/*
 * 商务系列 03 - 自然大地
 * Natural Earth Business Style
 * 
 * 特色：新自然主义设计，大地色系，温暖专业
 * 设计理念：可持续设计理念，环保意识与商务专业的结合
 */

@import url('./common/base.css');
@import url('./common/variables.css');

:root {
  /* 主题色彩 */
  --primary-color: var(--sustainable-forest);
  --secondary-color: var(--sustainable-earth);
  --accent-color: var(--nature-sage);
  --text-primary: #2c3e50;
  --text-secondary: #34495e;
  --background-color: var(--nature-cream);
  --surface-color: #ffffff;
  --border-color: #d5dbdb;
  --warm-accent: var(--nature-terracotta);
}

/* 页面整体样式 */
body {
  background-color: var(--background-color);
  color: var(--text-primary);
  font-weight: var(--font-weight-normal);
  letter-spacing: 0.005em;
  line-height: 1.7;
}

/* 标题样式系统 */
h1 {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  font-size: 2.8rem;
  margin-bottom: var(--spacing-2xl);
  position: relative;
  padding-bottom: var(--spacing-lg);
}

h1::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, var(--accent-color), var(--warm-accent), transparent);
  border-radius: var(--radius-full);
}

h2 {
  color: var(--secondary-color);
  font-weight: var(--font-weight-medium);
  background: linear-gradient(135deg, var(--surface-color), var(--background-color));
  padding: var(--spacing-lg) var(--spacing-xl);
  border-radius: var(--radius-lg);
  border-left: 5px solid var(--accent-color);
  margin: var(--spacing-2xl) 0 var(--spacing-xl) 0;
  box-shadow: 0 2px 8px rgba(135, 169, 107, 0.1);
}

h3 {
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
  position: relative;
  padding-left: var(--spacing-lg);
  margin-top: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
}

h3::before {
  content: '🌿';
  position: absolute;
  left: 0;
  font-size: 0.9em;
}

h4 {
  color: var(--accent-color);
  font-weight: var(--font-weight-medium);
  border-bottom: 2px solid var(--accent-color);
  padding-bottom: var(--spacing-xs);
  display: inline-block;
}

h5, h6 {
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

/* 段落优化 */
p {
  line-height: 1.8;
  margin-bottom: var(--spacing-lg);
  text-align: justify;
}

/* 强调文本 */
strong, b {
  color: var(--primary-color);
  font-weight: var(--font-weight-bold);
}

em, i {
  color: var(--warm-accent);
  font-style: italic;
}

/* 列表样式 */
ul {
  list-style: none;
  padding-left: 0;
}

ul li {
  position: relative;
  padding-left: var(--spacing-xl);
  margin-bottom: var(--spacing-md);
  line-height: 1.7;
}

ul li::before {
  content: '●';
  position: absolute;
  left: var(--spacing-sm);
  color: var(--accent-color);
  font-size: 1.2em;
}

ol {
  counter-reset: item;
  padding-left: 0;
}

ol li {
  position: relative;
  padding-left: var(--spacing-2xl);
  margin-bottom: var(--spacing-md);
  counter-increment: item;
  line-height: 1.7;
}

ol li::before {
  content: counter(item);
  position: absolute;
  left: 0;
  top: 0;
  background: linear-gradient(135deg, var(--accent-color), var(--warm-accent));
  color: white;
  width: 28px;
  height: 28px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-medium);
  box-shadow: 0 2px 4px rgba(135, 169, 107, 0.3);
}

/* 引用块样式 */
blockquote {
  background: linear-gradient(135deg, var(--surface-color), rgba(135, 169, 107, 0.05));
  border: none;
  border-left: 5px solid var(--accent-color);
  border-radius: 0 var(--radius-xl) var(--radius-xl) 0;
  padding: var(--spacing-xl) var(--spacing-2xl);
  margin: var(--spacing-2xl) 0;
  position: relative;
  box-shadow: 0 4px 12px rgba(135, 169, 107, 0.1);
}

blockquote::before {
  content: '🌱';
  position: absolute;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  font-size: 1.5rem;
  opacity: 0.6;
}

blockquote p {
  margin-bottom: 0;
  font-style: italic;
  color: var(--text-secondary);
  line-height: 1.6;
}

/* 代码样式 */
code {
  background: rgba(135, 169, 107, 0.1);
  color: var(--primary-color);
  padding: 0.2em 0.5em;
  border-radius: var(--radius-md);
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.9em;
  border: 1px solid rgba(135, 169, 107, 0.2);
}

pre {
  background: linear-gradient(135deg, var(--surface-color), rgba(135, 169, 107, 0.03));
  border: 1px solid rgba(135, 169, 107, 0.2);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  overflow-x: auto;
  margin: var(--spacing-2xl) 0;
  box-shadow: 0 2px 8px rgba(135, 169, 107, 0.1);
}

pre code {
  background: transparent;
  border: none;
  padding: 0;
  color: var(--text-primary);
}

/* 表格样式 */
table {
  border-collapse: separate;
  border-spacing: 0;
  background: var(--surface-color);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(135, 169, 107, 0.1);
  margin: var(--spacing-2xl) 0;
}

th {
  background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
  color: white;
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-lg);
  text-align: left;
  border: none;
}

td {
  padding: var(--spacing-lg);
  border-bottom: 1px solid rgba(135, 169, 107, 0.1);
  border-right: 1px solid rgba(135, 169, 107, 0.1);
}

td:last-child {
  border-right: none;
}

tr:last-child td {
  border-bottom: none;
}

tr:nth-child(even) {
  background: rgba(135, 169, 107, 0.03);
}

/* 链接样式 */
a {
  color: var(--warm-accent);
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: var(--transition-normal);
}

a:hover {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
}

/* 分隔线 */
hr {
  border: none;
  height: 2px;
  background: linear-gradient(90deg, var(--accent-color), var(--warm-accent), transparent);
  margin: var(--spacing-3xl) 0;
  border-radius: var(--radius-full);
}

/* 图片样式 */
img {
  border-radius: var(--radius-lg);
  box-shadow: 0 4px 12px rgba(135, 169, 107, 0.2);
  margin: var(--spacing-xl) 0;
}

/* 打印优化 */
@media print {
  body {
    color: #000000;
    background: #ffffff;
  }
  
  h1 {
    color: #000000;
  }
  
  h1::after {
    background: linear-gradient(90deg, #666666, #999999, transparent);
  }
  
  h2 {
    color: #000000;
    background: #f5f5f5;
    border-left: 3pt solid #666666;
    box-shadow: none;
  }
  
  h3::before {
    content: '▶';
    color: #666666;
  }
  
  h4 {
    color: #333333;
    border-bottom: 2pt solid #666666;
  }
  
  ol li::before {
    background: #666666;
    color: #ffffff;
    box-shadow: none;
  }
  
  ul li::before {
    color: #666666;
  }
  
  blockquote {
    background: #f8f8f8;
    border-left: 3pt solid #666666;
    box-shadow: none;
  }
  
  blockquote::before {
    content: '';
  }
  
  table {
    box-shadow: none;
  }
  
  th {
    background: #e0e0e0;
    color: #000000;
  }
  
  tr:nth-child(even) {
    background: #f5f5f5;
  }
  
  code {
    background: #f0f0f0;
    color: #000000;
    border: 1pt solid #cccccc;
  }
  
  pre {
    background: #f8f8f8;
    border: 1pt solid #cccccc;
    box-shadow: none;
  }
  
  a {
    color: #000000;
    border-bottom: 1pt solid #666666;
  }
  
  hr {
    background: linear-gradient(90deg, #666666, #999999, transparent);
  }
  
  img {
    box-shadow: none;
  }
}

