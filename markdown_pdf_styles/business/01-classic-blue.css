/*
 * 商务系列 01 - 经典蓝调
 * Classic Blue Business Style
 * 
 * 特色：经典商务蓝色主题，稳重专业，适合财务报告、年度总结
 * 设计理念：传统与现代的完美结合，强调权威性和可信度
 */

@import url('./common/base.css');
@import url('./common/variables.css');

:root {
  /* 主题色彩 */
  --primary-color: var(--business-primary);
  --secondary-color: var(--business-secondary);
  --accent-color: var(--business-accent);
  --text-primary: var(--business-neutral-dark);
  --text-secondary: var(--business-neutral);
  --background-color: #ffffff;
  --surface-color: var(--business-background);
  --border-color: var(--business-border);
}

/* 页面整体样式 */
body {
  background-color: var(--background-color);
  color: var(--text-primary);
  font-weight: var(--font-weight-normal);
  letter-spacing: 0.01em;
}

/* 标题样式系统 */
h1 {
  color: var(--primary-color);
  font-weight: var(--font-weight-bold);
  border-bottom: 3px solid var(--accent-color);
  padding-bottom: var(--spacing-md);
  margin-bottom: var(--spacing-2xl);
  position: relative;
}

h1::after {
  content: '';
  position: absolute;
  bottom: -3px;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, var(--accent-color), var(--secondary-color));
}

h2 {
  color: var(--secondary-color);
  font-weight: var(--font-weight-bold);
  border-left: 4px solid var(--accent-color);
  padding-left: var(--spacing-lg);
  margin-top: var(--spacing-2xl);
  margin-bottom: var(--spacing-xl);
  background: linear-gradient(90deg, var(--surface-color), transparent);
  padding-top: var(--spacing-sm);
  padding-bottom: var(--spacing-sm);
}

h3 {
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
  position: relative;
  padding-left: var(--spacing-lg);
}

h3::before {
  content: '▶';
  position: absolute;
  left: 0;
  color: var(--accent-color);
  font-size: 0.8em;
}

h4, h5, h6 {
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
}

/* 段落优化 */
p {
  text-align: justify;
  line-height: 1.7;
  margin-bottom: var(--spacing-lg);
}

/* 强调文本 */
strong, b {
  color: var(--primary-color);
  font-weight: var(--font-weight-bold);
}

em, i {
  color: var(--secondary-color);
  font-style: italic;
}

/* 列表样式 */
ul {
  list-style: none;
  padding-left: 0;
}

ul li {
  position: relative;
  padding-left: var(--spacing-xl);
  margin-bottom: var(--spacing-md);
}

ul li::before {
  content: '●';
  position: absolute;
  left: var(--spacing-sm);
  color: var(--accent-color);
  font-weight: bold;
}

ol {
  counter-reset: item;
  padding-left: 0;
}

ol li {
  position: relative;
  padding-left: var(--spacing-2xl);
  margin-bottom: var(--spacing-md);
  counter-increment: item;
}

ol li::before {
  content: counter(item);
  position: absolute;
  left: 0;
  top: 0;
  background: var(--accent-color);
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-bold);
}

/* 引用块样式 */
blockquote {
  background: linear-gradient(135deg, var(--surface-color), #ffffff);
  border-left: 5px solid var(--accent-color);
  border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
  padding: var(--spacing-xl);
  margin: var(--spacing-2xl) 0;
  position: relative;
  box-shadow: var(--shadow-md);
}

blockquote::before {
  content: '"';
  position: absolute;
  top: -10px;
  left: var(--spacing-lg);
  font-size: 3rem;
  color: var(--accent-color);
  opacity: 0.3;
  font-family: Georgia, serif;
}

blockquote p {
  margin-bottom: 0;
  font-style: italic;
  color: var(--text-secondary);
}

/* 代码样式 */
code {
  background: var(--surface-color);
  color: var(--primary-color);
  padding: 0.2em 0.4em;
  border-radius: var(--radius-md);
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.9em;
  border: 1px solid var(--border-color);
}

pre {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  overflow-x: auto;
  position: relative;
  box-shadow: var(--shadow-sm);
}

pre::before {
  content: '';
  position: absolute;
  top: var(--spacing-md);
  left: var(--spacing-md);
  width: 12px;
  height: 12px;
  background: #ff5f56;
  border-radius: 50%;
  box-shadow: 20px 0 #ffbd2e, 40px 0 #27ca3f;
}

pre code {
  background: transparent;
  border: none;
  padding: 0;
  color: var(--text-primary);
}

/* 表格样式 */
table {
  border-collapse: separate;
  border-spacing: 0;
  background: white;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  margin: var(--spacing-2xl) 0;
}

th {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-lg);
  text-align: left;
  border: none;
}

td {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  border-right: 1px solid var(--border-color);
}

td:last-child {
  border-right: none;
}

tr:last-child td {
  border-bottom: none;
}

tr:nth-child(even) {
  background: var(--surface-color);
}

/* 链接样式 */
a {
  color: var(--accent-color);
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: var(--transition-normal);
}

a:hover {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
}

/* 分隔线 */
hr {
  border: none;
  height: 2px;
  background: linear-gradient(90deg, var(--accent-color), transparent);
  margin: var(--spacing-3xl) 0;
}

/* 图片样式 */
img {
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  margin: var(--spacing-xl) 0;
}

/* 打印优化 */
@media print {
  body {
    color: #000000;
    background: #ffffff;
  }
  
  h1 {
    color: #000000;
    border-bottom: 2pt solid #000000;
  }
  
  h1::after {
    background: #000000;
  }
  
  h2 {
    color: #000000;
    border-left: 3pt solid #000000;
    background: #f5f5f5;
  }
  
  h3::before {
    color: #000000;
  }
  
  ol li::before {
    background: #000000;
    color: #ffffff;
  }
  
  ul li::before {
    color: #000000;
  }
  
  blockquote {
    background: #f8f8f8;
    border-left: 3pt solid #000000;
    box-shadow: none;
  }
  
  blockquote::before {
    color: #cccccc;
  }
  
  table {
    box-shadow: none;
  }
  
  th {
    background: #e0e0e0;
    color: #000000;
  }
  
  code {
    background: #f0f0f0;
    color: #000000;
    border: 1pt solid #cccccc;
  }
  
  pre {
    background: #f8f8f8;
    border: 1pt solid #cccccc;
    box-shadow: none;
  }
  
  pre::before {
    display: none;
  }
  
  a {
    color: #000000;
    border-bottom: 1pt solid #000000;
  }
  
  img {
    box-shadow: none;
  }
}

