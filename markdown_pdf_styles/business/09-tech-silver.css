/*
 * 商务系列 09 - 科技银灰
 * Tech Silver Business Style
 * 
 * 特色：科技感银灰色主题，现代简洁，适合科技、IT、创新企业
 * 设计理念：银灰色的未来感与蓝色科技元素的结合
 */

@import url('./common/base.css');
@import url('./common/variables.css');

:root {
  /* 主题色彩 */
  --primary-color: #37474f;
  --secondary-color: #546e7a;
  --accent-color: #00bcd4;
  --tech-blue: #03a9f4;
  --text-primary: #263238;
  --text-secondary: #607d8b;
  --background-color: #fafafa;
  --surface-color: #f5f5f5;
  --border-color: #e0e0e0;
}

/* 页面整体样式 */
body {
  background-color: var(--background-color);
  color: var(--text-primary);
  font-weight: var(--font-weight-normal);
  letter-spacing: 0.02em;
  line-height: 1.6;
}

/* 标题样式系统 */
h1 {
  color: var(--primary-color);
  font-weight: var(--font-weight-light);
  font-size: 3rem;
  margin-bottom: var(--spacing-2xl);
  position: relative;
  padding-left: var(--spacing-2xl);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

h1::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 6px;
  height: 100%;
  background: linear-gradient(180deg, var(--accent-color), var(--tech-blue));
  border-radius: var(--radius-full);
}

h1::after {
  content: '';
  position: absolute;
  left: 12px;
  top: 0;
  width: 2px;
  height: 100%;
  background: var(--surface-color);
  border-radius: var(--radius-full);
}

h2 {
  color: var(--secondary-color);
  font-weight: var(--font-weight-normal);
  background: linear-gradient(135deg, var(--surface-color), rgba(0, 188, 212, 0.03));
  padding: var(--spacing-lg) var(--spacing-xl);
  border-radius: var(--radius-md);
  margin: var(--spacing-2xl) 0 var(--spacing-xl) 0;
  position: relative;
  border-left: 3px solid var(--accent-color);
  font-family: 'SF Mono', monospace;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  font-size: 1.4rem;
}

h2::before {
  content: '//';
  position: absolute;
  right: var(--spacing-lg);
  top: 50%;
  transform: translateY(-50%);
  color: var(--tech-blue);
  font-size: 1.2em;
  opacity: 0.6;
}

h3 {
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
  position: relative;
  padding-left: var(--spacing-lg);
  margin-top: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
  font-family: 'SF Mono', monospace;
}

h3::before {
  content: '>';
  position: absolute;
  left: 0;
  color: var(--accent-color);
  font-weight: bold;
}

h4 {
  color: var(--accent-color);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.15em;
  font-size: 0.9rem;
  border-bottom: 1px solid var(--tech-blue);
  padding-bottom: var(--spacing-xs);
  display: inline-block;
}

h5, h6 {
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
  font-family: 'SF Mono', monospace;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  font-size: 0.8rem;
}

/* 段落优化 */
p {
  line-height: 1.7;
  margin-bottom: var(--spacing-lg);
}

/* 强调文本 */
strong, b {
  color: var(--primary-color);
  font-weight: var(--font-weight-bold);
  background: linear-gradient(135deg, transparent, rgba(0, 188, 212, 0.1), transparent);
  padding: 0 0.2em;
}

em, i {
  color: var(--tech-blue);
  font-style: italic;
}

/* 列表样式 */
ul {
  list-style: none;
  padding-left: 0;
}

ul li {
  position: relative;
  padding-left: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  line-height: 1.6;
}

ul li::before {
  content: '▸';
  position: absolute;
  left: 0;
  color: var(--accent-color);
  font-weight: bold;
}

ol {
  counter-reset: item;
  padding-left: 0;
}

ol li {
  position: relative;
  padding-left: var(--spacing-xl);
  margin-bottom: var(--spacing-md);
  counter-increment: item;
  line-height: 1.6;
}

ol li::before {
  content: '[' counter(item, decimal-leading-zero) ']';
  position: absolute;
  left: 0;
  top: 0;
  color: var(--tech-blue);
  font-family: 'SF Mono', monospace;
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-medium);
}

/* 引用块样式 */
blockquote {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-left: 4px solid var(--accent-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-xl);
  margin: var(--spacing-2xl) 0;
  position: relative;
  font-family: 'SF Mono', monospace;
}

blockquote::before {
  content: '/* ';
  position: absolute;
  top: var(--spacing-sm);
  left: var(--spacing-lg);
  color: var(--tech-blue);
  font-size: var(--font-size-small);
  opacity: 0.7;
}

blockquote::after {
  content: ' */';
  position: absolute;
  bottom: var(--spacing-sm);
  right: var(--spacing-lg);
  color: var(--tech-blue);
  font-size: var(--font-size-small);
  opacity: 0.7;
}

blockquote p {
  margin-bottom: 0;
  font-style: normal;
  color: var(--text-secondary);
  line-height: 1.5;
  padding: var(--spacing-md) 0;
}

/* 代码样式 */
code {
  background: var(--surface-color);
  color: var(--primary-color);
  padding: 0.2em 0.4em;
  border-radius: var(--radius-sm);
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.85em;
  border: 1px solid var(--border-color);
  font-weight: var(--font-weight-medium);
}

pre {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-xl);
  overflow-x: auto;
  margin: var(--spacing-2xl) 0;
  position: relative;
}

pre::before {
  content: 'CODE';
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-sm);
  color: var(--tech-blue);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  letter-spacing: 0.1em;
  opacity: 0.5;
}

pre code {
  background: transparent;
  border: none;
  padding: 0;
  color: var(--text-primary);
}

/* 表格样式 */
table {
  border-collapse: separate;
  border-spacing: 0;
  background: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  overflow: hidden;
  margin: var(--spacing-2xl) 0;
  font-family: 'SF Mono', monospace;
  font-size: var(--font-size-small);
}

th {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-md) var(--spacing-lg);
  text-align: left;
  border: none;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

td {
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  border-right: 1px solid var(--border-color);
}

td:last-child {
  border-right: none;
}

tr:last-child td {
  border-bottom: none;
}

tr:nth-child(even) {
  background: rgba(0, 188, 212, 0.02);
}

/* 链接样式 */
a {
  color: var(--accent-color);
  text-decoration: none;
  border-bottom: 1px dotted var(--accent-color);
  transition: var(--transition-normal);
  font-family: 'SF Mono', monospace;
}

a:hover {
  color: var(--tech-blue);
  border-bottom-style: solid;
  border-bottom-color: var(--tech-blue);
}

/* 分隔线 */
hr {
  border: none;
  height: 1px;
  background: linear-gradient(90deg, var(--accent-color), var(--tech-blue), transparent);
  margin: var(--spacing-3xl) 0;
  position: relative;
}

hr::before {
  content: '---';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  background: var(--background-color);
  color: var(--tech-blue);
  padding: 0 var(--spacing-sm);
  font-family: 'SF Mono', monospace;
  font-size: var(--font-size-small);
}

/* 图片样式 */
img {
  border-radius: var(--radius-md);
  margin: var(--spacing-xl) 0;
  border: 1px solid var(--border-color);
  filter: grayscale(10%);
}

/* 打印优化 */
@media print {
  body {
    color: #000000;
    background: #ffffff;
  }
  
  h1 {
    color: #000000;
  }
  
  h1::before {
    background: linear-gradient(180deg, #666666, #999999);
  }
  
  h1::after {
    background: #cccccc;
  }
  
  h2 {
    color: #000000;
    background: #f5f5f5;
    border-left-color: #666666;
  }
  
  h2::before {
    color: #999999;
  }
  
  h3::before {
    color: #666666;
  }
  
  h4 {
    color: #333333;
    border-bottom-color: #999999;
  }
  
  h5, h6 {
    color: #666666;
  }
  
  strong, b {
    background: none;
    color: #000000;
  }
  
  ul li::before {
    color: #666666;
  }
  
  ol li::before {
    color: #999999;
  }
  
  blockquote {
    background: #f8f8f8;
    border: 1pt solid #cccccc;
    border-left: 3pt solid #666666;
  }
  
  blockquote::before,
  blockquote::after {
    color: #999999;
  }
  
  code {
    background: #f0f0f0;
    color: #000000;
    border: 1pt solid #cccccc;
  }
  
  pre {
    background: #f8f8f8;
    border: 1pt solid #cccccc;
  }
  
  pre::before {
    color: #999999;
  }
  
  table {
    border: 1pt solid #cccccc;
  }
  
  th {
    background: #e0e0e0;
    color: #000000;
  }
  
  td {
    border-bottom: 1pt solid #cccccc;
    border-right: 1pt solid #cccccc;
  }
  
  tr:nth-child(even) {
    background: #f5f5f5;
  }
  
  a {
    color: #000000;
    border-bottom: 1pt dotted #666666;
  }
  
  hr {
    background: linear-gradient(90deg, #666666, #999999, transparent);
  }
  
  hr::before {
    background: #ffffff;
    color: #999999;
  }
  
  img {
    border: 1pt solid #cccccc;
    filter: none;
  }
}

