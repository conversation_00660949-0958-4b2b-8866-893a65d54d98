<link rel="stylesheet" href="./refined-06.css">

# Markdown PDF 样式测试文档

这是一个用于测试Markdown PDF样式的综合性文档，包含了所有常见的Markdown元素。

## 1. 标题层级测试

### 1.1 三级标题示例

#### 1.1.1 四级标题示例

##### 五级标题示例

###### 六级标题示例

## 2. 文本格式测试

这是一个普通段落，用于测试基本的文本显示效果。段落应该有合适的行高、字间距和段落间距。

这是另一个段落，包含**粗体文本**和*斜体文本*的示例。我们还可以测试`行内代码`的显示效果。

## 3. 列表测试

### 3.1 无序列表

- 第一个列表项
- 第二个列表项
  - 嵌套列表项
  - 另一个嵌套项
- 第三个列表项

### 3.2 有序列表

1. 第一个编号项
2. 第二个编号项
3. 第三个编号项

## 4. 引用块测试

> 这是一个引用块的示例。引用块通常用于突出显示重要的信息或者引用他人的观点。
> 
> 引用块可以包含多个段落，每个段落都应该有合适的格式。

## 5. 代码块测试

```javascript
function hello() {
    console.log("Hello, World!");
    return "测试代码块的显示效果";
}

// 这是一个注释
const result = hello();
```

## 6. 表格测试

| 项目 | 描述 | 状态 |
|------|------|------|
| 样式设计 | 创建30套PDF样式 | 进行中 |
| 测试验证 | 验证样式效果 | 当前阶段 |
| 文档整理 | 整理交付文档 | 待开始 |

## 7. 链接测试

这是一个[链接示例](https://example.com)，用于测试链接的显示效果。

## 8. 分隔线测试

---

## 9. 总结

这个测试文档包含了所有主要的Markdown元素，可以用来验证样式的完整性和一致性。每个样式都应该能够正确处理这些元素，并保持良好的视觉效果。

