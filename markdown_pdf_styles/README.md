# Markdown PDF 样式集合

## 项目概述

本项目为Visual Studio Code的Markdown Preview Enhanced插件提供30套符合2025年设计趋势的高级感PDF导出样式文件。

## 设计理念

基于2025年设计趋势研究，融合以下核心设计理念：

### 2025年设计趋势要点
1. **极简美学与精致细节**：简约而不简单，注重细节的完美呈现
2. **新自然主义**：大地色系，温暖而专业的视觉感受
3. **可持续设计**：环保理念，减少视觉噪音
4. **情感驱动设计**：通过排版传达专业与信任感
5. **工艺复兴**：手工感与现代技术的完美结合

### 核心优化方向

#### 字体排版系统
- **字体**：统一使用思源黑体（Source Han Sans）在线引用
- **字号体系**：建立和谐的字号层级（H1-H6, 正文, 引用等）
- **字重运用**：Light(300), Regular(400), Medium(500), Bold(700)
- **字间距**：精细调整letter-spacing，提升可读性
- **行高**：优化line-height，确保舒适的阅读体验

#### 布局与留白
- **版心设计**：合理的内容区域宽度
- **留白系统**：统一的margin和padding体系
- **呼吸感**：充足的元素间距，营造高级感

#### 色彩系统
- **主色调**：2025年主流低饱和度配色
- **辅助色**：谨慎克制的强调色运用
- **层次感**：通过色彩深浅营造信息层级

#### 打印一致化
- **媒体查询**：@media print规则确保打印一致性
- **字体渲染**：优化打印时的字体显示
- **颜色处理**：确保打印色彩准确性
- **布局稳定**：统一的页面布局参数

## 样式分类

### 商务系列（1-10套）
专业、稳重、权威感强，适用于商业报告、财务分析等

### 创意系列（11-20套）
现代、活力、创新感，适用于策划方案、创意提案等

### 学术系列（21-30套）
严谨、清晰、逻辑性强，适用于研究报告、洞察分析等

## 技术规范

### CSS架构
- 模块化设计，便于维护和扩展
- 语义化类名，提高可读性
- 响应式设计，适配不同屏幕尺寸

### 兼容性
- 支持Markdown Preview Enhanced插件
- 兼容主流浏览器打印功能
- 确保PDF导出质量

### 文件结构
```
styles/
├── business/          # 商务系列样式
├── creative/          # 创意系列样式
├── academic/          # 学术系列样式
├── common/           # 公共样式组件
└── demo/             # 演示文档
```

## 使用说明

1. 将样式文件放置在VSCode工作区
2. 在Markdown文件中引用对应样式
3. 使用Markdown Preview Enhanced导出PDF

## 更新日志

- v1.0.0: 初始版本，包含30套完整样式

