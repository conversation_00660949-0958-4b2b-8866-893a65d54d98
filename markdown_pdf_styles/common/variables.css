/* 
 * 商务系列样式变量
 * 专业、稳重、权威感强
 */

:root {
  /* 商务色彩系统 */
  --business-primary: #1a365d;      /* 深蓝色 - 专业权威 */
  --business-secondary: #2c5282;    /* 中蓝色 - 稳重可信 */
  --business-accent: #3182ce;       /* 亮蓝色 - 强调重点 */
  --business-neutral-dark: #2d3748; /* 深灰色 - 主要文本 */
  --business-neutral: #4a5568;      /* 中灰色 - 次要文本 */
  --business-neutral-light: #718096; /* 浅灰色 - 辅助文本 */
  --business-background: #f7fafc;   /* 背景色 - 清洁专业 */
  --business-border: #e2e8f0;       /* 边框色 - 分隔线 */
  --business-success: #38a169;      /* 成功色 - 正面数据 */
  --business-warning: #d69e2e;      /* 警告色 - 注意事项 */
  --business-error: #e53e3e;        /* 错误色 - 风险提示 */
}

/* 创意系列样式变量 */
:root {
  /* 创意色彩系统 */
  --creative-primary: #805ad5;      /* 紫色 - 创新创意 */
  --creative-secondary: #9f7aea;    /* 浅紫色 - 灵感启发 */
  --creative-accent: #ed8936;       /* 橙色 - 活力热情 */
  --creative-neutral-dark: #2d3748; /* 深灰色 - 主要文本 */
  --creative-neutral: #4a5568;      /* 中灰色 - 次要文本 */
  --creative-neutral-light: #718096; /* 浅灰色 - 辅助文本 */
  --creative-background: #faf5ff;   /* 背景色 - 温暖舒适 */
  --creative-border: #e9d8fd;       /* 边框色 - 柔和分隔 */
  --creative-success: #48bb78;      /* 成功色 - 积极向上 */
  --creative-warning: #ed8936;      /* 警告色 - 引起注意 */
  --creative-error: #f56565;        /* 错误色 - 明确提示 */
}

/* 学术系列样式变量 */
:root {
  /* 学术色彩系统 */
  --academic-primary: #2d3748;      /* 深灰色 - 严谨专业 */
  --academic-secondary: #4a5568;    /* 中灰色 - 逻辑清晰 */
  --academic-accent: #3182ce;       /* 蓝色 - 理性分析 */
  --academic-neutral-dark: #1a202c; /* 极深灰 - 主要文本 */
  --academic-neutral: #2d3748;      /* 深灰色 - 次要文本 */
  --academic-neutral-light: #4a5568; /* 中灰色 - 辅助文本 */
  --academic-background: #ffffff;   /* 背景色 - 纯净简洁 */
  --academic-border: #e2e8f0;       /* 边框色 - 清晰分隔 */
  --academic-success: #38a169;      /* 成功色 - 正确结论 */
  --academic-warning: #d69e2e;      /* 警告色 - 需要注意 */
  --academic-error: #e53e3e;        /* 错误色 - 错误标记 */
}

/* 2025年流行色彩趋势 */
:root {
  /* 新自然主义色彩 */
  --nature-sage: #87a96b;           /* 鼠尾草绿 */
  --nature-terracotta: #c65d07;     /* 赤陶色 */
  --nature-cream: #f5f5dc;          /* 奶油色 */
  --nature-stone: #8d8680;          /* 石头色 */
  
  /* 极简美学色彩 */
  --minimal-white: #ffffff;         /* 纯白 */
  --minimal-off-white: #fafafa;     /* 米白 */
  --minimal-light-gray: #f5f5f5;    /* 浅灰 */
  --minimal-medium-gray: #e0e0e0;   /* 中灰 */
  --minimal-dark-gray: #424242;     /* 深灰 */
  --minimal-black: #212121;         /* 近黑 */
  
  /* 可持续设计色彩 */
  --sustainable-forest: #2d5016;    /* 森林绿 */
  --sustainable-ocean: #006064;     /* 海洋蓝 */
  --sustainable-earth: #5d4037;     /* 大地棕 */
  --sustainable-sky: #81c784;       /* 天空蓝 */
}

/* 渐变色系统 */
:root {
  /* 商务渐变 */
  --gradient-business: linear-gradient(135deg, #1a365d 0%, #3182ce 100%);
  --gradient-business-subtle: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  
  /* 创意渐变 */
  --gradient-creative: linear-gradient(135deg, #805ad5 0%, #ed8936 100%);
  --gradient-creative-subtle: linear-gradient(135deg, #faf5ff 0%, #fed7d7 100%);
  
  /* 学术渐变 */
  --gradient-academic: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
  --gradient-academic-subtle: linear-gradient(135deg, #ffffff 0%, #f7fafc 100%);
}

/* 阴影系统 */
:root {
  /* 基础阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* 彩色阴影 */
  --shadow-business: 0 4px 14px 0 rgba(49, 130, 206, 0.15);
  --shadow-creative: 0 4px 14px 0 rgba(128, 90, 213, 0.15);
  --shadow-academic: 0 4px 14px 0 rgba(45, 55, 72, 0.15);
}

/* 边框圆角系统 */
:root {
  --radius-sm: 0.125rem;   /* 2px */
  --radius-md: 0.25rem;    /* 4px */
  --radius-lg: 0.5rem;     /* 8px */
  --radius-xl: 0.75rem;    /* 12px */
  --radius-2xl: 1rem;      /* 16px */
  --radius-full: 9999px;   /* 完全圆角 */
}

/* 过渡动画系统 */
:root {
  --transition-fast: 0.15s ease-out;
  --transition-normal: 0.2s ease-out;
  --transition-slow: 0.3s ease-out;
  --transition-all: all 0.2s ease-out;
}

/* 打印专用色彩 */
@media print {
  :root {
    /* 打印时将彩色转换为灰度 */
    --business-primary: #000000;
    --business-secondary: #333333;
    --business-accent: #666666;
    --creative-primary: #000000;
    --creative-secondary: #333333;
    --creative-accent: #666666;
    --academic-primary: #000000;
    --academic-secondary: #333333;
    --academic-accent: #666666;
    
    /* 打印时的背景色 */
    --business-background: #ffffff;
    --creative-background: #ffffff;
    --academic-background: #ffffff;
    
    /* 打印时的边框色 */
    --business-border: #cccccc;
    --creative-border: #cccccc;
    --academic-border: #cccccc;
  }
}

