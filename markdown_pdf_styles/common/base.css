/* 
 * 公共基础样式 - 思源黑体字体系统
 * 适用于所有Markdown PDF样式
 * 确保打印一致性
 */

/* 思源黑体在线引用 */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap');

/* 基础变量定义 */
:root {
  /* 字体系统 */
  --font-family-base: 'Noto Sans SC', 'Source Han Sans SC', 'PingFang SC', 'Microsoft YaHei', sans-serif;
  
  /* 字号系统 */
  --font-size-h1: 2.5rem;      /* 40px */
  --font-size-h2: 2rem;        /* 32px */
  --font-size-h3: 1.5rem;      /* 24px */
  --font-size-h4: 1.25rem;     /* 20px */
  --font-size-h5: 1.125rem;    /* 18px */
  --font-size-h6: 1rem;        /* 16px */
  --font-size-body: 1rem;      /* 16px */
  --font-size-small: 0.875rem; /* 14px */
  --font-size-xs: 0.75rem;     /* 12px */
  
  /* 字重系统 */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-bold: 700;
  
  /* 行高系统 */
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.6;
  --line-height-loose: 1.8;
  
  /* 字间距系统 */
  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0;
  --letter-spacing-wide: 0.025em;
  --letter-spacing-wider: 0.05em;
  
  /* 间距系统 */
  --spacing-xs: 0.25rem;   /* 4px */
  --spacing-sm: 0.5rem;    /* 8px */
  --spacing-md: 1rem;      /* 16px */
  --spacing-lg: 1.5rem;    /* 24px */
  --spacing-xl: 2rem;      /* 32px */
  --spacing-2xl: 3rem;     /* 48px */
  --spacing-3xl: 4rem;     /* 64px */
  
  /* 页面布局 */
  --page-max-width: 800px;
  --page-padding: 2rem;
  --content-padding: 1.5rem;
}

/* 基础重置样式 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* 页面基础样式 */
html {
  font-size: 16px;
  line-height: var(--line-height-normal);
}

body {
  font-family: var(--font-family-base);
  font-size: var(--font-size-body);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-relaxed);
  letter-spacing: var(--letter-spacing-normal);
  color: #2c3e50;
  background-color: #ffffff;
  max-width: var(--page-max-width);
  margin: 0 auto;
  padding: var(--page-padding);
}

/* 标题基础样式 */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-base);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  letter-spacing: var(--letter-spacing-tight);
  margin-top: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
  color: #1a202c;
}

h1 {
  font-size: var(--font-size-h1);
  margin-top: 0;
  margin-bottom: var(--spacing-2xl);
}

h2 {
  font-size: var(--font-size-h2);
  margin-top: var(--spacing-2xl);
}

h3 {
  font-size: var(--font-size-h3);
}

h4 {
  font-size: var(--font-size-h4);
  font-weight: var(--font-weight-medium);
}

h5 {
  font-size: var(--font-size-h5);
  font-weight: var(--font-weight-medium);
}

h6 {
  font-size: var(--font-size-h6);
  font-weight: var(--font-weight-medium);
  color: #4a5568;
}

/* 段落样式 */
p {
  margin-bottom: var(--spacing-lg);
  line-height: var(--line-height-relaxed);
}

/* 强调文本 */
strong, b {
  font-weight: var(--font-weight-bold);
}

em, i {
  font-style: italic;
}

/* 列表样式 */
ul, ol {
  margin-bottom: var(--spacing-lg);
  padding-left: var(--spacing-xl);
}

li {
  margin-bottom: var(--spacing-sm);
  line-height: var(--line-height-relaxed);
}

/* 引用块样式 */
blockquote {
  margin: var(--spacing-xl) 0;
  padding: var(--spacing-lg);
  border-left: 4px solid #e2e8f0;
  background-color: #f7fafc;
  font-style: italic;
  color: #4a5568;
}

/* 代码样式 */
code {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.875em;
  background-color: #f1f5f9;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  color: #1e293b;
}

pre {
  margin: var(--spacing-lg) 0;
  padding: var(--spacing-lg);
  background-color: #f8fafc;
  border-radius: 0.5rem;
  overflow-x: auto;
  border: 1px solid #e2e8f0;
}

pre code {
  background-color: transparent;
  padding: 0;
  border-radius: 0;
  font-size: 0.875rem;
  line-height: var(--line-height-relaxed);
}

/* 链接样式 */
a {
  color: #3182ce;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: border-color 0.2s ease;
}

a:hover {
  border-bottom-color: #3182ce;
}

/* 表格样式 */
table {
  width: 100%;
  margin: var(--spacing-xl) 0;
  border-collapse: collapse;
  font-size: var(--font-size-small);
}

th, td {
  padding: var(--spacing-sm) var(--spacing-md);
  text-align: left;
  border-bottom: 1px solid #e2e8f0;
}

th {
  font-weight: var(--font-weight-medium);
  background-color: #f7fafc;
  color: #2d3748;
}

/* 分隔线 */
hr {
  margin: var(--spacing-2xl) 0;
  border: none;
  height: 1px;
  background-color: #e2e8f0;
}

/* 图片样式 */
img {
  max-width: 100%;
  height: auto;
  margin: var(--spacing-lg) 0;
  border-radius: 0.5rem;
}

/* 打印样式优化 */
@media print {
  /* 确保打印时字体正确显示 */
  body {
    font-family: var(--font-family-base);
    color: #000000;
    background-color: #ffffff;
    font-size: 12pt;
    line-height: 1.5;
  }
  
  /* 标题打印优化 */
  h1 { font-size: 24pt; }
  h2 { font-size: 20pt; }
  h3 { font-size: 16pt; }
  h4 { font-size: 14pt; }
  h5 { font-size: 12pt; }
  h6 { font-size: 11pt; }
  
  /* 避免分页断裂 */
  h1, h2, h3, h4, h5, h6 {
    page-break-after: avoid;
  }
  
  /* 段落分页优化 */
  p, blockquote {
    orphans: 3;
    widows: 3;
  }
  
  /* 代码块分页优化 */
  pre {
    page-break-inside: avoid;
  }
  
  /* 表格分页优化 */
  table {
    page-break-inside: avoid;
  }
  
  /* 链接打印优化 */
  a {
    color: #000000;
    text-decoration: underline;
  }
  
  /* 背景色打印优化 */
  blockquote {
    background-color: transparent;
    border-left: 2pt solid #cccccc;
  }
  
  code {
    background-color: #f5f5f5;
  }
  
  pre {
    background-color: #f8f8f8;
    border: 1pt solid #cccccc;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  :root {
    --font-size-h1: 2rem;
    --font-size-h2: 1.75rem;
    --font-size-h3: 1.375rem;
    --page-padding: 1rem;
  }
}

