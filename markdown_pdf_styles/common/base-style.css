// 引入思源黑体
@import url("https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap");

// ===== 统一字号系统 - 基于1.25倍比例 =====
@font-size-xs: 0.75rem;    // 12px - 极小文字（页码、标注）
@font-size-sm: 0.875rem;   // 14px - 小文字（代码、表格）
@font-size-base: 1rem;     // 16px - 基础文字（正文、列表）
@font-size-md: 1.125rem;   // 18px - 中等文字（引用、首段）
@font-size-lg: 1.25rem;    // 20px - 大文字（H4）
@font-size-xl: 1.5rem;     // 24px - 特大文字（H3）
@font-size-2xl: 1.875rem;  // 30px - 超大文字（H2）
@font-size-3xl: 2.25rem;   // 36px - 巨大文字（H1）

// ===== 统一行高系统 =====
@line-height-tight: 1.25;   // 紧凑行高（大标题）
@line-height-snug: 1.375;   // 略紧行高（小标题）
@line-height-normal: 1.6;   // 标准行高（正文，略微调大）
@line-height-relaxed: 1.75; // 舒适行高（引用，略微调大）
@line-height-loose: 1.85;   // 宽松行高（特殊内容，略微调大）

// ===== 统一字重系统 =====
@font-weight-light: 300;
@font-weight-normal: 400;
@font-weight-medium: 500;
@font-weight-semibold: 600;
@font-weight-bold: 700;

// ===== 高级色彩系统 - 微调，更克制和柔和 =====
@color-neutral-50: #F9FAFB;
@color-neutral-100: #F3F4F6;
@color-neutral-200: #E5E7EB;
@color-neutral-300: #D1D5DB;
@color-neutral-400: #9CA3AF;
@color-neutral-500: #6B7280;
@color-neutral-600: #4B5563;
@color-neutral-700: #374151;
@color-neutral-800: #1F2937;
@color-neutral-900: #111827;

@color-primary: #3B82F6; // 柔和的蓝色
@color-primary-light: #60A5FA;
@color-primary-dark: #2563EB;

// 渐变色系 - 调整为更柔和的商务渐变
@gradient-primary: linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%); // 蓝紫渐变
@gradient-accent: linear-gradient(135deg, #3B82F6 0%, #10B981 100%); // 蓝绿渐变

// 阴影系统 - 保持不变，已很精致
@shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.04);
@shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.06), 0 1px 2px rgba(0, 0, 0, 0.04);
@shadow-md: 0 4px 6px rgba(0, 0, 0, 0.05), 0 2px 4px rgba(0, 0, 0, 0.03);
@shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.08), 0 4px 6px rgba(0, 0, 0, 0.04);

// 间距系统 - 保持不变，已很精致
@space-1: 0.25rem;  // 4px
@space-2: 0.5rem;   // 8px
@space-3: 0.75rem;  // 12px
@space-4: 1rem;     // 16px
@space-5: 1.25rem;  // 20px
@space-6: 1.5rem;   // 24px
@space-8: 2rem;     // 32px
@space-10: 2.5rem;  // 40px
@space-12: 3rem;    // 48px
@space-16: 4rem;    // 64px

.markdown-preview.markdown-preview {
  // ===== 基础设置 =====
  font-family: "Noto Sans SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
  font-size: @font-size-base;        // 16px
  line-height: @line-height-normal;  // 1.6
  letter-spacing: 0.01em;            // 略微调大字间距，提升阅读舒适度
  color: @color-neutral-800;         // 调整为更深的文本颜色，对比度更高
  font-weight: @font-weight-normal;
  font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: @color-neutral-50;     // 纯色背景，更简洁
  
  // ===== 标题系统 - 严格按比例递减 =====
  h1, h2, h3, h4, h5, h6 {
    font-family: "Noto Sans SC", sans-serif;
    margin: 0;
    scroll-margin-top: @space-10;
    letter-spacing: -0.015em;          // 标题字间距略微收紧
  }
  
  h1 {
    font-size: @font-size-3xl;          // 36px
    line-height: @line-height-tight;    // 1.25
    font-weight: @font-weight-bold;     // 700
    margin: @space-12 0 @space-8 0;     // 48px 0 32px 0
    text-align: center;
    position: relative;
    
    // 移除动画，使用纯色背景和文本颜色
    background: none;
    color: @color-neutral-900;          // 纯黑，更正式
    
    &::after {
      content: ";
      position: absolute;
      bottom: -@space-2;
      left: 50%;
      transform: translateX(-50%);
      width: 80px;                     // 宽度略增
      height: 4px;                     // 粗细略增
      background: @color-primary;      // 使用主色
      border-radius: 2px;
      opacity: 1;                      // 完全不透明
    }
  }
  
  h2 {
    font-size: @font-size-2xl;          // 30px
    line-height: @line-height-tight;    // 1.25
    font-weight: @font-weight-semibold; // 600
    color: @color-neutral-800;
    margin: @space-10 0 @space-6 0;     // 40px 0 24px 0
    position: relative;
    padding-left: @space-5;             // 增加左侧内边距
    
    &::before {
      content: ";
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 5px;                      // 粗细略增
      background: @color-primary;      // 使用主色
      border-radius: 2px;
      opacity: 1;                      // 完全不透明
    }
  }
  
  h3 {
    font-size: @font-size-xl;           // 24px
    line-height: @line-height-snug;     // 1.375
    font-weight: @font-weight-semibold; // 600
    color: @color-neutral-700;
    margin: @space-8 0 @space-5 0;      // 32px 0 20px 0
    border-bottom: 1px solid @color-neutral-200; // 增加底部细线
    padding-bottom: @space-2;           // 增加底部内边距
  }
  
  h4 {
    font-size: @font-size-lg;           // 20px
    line-height: @line-height-snug;     // 1.375
    font-weight: @font-weight-medium;   // 500
    color: @color-neutral-700;
    margin: @space-6 0 @space-4 0;      // 24px 0 16px 0
  }
  
  h5 {
    font-size: @font-size-md;           // 18px
    line-height: @line-height-normal;   // 1.6
    font-weight: @font-weight-medium;   // 500
    color: @color-neutral-600;
    margin: @space-5 0 @space-3 0;      // 20px 0 12px 0
  }
  
  h6 {
    font-size: @font-size-base;         // 16px
    line-height: @line-height-normal;   // 1.6
    font-weight: @font-weight-medium;   // 500
    color: @color-neutral-500;
    margin: @space-4 0 @space-2 0;      // 16px 0 8px 0
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }
  
  // ===== 正文段落 =====
  p {
    font-size: @font-size-base;         // 16px
    line-height: @line-height-normal;   // 1.6
    font-weight: @font-weight-normal;   // 400
    color: @color-neutral-700;
    margin: @space-4 0;                 // 16px 0
    text-align: justify;                // 文本两端对齐，更正式
    
    // 首段特殊处理
    &:first-of-type {
      font-size: @font-size-md;         // 18px
      line-height: @line-height-relaxed; // 1.75
      font-weight: @font-weight-light;  // 300
      color: @color-neutral-600;
    }
    
    & + p {
      margin-top: @space-5;             // 20px
    }
  }
  
  // ===== 列表系统 - 统一协调 =====
  ul, ol {
    font-size: @font-size-base;         // 16px - 与正文一致
    line-height: @line-height-normal;   // 1.6 - 与正文一致
    font-weight: @font-weight-normal;   // 400
    color: @color-neutral-700;
    margin: @space-4 0;                 // 16px 0 - 与段落一致
    padding-left: @space-6;             // 24px
    
    li {
      margin: @space-2 0;               // 8px 0 - 适中间距
      position: relative;
      
      &::marker {
        color: @color-primary;          // 标记颜色使用主色
        font-weight: @font-weight-medium; // 500
        font-size: 1em;                 // 标记字号与列表项一致
      }
      
      &:hover {
        color: @color-neutral-800;
        transition: color 0.2s ease;
      }
    }
    
    // 嵌套列表
    ul, ol {
      font-size: @font-size-sm;         // 14px - 嵌套稍小
      margin: @space-1 0;               // 4px 0
      opacity: 0.95;
    }
  }
  
  // 有序列表特殊处理
  ol li {
    padding-left: @space-1;             // 4px - 给数字后内容小间距
    
    &::marker {
      font-weight: @font-weight-semibold; // 600 - 数字稍重
      font-size: 0.9em;                 // 数字略小
    }
  }
  
  // ===== 引用块 =====
  blockquote {
    font-size: @font-size-md;           // 18px - 比正文大一级
    line-height: @line-height-relaxed;  // 1.75
    font-weight: @font-weight-light;    // 300
    font-style: italic;
    color: @color-neutral-600;
    margin: @space-6 0;                 // 24px 0
    padding: @space-5 @space-6;         // 20px 24px
    position: relative;
    background: @color-neutral-50;      // 纯色背景，更简洁
    border-radius: 8px;                 // 略微减小圆角
    border-left: 4px solid @color-primary; // 左侧边框使用主色
    box-shadow: @shadow-sm;
    
    &::before {
      content: ";
      font-size: 2.5rem;                // 引用符号略小
      color: @color-primary;
      opacity: 0.1;
      position: absolute;
      top: @space-1;
      left: @space-3;
      font-family: Georgia, serif;
      line-height: 1;
    }
    
    p {
      font-size: inherit;               // 继承引用块字号
      line-height: inherit;
      font-weight: inherit;
      margin: 0;
      position: relative;
      z-index: 1;
    }
  }
  
  // ===== 代码系统 =====
  code {
    font-family: "JetBrains Mono", "Fira Code", "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace;
    font-size: @font-size-sm;           // 14px - 比正文小一级
    line-height: @line-height-normal;   // 1.6
    font-weight: @font-weight-medium;   // 500
    color: @color-primary-dark;
    background: @color-neutral-100;     // 纯色背景
    padding: @space-1 @space-2;         // 4px 8px
    border-radius: 4px;                 // 略微减小圆角
    border: 1px solid @color-neutral-200;
    box-shadow: none;                   // 移除阴影，更简洁
    font-feature-settings: "liga" 1, "calt" 1;
  }
  
  pre {
    font-family: "JetBrains Mono", "Fira Code", "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace;
    font-size: @font-size-sm;           // 14px - 与行内代码一致
    line-height: @line-height-relaxed;  // 1.75 - 代码块稍松
    font-weight: @font-weight-normal;   // 400
    color: @color-neutral-700;
    background: @color-neutral-100;     // 纯色背景
    padding: @space-6;                  // 24px
    border-radius: 8px;                 // 略微减小圆角
    margin: @space-6 0;                 // 24px 0
    overflow-x: auto;
    border: 1px solid @color-neutral-200;
    box-shadow: @shadow-sm;             // 调整为小阴影
    position: relative;
    
    &::before {
      content: ";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;                     // 粗细略减
      background: @color-primary;      // 使用主色
      border-radius: 8px 8px 0 0;
    }
    
    code {
      background: transparent;
      padding: 0;
      border: none;
      box-shadow: none;
      color: inherit;
      font-size: inherit;
      font-weight: inherit;
    }
  }
  
  // ===== 表格系统 =====
  table {
    font-size: @font-size-sm;           // 14px - 表格统一小字号
    line-height: @line-height-normal;   // 1.6
    font-weight: @font-weight-normal;   // 400
    color: @color-neutral-700;
    border-collapse: collapse;           // 调整为合并边框，更简洁
    border-spacing: 0;
    margin: @space-6 0;                 // 24px 0
    width: 100%;
    background: #ffffff;
    border-radius: 8px;                 // 略微减小圆角
    overflow: hidden;
    box-shadow: @shadow-md;             // 调整为中等阴影
    border: 1px solid @color-neutral-200;
    
    th {
      font-size: @font-size-xs;         // 12px - 表头更小
      font-weight: @font-weight-semibold; // 600
      color: @color-neutral-800;
      background: @color-neutral-100;    // 纯色背景
      padding: @space-3 @space-4;       // 12px 16px
      text-align: left;
      border-bottom: 1px solid @color-neutral-300; // 细线
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }
    
    td {
      padding: @space-3 @space-4;       // 12px 16px - 与表头一致
      border-bottom: 1px solid @color-neutral-200;
      transition: all 0.2s ease;
    }
    
    tr:nth-child(even) {                // 斑马线效果
      background: @color-neutral-50;
    }
    
    tr:hover {
      background: @color-neutral-100;    // 纯色hover背景
      transform: none;                   // 移除位移动画
      box-shadow: none;                  // 移除阴影
    }
  }
  
  // ===== 链接系统 =====
  a {
    font-size: inherit;                 // 继承父元素字号
    font-weight: @font-weight-medium;   // 500 - 统一中等字重
    color: @color-primary;
    text-decoration: none;
    position: relative;
    transition: color 0.3s ease;
    
    &::after {
      content: ";
      position: absolute;
      bottom: -1px;                    // 下划线位置略高
      left: 0;
      width: 100%;                     // 默认显示下划线
      height: 1px;                     // 细线
      background: @color-primary-light; // 使用浅色主色
      transition: none;                // 移除动画
    }
    
    &:hover {
      color: @color-primary-dark;
      transform: none;                   // 移除位移动画
      
      &::after {
        width: 100%;
      }
    }
  }
  
  // ===== 强调文本 =====
  strong, b {
    font-weight: @font-weight-semibold; // 600 - 统一半粗体
    color: @color-neutral-900;          // 更深的颜色
  }
  
  em, i {
    font-style: italic;
    font-weight: @font-weight-normal;   // 400 - 斜体使用正常字重
    color: @color-neutral-600;
  }
  
  // ===== 分割线 =====
  hr {
    border: none;
    height: 1px;
    background: @color-neutral-200;     // 纯色细线
    margin: @space-8 0;                 // 32px 0
    position: relative;
    
    &::after {
      content: ";
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: #ffffff;
      color: @color-neutral-400;
      padding: 0 @space-3;
      font-size: @font-size-xs;         // 12px
    }
  }
  
  // ===== 特殊元素 =====
  mark {
    font-size: inherit;
    font-weight: @font-weight-medium;   // 500
    background: @color-neutral-200;     // 纯色背景
    color: @color-neutral-800;
    padding: @space-1 @space-2;
    border-radius: 4px;
    border: none;                       // 移除边框
  }
  
  // ===== 响应式设计 =====
  @media (max-width: 768px) {
    font-size: @font-size-sm;           // 14px - 移动端基础字号
    
    h1 { font-size: @font-size-2xl; }   // 30px
    h2 { font-size: @font-size-xl; }    // 24px
    h3 { font-size: @font-size-lg; }    // 20px
    h4 { font-size: @font-size-md; }    // 18px
    h5 { font-size: @font-size-base; }  // 16px
    h6 { font-size: @font-size-sm; }    // 14px
    
    p, ul, ol {
      font-size: @font-size-sm;         // 14px
    }
    
    blockquote {
      font-size: @font-size-base;       // 16px
    }
    
    code, pre {
      font-size: @font-size-xs;         // 12px
    }
    
    table {
      font-size: @font-size-xs;         // 12px
      
      th {
        font-size: 11px;                // 更小
      }
    }
  }
  
  // ===== 打印样式 - 强化一致性 =====
  @media print {
    font-size: 12pt;                    // 打印标准字号
    color: #000000;                     // 确保打印为黑色
    background: #ffffff;                // 确保打印背景为白色
    
    h1, h2, h3, h4, h5, h6, p, ul, ol, blockquote, pre, table {
      animation: none !important;       // 移除所有动画
      background: none !important;      // 移除所有背景色
      box-shadow: none !important;      // 移除所有阴影
      text-shadow: none !important;     // 移除所有文本阴影
      color: #000000 !important;        // 确保所有文本为黑色
    }
    
    h1 {
      font-size: 18pt;
      border-bottom: 2pt solid #000000; // 打印时使用纯色边框
      &::after {
        background: #666666 !important; // 打印时使用纯色
      }
    }
    h2 {
      font-size: 16pt;
      &::before {
        background: #666666 !important; // 打印时使用纯色
      }
    }
    h3 { font-size: 14pt; border-bottom: 1pt solid #cccccc !important; }
    h4 { font-size: 13pt; }
    h5 { font-size: 12pt; }
    h6 { font-size: 11pt; }
    
    p, ul, ol { font-size: 11pt; }
    blockquote {
      font-size: 12pt;
      border-left-color: #666666 !important; // 打印时使用纯色边框
      &::before {
        color: #cccccc !important;       // 打印时引用符号颜色
      }
    }
    code, pre {
      font-size: 10pt;
      border-color: #cccccc !important; // 打印时边框颜色
      &::before {
        background: #cccccc !important; // 打印时顶部装饰线颜色
      }
    }
    table {
      font-size: 10pt;
      border-color: #cccccc !important; // 打印时边框颜色
      th {
        background: #f0f0f0 !important; // 打印时表头背景
        color: #000000 !important;
        border-bottom-color: #999999 !important;
      }
      td {
        border-bottom-color: #e0e0e0 !important;
      }
      tr:nth-child(even) {
        background: #f8f8f8 !important;
      }
      tr:hover {
        background: none !important;
      }
    }
    a {
      color: #0000EE !important;         // 打印时链接颜色
      text-decoration: underline !important; // 打印时显示下划线
      &::after {
        display: none !important;         // 隐藏自定义下划线
      }
    }
    strong, b {
      color: #000000 !important;
    }
    em, i {
      color: #333333 !important;
    }
    hr {
      background: #cccccc !important;
      &::after {
        color: #999999 !important;
      }
    }
    mark {
      background: #ffff00 !important;    // 打印时高亮颜色
      color: #000000 !important;
    }
  }
}

// 移除动画效果，因为PDF不支持动画
@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.markdown-preview.markdown-preview {
  h1, h2, h3, h4, h5, h6, p, ul, ol, blockquote, pre, table {
    animation: none; // 移除动画
  }
}


