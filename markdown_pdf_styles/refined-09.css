// Refined Version 02 - 现代简约 (保留原配色和视觉效果，微调细节)
// 引入更丰富的字体 (保留用户原字体)
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");
@import url("https://at.alicdn.com/wf/webfont/jDO0xEvBqtj/Alibaba-PuHuiTi-3.0-55-Regular.css");
@import url("https://at.alicdn.com/wf/webfont/jDO0xEvBqtj/Alibaba-PuHuiTi-3.0-65-Medium.css");
@import url("https://at.alicdn.com/wf/webfont/jDO0xEvBqtj/Alibaba-PuHuiTi-3.0-75-SemiBold.css");

// ===== 统一字号系统 - 基于1.25倍比例 ===== (微调字号)
@font-size-xs: 0.75rem;    // 12px - 极小文字（页码、标注）
@font-size-sm: 0.875rem;   // 14px - 小文字（代码、表格）
@font-size-base: 1.05rem;  // 16.8px - 基础文字（正文、列表）微调增大
@font-size-md: 1.175rem;   // 18.8px - 中等文字（引用、首段）微调增大
@font-size-lg: 1.3rem;     // 20.8px - 大文字（H4）微调增大
@font-size-xl: 1.55rem;    // 24.8px - 特大文字（H3）微调增大
@font-size-2xl: 1.925rem;  // 30.8px - 超大文字（H2）微调增大
@font-size-3xl: 2.3rem;    // 36.8px - 巨大文字（H1）微调增大

// ===== 统一行高系统 ===== (微调优化)
@line-height-tight: 1.2;    // 紧凑行高（大标题）微调
@line-height-snug: 1.35;    // 略紧行高（小标题）微调
@line-height-normal: 1.6;   // 标准行高（正文，微调提升阅读舒适度）
@line-height-relaxed: 1.7;  // 舒适行高（引用，微调）
@line-height-loose: 1.85;   // 宽松行高（特殊内容，微调）

// ===== 统一字重系统 ===== (保留用户原定义)
@font-weight-light: 300;
@font-weight-normal: 400;
@font-weight-medium: 500;
@font-weight-semibold: 600;
@font-weight-bold: 700;

// ===== 高级色彩系统 ===== (保留用户原定义)
@color-neutral-50: #FAFBFC;
@color-neutral-100: #F4F6F8;
@color-neutral-200: #E7EAEE;
@color-neutral-300: #D1D9E0;
@color-neutral-400: #B0BEC5;
@color-neutral-500: #78909C;
@color-neutral-600: #546E7A;
@color-neutral-700: #37474F;
@color-neutral-800: #263238;
@color-neutral-900: #1A1A1A;

@color-primary: #0066FF;
@color-primary-light: #3385FF;
@color-primary-dark: #0052CC;

// 渐变色系 (保留用户原定义)
@gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
@gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);

// 阴影系统 (保留用户原定义)
@shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.04);
@shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.06), 0 1px 2px rgba(0, 0, 0, 0.04);
@shadow-md: 0 4px 6px rgba(0, 0, 0, 0.05), 0 2px 4px rgba(0, 0, 0, 0.03);
@shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.08), 0 4px 6px rgba(0, 0, 0, 0.04);

// 间距系统 (微调间距)
@space-1: 0.25rem;  // 4px
@space-2: 0.5rem;   // 8px
@space-3: 0.75rem;  // 12px
@space-4: 1.1rem;   // 17.6px 微调增大
@space-5: 1.35rem;  // 21.6px 微调增大
@space-6: 1.6rem;   // 25.6px 微调增大
@space-8: 2.1rem;   // 33.6px 微调增大
@space-10: 2.6rem;  // 41.6px 微调增大
@space-12: 3.1rem;  // 49.6px 微调增大
@space-16: 4.1rem;  // 65.6px 微调增大

.markdown-preview.markdown-preview {
  // ===== 基础设置 ===== (微调字间距)
  font-family: 'Inter', 'Alibaba PuHuiTi 3.0', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  font-size: @font-size-base;        // 16.8px 微调增大
  line-height: @line-height-normal;  // 1.6 微调
  letter-spacing: -0.005em;          // 微调字间距，更舒适
  color: @color-neutral-700;
  font-weight: @font-weight-normal;
  font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(135deg, @color-neutral-50 0%, #ffffff 100%);
  
  // ===== 标题系统 - 严格按比例递减 ===== (微调字间距和下划线)
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Inter', 'Alibaba PuHuiTi 3.0', sans-serif;
    margin: 0;
    scroll-margin-top: @space-10;
    letter-spacing: -0.015em;          // 微调标题字间距
  }
  
  h1 {
    font-size: @font-size-3xl;          // 36.8px 微调增大
    line-height: @line-height-tight;    // 1.2 微调
    font-weight: @font-weight-bold;     // 700
    margin: @space-12 0 @space-8 0;     // 微调间距
    text-align: center;
    position: relative;
    
    // 保留原渐变背景和动画
    background: @gradient-primary;
    background-size: 200% 200%;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradient-shift 6s ease-in-out infinite;
    
    &::after {
      content: '';
      position: absolute;
      bottom: -@space-2;
      left: 50%;
      transform: translateX(-50%);
      width: 70px;                     // 微调下划线宽度
      height: 3.5px;                   // 微调下划线粗细
      background: @gradient-accent;
      border-radius: 2px;
      opacity: 0.8;
    }
  }
  
  h2 {
    font-size: @font-size-2xl;          // 30.8px 微调增大
    line-height: @line-height-tight;    // 1.2 微调
    font-weight: @font-weight-semibold; // 600
    color: @color-neutral-800;
    margin: @space-10 0 @space-6 0;     // 微调间距
    position: relative;
    padding-left: @space-4;
    
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 4.5px;                    // 微调侧边条宽度
      background: @gradient-primary;
      border-radius: 2px;
      opacity: 0.8;
    }
  }
  
  h3 {
    font-size: @font-size-xl;           // 24.8px 微调增大
    line-height: @line-height-snug;     // 1.35 微调
    font-weight: @font-weight-semibold; // 600
    color: @color-neutral-700;
    margin: @space-8 0 @space-5 0;      // 微调间距
  }
  
  h4 {
    font-size: @font-size-lg;           // 20.8px 微调增大
    line-height: @line-height-snug;     // 1.35 微调
    font-weight: @font-weight-medium;   // 500
    color: @color-neutral-700;
    margin: @space-6 0 @space-4 0;      // 微调间距
  }
  
  h5 {
    font-size: @font-size-md;           // 18.8px 微调增大
    line-height: @line-height-normal;   // 1.6 微调
    font-weight: @font-weight-medium;   // 500
    color: @color-neutral-600;
    margin: @space-5 0 @space-3 0;      // 微调间距
  }
  
  h6 {
    font-size: @font-size-base;         // 16.8px 微调增大
    line-height: @line-height-normal;   // 1.6 微调
    font-weight: @font-weight-medium;   // 500
    color: @color-neutral-500;
    margin: @space-4 0 @space-2 0;      // 微调间距
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }
  
  // ===== 正文段落 ===== (微调段落间距)
  p {
    font-size: @font-size-base;         // 16.8px 微调增大
    line-height: @line-height-normal;   // 1.6 微调
    font-weight: @font-weight-normal;   // 400
    color: @color-neutral-700;
    margin: @space-4 0;                 // 微调间距
    
    // 首段特殊处理
    &:first-of-type {
      font-size: @font-size-md;         // 18.8px 微调增大
      line-height: @line-height-relaxed; // 1.7 微调
      font-weight: @font-weight-light;  // 300
      color: @color-neutral-600;
    }
    
    & + p {
      margin-top: @space-5;             // 微调间距
    }
  }
  
  // ===== 列表系统 - 统一协调 ===== (微调列表项间距)
  ul, ol {
    font-size: @font-size-base;         // 16.8px - 与正文一致 微调增大
    line-height: @line-height-normal;   // 1.6 - 与正文一致 微调
    font-weight: @font-weight-normal;   // 400
    color: @color-neutral-700;
    margin: @space-4 0;                 // 微调间距 - 与段落一致
    padding-left: @space-6;             // 微调间距
    
    li {
      margin: @space-2 0;               // 8px 0 - 适中间距
      position: relative;
      
      &::marker {
        color: @color-primary;
        font-weight: @font-weight-medium; // 500
        font-size: 0.9em;               // 稍小的标记
      }
      
      &:hover {
        color: @color-neutral-800;
        transition: color 0.2s ease;
      }
    }
    
    // 嵌套列表
    ul, ol {
      font-size: @font-size-sm;         // 14px - 嵌套稍小
      margin: @space-1 0;               // 4px 0
      opacity: 0.95;
    }
  }
  
  // 有序列表特殊处理
  ol li {
    padding-left: @space-1;             // 4px - 给数字后内容小间距
    
    &::marker {
      font-weight: @font-weight-semibold; // 600 - 数字稍重
      font-size: 0.85em;                // 数字更小
    }
  }
  
  // ===== 引用块 ===== (保留原渐变背景，微调内边距)
  blockquote {
    font-size: @font-size-md;           // 18.8px - 比正文大一级 微调增大
    line-height: @line-height-relaxed;  // 1.7 微调
    font-weight: @font-weight-light;    // 300
    font-style: italic;
    color: @color-neutral-600;
    margin: @space-6 0;                 // 微调间距
    padding: @space-5 @space-6;         // 微调内边距
    position: relative;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.03) 0%, rgba(255, 255, 255, 0.8) 100%);
    border-radius: 12px;
    border-left: 4px solid transparent;
    border-image: @gradient-primary 1;
    box-shadow: @shadow-sm;
    
    &::before {
      content: '"';
      font-size: 3.2rem;               // 微调引用符号大小
      color: @color-primary;
      opacity: 0.2;
      position: absolute;
      top: -@space-1;
      left: @space-4;
      font-family: Georgia, serif;
      line-height: 1;
    }
    
    p {
      font-size: inherit;               // 继承引用块字号
      line-height: inherit;
      font-weight: inherit;
      margin: 0;
      position: relative;
      z-index: 1;
    }
  }
  
  // ===== 代码系统 ===== (保留原渐变背景，微调字号)
  code {
    font-family: 'JetBrains Mono', 'Fira Code', 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    font-size: @font-size-sm;           // 14px - 比正文小一级
    line-height: @line-height-normal;   // 1.6 微调
    font-weight: @font-weight-medium;   // 500
    color: @color-primary-dark;
    background: linear-gradient(135deg, @color-neutral-100 0%, @color-neutral-50 100%);
    padding: @space-1 @space-2;         // 4px 8px
    border-radius: 6px;
    border: 1px solid @color-neutral-200;
    box-shadow: @shadow-xs;
    font-feature-settings: 'liga' 1, 'calt' 1;
  }
  
  pre {
    font-family: 'JetBrains Mono', 'Fira Code', 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    font-size: @font-size-sm;           // 14px - 与行内代码一致
    line-height: @line-height-relaxed;  // 1.7 - 代码块稍松 微调
    font-weight: @font-weight-normal;   // 400
    color: @color-neutral-700;
    background: linear-gradient(135deg, @color-neutral-100 0%, #ffffff 100%);
    padding: @space-6;                  // 微调内边距
    border-radius: 12px;
    margin: @space-6 0;                 // 微调间距
    overflow-x: auto;
    border: 1px solid @color-neutral-200;
    box-shadow: @shadow-md;
    position: relative;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: @gradient-accent;
      border-radius: 12px 12px 0 0;
    }
    
    code {
      background: transparent;
      padding: 0;
      border: none;
      box-shadow: none;
      color: inherit;
      font-size: inherit;
      font-weight: inherit;
    }
  }
  
  // ===== 表格系统 ===== (保留原渐变背景，微调内边距)
  table {
    font-size: @font-size-sm;           // 14px - 表格统一小字号
    line-height: @line-height-normal;   // 1.6 微调
    font-weight: @font-weight-normal;   // 400
    color: @color-neutral-700;
    border-collapse: separate;
    border-spacing: 0;
    margin: @space-6 0;                 // 微调间距
    width: 100%;
    background: #ffffff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: @shadow-lg;
    border: 1px solid @color-neutral-200;
    
    th {
      font-size: @font-size-xs;         // 12px - 表头更小
      font-weight: @font-weight-semibold; // 600
      color: @color-neutral-800;
      background: linear-gradient(135deg, @color-neutral-100 0%, @color-neutral-50 100%);
      padding: @space-3 @space-4;       // 12px 16px
      text-align: left;
      border-bottom: 2px solid @color-neutral-300;
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }
    
    td {
      padding: @space-3 @space-4;       // 12px 16px - 与表头一致
      border-bottom: 1px solid @color-neutral-200;
      transition: all 0.2s ease;
    }
    
    tr:hover {
      background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(255, 255, 255, 0.8) 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }
  }
  
  // ===== 链接系统 ===== (保留原渐变下划线和动画)
  a {
    font-size: inherit;                 // 继承父元素字号
    font-weight: @font-weight-medium;   // 500 - 统一中等字重
    color: @color-primary;
    text-decoration: none;
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    
    &::after {
      content: '';
      position: absolute;
      bottom: -2px;
      left: 0;
      width: 0;
      height: 2px;
      background: @gradient-accent;
      transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
    
    &:hover {
      color: @color-primary-dark;
      transform: translateY(-1px);
      
      &::after {
        width: 100%;
      }
    }
  }
  
  // ===== 强调文本 ===== (保留原设置)
  strong, b {
    font-weight: @font-weight-semibold; // 600 - 统一半粗体
    color: @color-neutral-800;
  }
  
  em, i {
    font-style: italic;
    font-weight: @font-weight-light;    // 300 - 斜体用细体
    color: @color-neutral-600;
  }
  
  // ===== 分割线 ===== (保留原渐变背景)
  hr {
    border: none;
    height: 1px;
    background: linear-gradient(90deg, transparent, @color-neutral-300, transparent);
    margin: @space-8 0;                 // 微调间距
    position: relative;
    
    &::after {
      content: '◆';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: #ffffff;
      color: @color-neutral-400;
      padding: 0 @space-3;
      font-size: @font-size-xs;         // 12px
    }
  }
  
  // ===== 特殊元素 ===== (保留原渐变背景)
  mark {
    font-size: inherit;
    font-weight: @font-weight-medium;   // 500
    background: linear-gradient(120deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    color: @color-neutral-800;
    padding: @space-1 @space-2;
    border-radius: 4px;
    border: 1px solid rgba(102, 126, 234, 0.2);
  }
  
  // ===== 响应式设计 ===== (保留原设置)
  @media (max-width: 768px) {
    font-size: @font-size-sm;           // 14px - 移动端基础字号
    
    h1 { font-size: @font-size-2xl; }   // 30px
    h2 { font-size: @font-size-xl; }    // 24px
    h3 { font-size: @font-size-lg; }    // 20px
    h4 { font-size: @font-size-md; }    // 18px
    h5 { font-size: @font-size-base; }  // 16px
    h6 { font-size: @font-size-sm; }    // 14px
    
    p, ul, ol {
      font-size: @font-size-sm;         // 14px
    }
    
    blockquote {
      font-size: @font-size-base;       // 16px
    }
    
    code, pre {
      font-size: @font-size-xs;         // 12px
    }
    
    table {
      font-size: @font-size-xs;         // 12px
      
      th {
        font-size: 11px;                // 更小
      }
    }
  }
  
  // ===== 打印样式 - 优雅降级处理 ===== (与版本01相同的打印处理)
  @media print {
    font-size: 12pt;                    // 打印标准字号
    color: #000000 !important;          // 确保打印为黑色
    background: #ffffff !important;     // 确保打印背景为白色
    
    // 禁用所有动画和过渡
    * {
      animation: none !important;
      transition: none !important;
    }

    // H1标题：保留渐变效果的近似纯色版本
    h1 {
      background: none !important;
      -webkit-text-fill-color: initial !important;
      color: #4A4A8A !important;        // 渐变的近似纯色
      &::after {
        background: #4A90E2 !important; // 渐变的近似纯色
        opacity: 1 !important;
      }
    }

    // H2标题：保留侧边条的纯色版本
    h2::before {
      background: #4A4A8A !important;   // 渐变的近似纯色
      opacity: 1 !important;
    }

    // 引用块：保留边框，移除背景渐变
    blockquote {
      background: #F8F8F8 !important;   // 纯色背景
      border-left: 4px solid #4A4A8A !important; // 纯色边框
      border-image: none !important;
      box-shadow: none !important;
      &::before {
        color: #CCCCCC !important;       // 引用符号颜色
        opacity: 1 !important;
      }
    }

    // 代码块：保留结构，转换为纯色
    code {
      background: #F0F0F0 !important;   // 纯色背景
      border-color: #CCCCCC !important;
      box-shadow: none !important;
    }
    pre {
      background: #F0F0F0 !important;   // 纯色背景
      border-color: #CCCCCC !important;
      box-shadow: none !important;
      &::before {
        background: #4A90E2 !important; // 顶部装饰线纯色
      }
    }

    // 表格：保留结构，转换为纯色
    table {
      box-shadow: none !important;
      border-color: #CCCCCC !important;
      th {
        background: #F0F0F0 !important; // 表头纯色背景
        color: #000000 !important;
        border-bottom-color: #999999 !important;
      }
      td {
        border-bottom-color: #E0E0E0 !important;
      }
      tr:nth-child(even) {
        background: #F8F8F8 !important;
      }
      tr:hover {
        background: none !important;
        transform: none !important;
        box-shadow: none !important;
      }
    }

    // 链接：标准打印样式
    a {
      color: #0000EE !important;         // 标准链接蓝色
      text-decoration: underline !important;
      transform: none !important;
      &::after {
        display: none !important;         // 隐藏自定义下划线
      }
    }

    // 分割线：转换为纯色
    hr {
      background: #CCCCCC !important;
      &::after {
        color: #999999 !important;
      }
    }

    // 标记：标准高亮色
    mark {
      background: #FFFF00 !important;    // 标准黄色高亮
      color: #000000 !important;
      border: none !important;
    }

    // 其他元素确保为黑色
    h2, h3, h4, h5, h6, p, ul, ol, strong, b, em, i {
      color: #000000 !important;
    }

    // 打印时字号 (保留用户原打印字号)
    h1 { font-size: 18pt; }
    h2 { font-size: 16pt; }
    h3 { font-size: 14pt; }
    h4 { font-size: 13pt; }
    h5 { font-size: 12pt; }
    h6 { font-size: 11pt; }
    
    p, ul, ol { font-size: 11pt; }
    blockquote { font-size: 12pt; }
    code, pre { font-size: 10pt; }
    table { font-size: 10pt; }
  }
}

// ===== 动画效果 (保留用户原定义) =====
@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.markdown-preview.markdown-preview {
  h1, h2, h3, h4, h5, h6, p, ul, ol, blockquote, pre, table {
    animation: fade-in-up 0.6s ease-out;
  }
}

