/*
 * 学术系列 22 - 现代研究
 * Modern Research Style
 * 
 * 特色：现代研究风格，清晰简洁，适合科研报告、数据分析
 * 设计理念：现代学术排版，强调数据可视化和逻辑结构
 */

@import url('./common/base.css');
@import url('./common/variables.css');

:root {
  --primary-color: #2c3e50;
  --secondary-color: #34495e;
  --accent-color: #3498db;
  --text-primary: #2c3e50;
  --text-secondary: #7f8c8d;
  --background-color: #ffffff;
  --surface-color: #ecf0f1;
  --border-color: #bdc3c7;
}

body {
  background-color: var(--background-color);
  color: var(--text-primary);
  font-weight: var(--font-weight-normal);
  letter-spacing: 0.01em;
  line-height: 1.75;
  max-width: 900px;
  margin: 0 auto;
  padding: var(--spacing-2xl);
}

h1 {
  color: var(--primary-color);
  font-weight: var(--font-weight-bold);
  font-size: 2.8rem;
  text-align: center;
  margin-bottom: var(--spacing-2xl);
  padding: var(--spacing-lg) 0;
  border-bottom: 3px solid var(--accent-color);
  position: relative;
}

h1::after {
  content: '';
  position: absolute;
  bottom: -3px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 3px;
  background: var(--secondary-color);
}

h2 {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  font-size: 1.8rem;
  margin-top: var(--spacing-2xl);
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--surface-color);
  border-left: 4px solid var(--accent-color);
  border-radius: 0 var(--radius-md) var(--radius-md) 0;
}

h3 {
  color: var(--secondary-color);
  font-weight: var(--font-weight-medium);
  font-size: 1.4rem;
  margin-top: var(--spacing-xl);
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-xs);
  border-bottom: 2px solid var(--accent-color);
  display: inline-block;
}

h4 {
  color: var(--accent-color);
  font-weight: var(--font-weight-medium);
  font-size: 1.2rem;
  margin-top: var(--spacing-lg);
  margin-bottom: var(--spacing-sm);
}

/* 段落和文本 */
p {
  line-height: 1.8;
  margin-bottom: var(--spacing-lg);
  text-align: justify;
}

strong, b {
  color: var(--primary-color);
  font-weight: var(--font-weight-bold);
  background: linear-gradient(180deg, transparent 70%, rgba(52, 152, 219, 0.2) 70%);
}

/* 列表样式 */
ul li::before {
  content: '▸';
  position: absolute;
  left: 0;
  color: var(--accent-color);
  font-weight: bold;
}

ol li {
  padding-left: var(--spacing-md);
}

/* 引用块 */
blockquote {
  background: var(--surface-color);
  border-left: 4px solid var(--accent-color);
  padding: var(--spacing-lg);
  margin: var(--spacing-xl) 0;
  border-radius: 0 var(--radius-md) var(--radius-md) 0;
  position: relative;
}

blockquote::before {
  content: 'Research Note';
  position: absolute;
  top: var(--spacing-xs);
  right: var(--spacing-sm);
  font-size: 0.7rem;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

/* 表格样式 */
table {
  border-collapse: separate;
  border-spacing: 0;
  background: var(--background-color);
  border-radius: var(--radius-md);
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: var(--spacing-xl) 0;
}

th {
  background: var(--accent-color);
  color: white;
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-md);
  text-align: left;
}

td {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

tr:nth-child(even) {
  background: var(--surface-color);
}

/* 代码样式 */
code {
  background: var(--surface-color);
  color: var(--primary-color);
  padding: 0.2em 0.4em;
  border-radius: var(--radius-sm);
  font-family: 'SF Mono', monospace;
  font-size: 0.9em;
}

pre {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  overflow-x: auto;
  margin: var(--spacing-xl) 0;
}

/* 打印优化 */
@media print {
  body {
    color: #000000;
    background: #ffffff;
    max-width: none;
    margin: 0;
    padding: 1in;
  }
  
  h1 {
    border-bottom: 2pt solid #000000;
  }
  
  h1::after {
    background: #666666;
  }
  
  h2 {
    background: #f0f0f0;
    border-left: 3pt solid #666666;
  }
  
  h3 {
    border-bottom: 1pt solid #666666;
  }
  
  strong, b {
    background: none;
  }
  
  blockquote {
    background: #f8f8f8;
    border-left: 3pt solid #666666;
  }
  
  table {
    box-shadow: none;
  }
  
  th {
    background: #e0e0e0;
    color: #000000;
  }
  
  tr:nth-child(even) {
    background: #f5f5f5;
  }
}

