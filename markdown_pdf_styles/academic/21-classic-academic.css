/*
 * 学术系列 21 - 经典学术
 * Classic Academic Style
 * 
 * 特色：经典学术风格，严谨专业，适合学术论文、研究报告
 * 设计理念：传统学术排版的现代化演绎，强调可读性和逻辑性
 */

@import url('./common/base.css');
@import url('./common/variables.css');

:root {
  /* 主题色彩 */
  --primary-color: #1a1a1a;
  --secondary-color: #333333;
  --accent-color: #0066cc;
  --text-primary: #000000;
  --text-secondary: #333333;
  --background-color: #ffffff;
  --surface-color: #f8f9fa;
  --border-color: #dee2e6;
}

/* 页面整体样式 */
body {
  background-color: var(--background-color);
  color: var(--text-primary);
  font-weight: var(--font-weight-normal);
  letter-spacing: 0.02em;
  line-height: 1.8;
  max-width: 800px;
  margin: 0 auto;
  padding: var(--spacing-3xl) var(--spacing-2xl);
}

/* 标题样式系统 */
h1 {
  color: var(--primary-color);
  font-weight: var(--font-weight-bold);
  font-size: 2.5rem;
  text-align: center;
  margin-bottom: var(--spacing-3xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: 2px solid var(--accent-color);
  line-height: 1.2;
}

h2 {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  font-size: 1.8rem;
  margin-top: var(--spacing-3xl);
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-color);
  counter-increment: section;
}

h2::before {
  content: counter(section, decimal) ". ";
  color: var(--accent-color);
  font-weight: var(--font-weight-bold);
}

h3 {
  color: var(--secondary-color);
  font-weight: var(--font-weight-medium);
  font-size: 1.4rem;
  margin-top: var(--spacing-2xl);
  margin-bottom: var(--spacing-lg);
  counter-increment: subsection;
}

h3::before {
  content: counter(section, decimal) "." counter(subsection, decimal) " ";
  color: var(--accent-color);
  font-weight: var(--font-weight-medium);
}

h4 {
  color: var(--secondary-color);
  font-weight: var(--font-weight-medium);
  font-size: 1.2rem;
  margin-top: var(--spacing-xl);
  margin-bottom: var(--spacing-md);
  font-style: italic;
}

h5, h6 {
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
  font-size: 1rem;
  margin-top: var(--spacing-lg);
  margin-bottom: var(--spacing-sm);
}

/* 重置计数器 */
body {
  counter-reset: section;
}

h2 {
  counter-reset: subsection;
}

/* 段落优化 */
p {
  line-height: 1.8;
  margin-bottom: var(--spacing-lg);
  text-align: justify;
  text-indent: 2em;
}

p:first-of-type,
h1 + p,
h2 + p,
h3 + p,
h4 + p,
h5 + p,
h6 + p {
  text-indent: 0;
}

/* 强调文本 */
strong, b {
  color: var(--primary-color);
  font-weight: var(--font-weight-bold);
}

em, i {
  color: var(--text-secondary);
  font-style: italic;
}

/* 列表样式 */
ul {
  list-style: none;
  padding-left: 0;
  margin-bottom: var(--spacing-lg);
}

ul li {
  position: relative;
  padding-left: var(--spacing-lg);
  margin-bottom: var(--spacing-sm);
  line-height: 1.7;
}

ul li::before {
  content: '•';
  position: absolute;
  left: 0;
  color: var(--accent-color);
  font-weight: bold;
}

ol {
  padding-left: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

ol li {
  margin-bottom: var(--spacing-sm);
  line-height: 1.7;
}

/* 引用块样式 */
blockquote {
  background: var(--surface-color);
  border-left: 4px solid var(--accent-color);
  padding: var(--spacing-lg);
  margin: var(--spacing-xl) 0;
  font-style: italic;
  color: var(--text-secondary);
  position: relative;
}

blockquote p {
  margin-bottom: 0;
  text-indent: 0;
}

blockquote::before {
  content: '"';
  position: absolute;
  top: 0;
  left: var(--spacing-sm);
  font-size: 2rem;
  color: var(--accent-color);
  opacity: 0.5;
  font-family: Georgia, serif;
}

/* 代码样式 */
code {
  background: var(--surface-color);
  color: var(--primary-color);
  padding: 0.2em 0.4em;
  border-radius: var(--radius-sm);
  font-family: 'Courier New', 'Monaco', monospace;
  font-size: 0.9em;
  border: 1px solid var(--border-color);
}

pre {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  overflow-x: auto;
  margin: var(--spacing-xl) 0;
  line-height: 1.4;
}

pre code {
  background: transparent;
  border: none;
  padding: 0;
  color: var(--text-primary);
}

/* 表格样式 */
table {
  border-collapse: collapse;
  margin: var(--spacing-xl) 0;
  width: 100%;
  font-size: 0.9rem;
}

th {
  background: var(--surface-color);
  color: var(--primary-color);
  font-weight: var(--font-weight-bold);
  padding: var(--spacing-md);
  text-align: left;
  border: 1px solid var(--border-color);
}

td {
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  vertical-align: top;
}

tr:nth-child(even) {
  background: rgba(0, 102, 204, 0.02);
}

/* 链接样式 */
a {
  color: var(--accent-color);
  text-decoration: underline;
  transition: var(--transition-normal);
}

a:hover {
  color: var(--primary-color);
}

/* 分隔线 */
hr {
  border: none;
  height: 1px;
  background: var(--border-color);
  margin: var(--spacing-2xl) 0;
}

/* 图片样式 */
img {
  max-width: 100%;
  height: auto;
  margin: var(--spacing-lg) 0;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
}

/* 图片说明 */
img + em {
  display: block;
  text-align: center;
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin-top: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
}

/* 脚注样式 */
.footnote {
  font-size: 0.8rem;
  color: var(--text-secondary);
  border-top: 1px solid var(--border-color);
  padding-top: var(--spacing-md);
  margin-top: var(--spacing-2xl);
}

/* 打印优化 */
@media print {
  body {
    color: #000000;
    background: #ffffff;
    font-size: 12pt;
    line-height: 1.6;
    max-width: none;
    margin: 0;
    padding: 1in;
  }
  
  h1 {
    font-size: 18pt;
    border-bottom: 2pt solid #000000;
  }
  
  h2 {
    font-size: 16pt;
    border-bottom: 1pt solid #cccccc;
  }
  
  h3 {
    font-size: 14pt;
  }
  
  h4 {
    font-size: 12pt;
  }
  
  h5, h6 {
    font-size: 11pt;
  }
  
  blockquote {
    background: #f8f8f8;
    border-left: 3pt solid #666666;
  }
  
  blockquote::before {
    color: #cccccc;
  }
  
  code {
    background: #f0f0f0;
    border: 1pt solid #cccccc;
  }
  
  pre {
    background: #f8f8f8;
    border: 1pt solid #cccccc;
  }
  
  table {
    font-size: 10pt;
  }
  
  th {
    background: #f0f0f0;
    border: 1pt solid #000000;
  }
  
  td {
    border: 1pt solid #cccccc;
  }
  
  tr:nth-child(even) {
    background: #f8f8f8;
  }
  
  a {
    color: #000000;
    text-decoration: underline;
  }
  
  hr {
    background: #cccccc;
  }
  
  img {
    border: 1pt solid #cccccc;
  }
  
  /* 分页控制 */
  h1, h2, h3 {
    page-break-after: avoid;
  }
  
  p, blockquote {
    orphans: 3;
    widows: 3;
  }
  
  pre, table {
    page-break-inside: avoid;
  }
}

