/*
 * 创意系列 17 - 森林绿调
 * Forest Green Creative Style
 * 
 * 特色：森林绿色主题，自然创新，适合环保项目、可持续发展
 * 设计理念：森林绿的生机与创新思维的结合
 */

@import url('./common/base.css');
@import url('./common/variables.css');

:root {
  --primary-color: #2e7d32;
  --secondary-color: #388e3c;
  --accent-color: #4caf50;
  --forest-light: #66bb6a;
  --text-primary: #1b5e20;
  --text-secondary: #2e7d32;
  --background-color: #f8fff8;
  --surface-color: #e8f5e8;
  --border-color: #c8e6c9;
}

body {
  background-color: var(--background-color);
  color: var(--text-primary);
  font-weight: var(--font-weight-normal);
  letter-spacing: 0.01em;
  line-height: 1.7;
}

h1 {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  font-size: 3rem;
  margin-bottom: var(--spacing-2xl);
  text-align: center;
  position: relative;
  padding: var(--spacing-xl) 0;
}

h1::before {
  content: '🌲';
  position: absolute;
  top: 0;
  left: 20%;
  font-size: 1.8rem;
  opacity: 0.6;
}

h1::after {
  content: '🌲';
  position: absolute;
  top: 0;
  right: 20%;
  font-size: 1.5rem;
  opacity: 0.4;
}

h2 {
  color: var(--secondary-color);
  font-weight: var(--font-weight-medium);
  background: linear-gradient(135deg, var(--surface-color), rgba(76, 175, 80, 0.05));
  padding: var(--spacing-lg) var(--spacing-xl);
  border-radius: var(--radius-xl);
  margin: var(--spacing-2xl) 0 var(--spacing-xl) 0;
  position: relative;
  box-shadow: 0 4px 20px rgba(76, 175, 80, 0.15);
  border-left: 4px solid var(--accent-color);
}

h2::after {
  content: '🌱';
  position: absolute;
  right: var(--spacing-lg);
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.2rem;
  opacity: 0.7;
}

ul li::before {
  content: '🍀';
  position: absolute;
  left: 0;
  font-size: 1rem;
}

ol li::before {
  content: counter(item);
  background: linear-gradient(135deg, var(--accent-color), var(--forest-light));
  color: white;
  width: 26px;
  height: 26px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-medium);
  box-shadow: 0 3px 12px rgba(76, 175, 80, 0.3);
}

blockquote::before {
  content: '🌿';
  position: absolute;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  font-size: 1.3rem;
  opacity: 0.6;
}

@media print {
  body { color: #000000; background: #ffffff; }
  h1, h2, h3 { color: #000000; }
  h1::before, h1::after, h2::after, blockquote::before { content: ''; }
}

