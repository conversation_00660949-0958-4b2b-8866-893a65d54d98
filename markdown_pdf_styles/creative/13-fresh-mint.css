/*
 * 创意系列 13 - 青春薄荷
 * Fresh Mint Creative Style
 * 
 * 特色：清新薄荷绿主题，年轻活力，适合创新项目、青年创业
 * 设计理念：薄荷绿的清新与现代设计的活力结合
 */

@import url('./common/base.css');
@import url('./common/variables.css');

:root {
  /* 主题色彩 */
  --primary-color: #00695c;
  --secondary-color: #26a69a;
  --accent-color: #4db6ac;
  --mint-light: #80cbc4;
  --text-primary: #004d40;
  --text-secondary: #00695c;
  --background-color: #f8ffff;
  --surface-color: #e0f2f1;
  --border-color: #b2dfdb;
}

/* 页面整体样式 */
body {
  background-color: var(--background-color);
  color: var(--text-primary);
  font-weight: var(--font-weight-normal);
  letter-spacing: 0.01em;
  line-height: 1.68;
}

/* 标题样式系统 */
h1 {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  font-size: 3.2rem;
  margin-bottom: var(--spacing-2xl);
  position: relative;
  padding: var(--spacing-lg) 0;
  text-align: center;
}

h1::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 120px;
  height: 4px;
  background: linear-gradient(90deg, var(--accent-color), var(--mint-light));
  border-radius: var(--radius-full);
  animation: wave 2s ease-in-out infinite;
}

@keyframes wave {
  0%, 100% { transform: translateX(-50%) scaleX(1); }
  50% { transform: translateX(-50%) scaleX(1.1); }
}

h1::after {
  content: '🌱';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  font-size: 1.5rem;
  opacity: 0.7;
}

h2 {
  color: var(--secondary-color);
  font-weight: var(--font-weight-medium);
  background: linear-gradient(135deg, var(--surface-color), rgba(77, 182, 172, 0.05));
  padding: var(--spacing-lg) var(--spacing-xl);
  border-radius: var(--radius-xl);
  margin: var(--spacing-2xl) 0 var(--spacing-xl) 0;
  position: relative;
  box-shadow: 0 4px 20px rgba(77, 182, 172, 0.15);
  border-left: 4px solid var(--accent-color);
}

h2::before {
  content: '▲';
  position: absolute;
  left: var(--spacing-lg);
  top: 50%;
  transform: translateY(-50%);
  color: var(--accent-color);
  font-size: 0.8em;
}

h2::after {
  content: '';
  position: absolute;
  right: var(--spacing-lg);
  top: 50%;
  transform: translateY(-50%);
  width: 12px;
  height: 12px;
  background: var(--mint-light);
  border-radius: 50%;
  box-shadow: -20px 0 var(--accent-color), -40px 0 var(--mint-light);
}

h3 {
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
  position: relative;
  padding-left: var(--spacing-lg);
  margin-top: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
}

h3::before {
  content: '◉';
  position: absolute;
  left: 0;
  color: var(--accent-color);
  font-size: 0.9em;
}

h4 {
  color: var(--accent-color);
  font-weight: var(--font-weight-medium);
  position: relative;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-xs);
}

h4::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 2px;
  background: linear-gradient(90deg, var(--accent-color), var(--mint-light));
  border-radius: var(--radius-full);
}

h5, h6 {
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

/* 段落优化 */
p {
  line-height: 1.75;
  margin-bottom: var(--spacing-lg);
}

/* 强调文本 */
strong, b {
  color: var(--primary-color);
  font-weight: var(--font-weight-bold);
  background: linear-gradient(135deg, rgba(77, 182, 172, 0.1), rgba(128, 203, 196, 0.1));
  padding: 0.1em 0.3em;
  border-radius: var(--radius-sm);
}

em, i {
  color: var(--accent-color);
  font-style: italic;
}

/* 列表样式 */
ul {
  list-style: none;
  padding-left: 0;
}

ul li {
  position: relative;
  padding-left: var(--spacing-xl);
  margin-bottom: var(--spacing-md);
  line-height: 1.7;
}

ul li::before {
  content: '🍃';
  position: absolute;
  left: 0;
  font-size: 1rem;
}

ol {
  counter-reset: item;
  padding-left: 0;
}

ol li {
  position: relative;
  padding-left: var(--spacing-2xl);
  margin-bottom: var(--spacing-md);
  counter-increment: item;
  line-height: 1.7;
}

ol li::before {
  content: counter(item);
  position: absolute;
  left: 0;
  top: 0;
  background: linear-gradient(135deg, var(--accent-color), var(--mint-light));
  color: white;
  width: 24px;
  height: 24px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-medium);
  box-shadow: 0 2px 8px rgba(77, 182, 172, 0.3);
}

/* 引用块样式 */
blockquote {
  background: linear-gradient(135deg, var(--surface-color), rgba(77, 182, 172, 0.03));
  border: none;
  border-left: 4px solid var(--accent-color);
  border-radius: 0 var(--radius-xl) var(--radius-xl) 0;
  padding: var(--spacing-xl);
  margin: var(--spacing-2xl) 0;
  position: relative;
  box-shadow: 0 4px 20px rgba(77, 182, 172, 0.1);
}

blockquote::before {
  content: '💡';
  position: absolute;
  top: var(--spacing-md);
  right: var(--spacing-md);
  font-size: 1.3rem;
  opacity: 0.6;
}

blockquote::after {
  content: '';
  position: absolute;
  top: -2px;
  right: -2px;
  width: 16px;
  height: 16px;
  background: var(--mint-light);
  border-radius: 50%;
  opacity: 0.4;
}

blockquote p {
  margin-bottom: 0;
  font-style: italic;
  color: var(--text-secondary);
  line-height: 1.6;
}

/* 代码样式 */
code {
  background: rgba(77, 182, 172, 0.08);
  color: var(--primary-color);
  padding: 0.2em 0.5em;
  border-radius: var(--radius-md);
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.9em;
  border: 1px solid rgba(77, 182, 172, 0.2);
}

pre {
  background: linear-gradient(135deg, var(--surface-color), rgba(77, 182, 172, 0.02));
  border: 1px solid rgba(77, 182, 172, 0.2);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  overflow-x: auto;
  margin: var(--spacing-2xl) 0;
  box-shadow: 0 4px 20px rgba(77, 182, 172, 0.1);
  position: relative;
}

pre::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--accent-color), var(--mint-light));
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

pre code {
  background: transparent;
  border: none;
  padding: 0;
  color: var(--text-primary);
}

/* 表格样式 */
table {
  border-collapse: separate;
  border-spacing: 0;
  background: var(--background-color);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: 0 6px 25px rgba(77, 182, 172, 0.15);
  margin: var(--spacing-2xl) 0;
}

th {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-lg);
  text-align: left;
  border: none;
}

td {
  padding: var(--spacing-lg);
  border-bottom: 1px solid rgba(77, 182, 172, 0.1);
  border-right: 1px solid rgba(77, 182, 172, 0.1);
}

td:last-child {
  border-right: none;
}

tr:last-child td {
  border-bottom: none;
}

tr:nth-child(even) {
  background: rgba(77, 182, 172, 0.03);
}

tr:hover {
  background: rgba(128, 203, 196, 0.08);
}

/* 链接样式 */
a {
  color: var(--accent-color);
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: var(--transition-normal);
  position: relative;
}

a::before {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 0;
  height: 1px;
  background: linear-gradient(90deg, var(--accent-color), var(--mint-light));
  transition: width 0.3s ease;
}

a:hover {
  color: var(--primary-color);
}

a:hover::before {
  width: 100%;
}

/* 分隔线 */
hr {
  border: none;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--accent-color), var(--mint-light), transparent);
  margin: var(--spacing-3xl) 0;
  border-radius: var(--radius-full);
  position: relative;
}

hr::before {
  content: '🌿';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  background: var(--background-color);
  padding: 0 var(--spacing-sm);
  font-size: 1.1em;
}

/* 图片样式 */
img {
  border-radius: var(--radius-xl);
  box-shadow: 0 6px 25px rgba(77, 182, 172, 0.2);
  margin: var(--spacing-xl) 0;
  border: 3px solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(45deg, var(--accent-color), var(--mint-light)) border-box;
}

/* 特殊效果 */
.highlight {
  background: linear-gradient(135deg, rgba(77, 182, 172, 0.1), rgba(128, 203, 196, 0.1));
  padding: var(--spacing-md);
  border-radius: var(--radius-lg);
  border-left: 3px solid var(--accent-color);
  margin: var(--spacing-lg) 0;
}

/* 打印优化 */
@media print {
  body {
    color: #000000;
    background: #ffffff;
  }
  
  h1 {
    color: #000000;
  }
  
  h1::before {
    background: linear-gradient(90deg, #666666, #999999);
    animation: none;
  }
  
  h1::after {
    content: '';
  }
  
  h2 {
    color: #000000;
    background: #f5f5f5;
    border-left: 3pt solid #666666;
    box-shadow: none;
  }
  
  h2::before {
    color: #666666;
  }
  
  h2::after {
    background: #999999;
    box-shadow: -20px 0 #666666, -40px 0 #999999;
  }
  
  h3::before {
    color: #666666;
  }
  
  h4 {
    color: #333333;
  }
  
  h4::after {
    background: linear-gradient(90deg, #666666, #999999);
  }
  
  strong, b {
    background: #f0f0f0;
    color: #000000;
  }
  
  ul li::before {
    content: '•';
    color: #666666;
  }
  
  ol li::before {
    background: linear-gradient(135deg, #666666, #999999);
    box-shadow: none;
  }
  
  blockquote {
    background: #f8f8f8;
    border-left: 3pt solid #666666;
    box-shadow: none;
  }
  
  blockquote::before {
    content: '';
  }
  
  blockquote::after {
    display: none;
  }
  
  code {
    background: #f0f0f0;
    color: #000000;
    border: 1pt solid #cccccc;
  }
  
  pre {
    background: #f8f8f8;
    border: 1pt solid #cccccc;
    box-shadow: none;
  }
  
  pre::before {
    background: linear-gradient(90deg, #666666, #999999);
  }
  
  table {
    box-shadow: none;
  }
  
  th {
    background: #e0e0e0;
    color: #000000;
  }
  
  tr:nth-child(even) {
    background: #f5f5f5;
  }
  
  tr:hover {
    background: transparent;
  }
  
  a {
    color: #000000;
    border-bottom: 1pt solid #666666;
  }
  
  a::before {
    display: none;
  }
  
  hr {
    background: linear-gradient(90deg, transparent, #666666, #999999, transparent);
  }
  
  hr::before {
    content: '';
    background: #ffffff;
  }
  
  img {
    box-shadow: none;
    border: 2pt solid #cccccc;
    background: none;
  }
  
  .highlight {
    background: #f0f0f0;
    border-left: 2pt solid #666666;
  }
}

