/*
 * 创意系列 18 - 深紫创意
 * Deep Purple Creative Style
 * 
 * 特色：深紫色主题，神秘创意，适合艺术设计、创意工作室
 * 设计理念：深紫色的神秘感与创意灵感的碰撞
 */

@import url('./common/base.css');
@import url('./common/variables.css');

:root {
  --primary-color: #512da8;
  --secondary-color: #673ab7;
  --accent-color: #7c4dff;
  --purple-light: #9575cd;
  --text-primary: #311b92;
  --text-secondary: #512da8;
  --background-color: #faf8ff;
  --surface-color: #ede7f6;
  --border-color: #d1c4e9;
}

body {
  background-color: var(--background-color);
  color: var(--text-primary);
  font-weight: var(--font-weight-normal);
  letter-spacing: 0.015em;
  line-height: 1.7;
}

h1 {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  font-size: 3rem;
  margin-bottom: var(--spacing-2xl);
  text-align: center;
  position: relative;
  padding: var(--spacing-xl) 0;
}

h1::before {
  content: '🔮';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  font-size: 1.5rem;
  opacity: 0.7;
}

h2::after {
  content: '✨';
  position: absolute;
  right: var(--spacing-lg);
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.2rem;
  opacity: 0.7;
}

ul li::before {
  content: '🌟';
  position: absolute;
  left: 0;
  font-size: 1rem;
}

blockquote::before {
  content: '💜';
  position: absolute;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  font-size: 1.3rem;
  opacity: 0.6;
}

@media print {
  body { color: #000000; background: #ffffff; }
  h1, h2, h3 { color: #000000; }
  h1::before, h2::after, blockquote::before { content: ''; }
}

