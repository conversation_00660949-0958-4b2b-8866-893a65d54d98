/*
 * 创意系列 12 - 梦幻紫粉
 * Dreamy Purple Pink Creative Style
 * 
 * 特色：梦幻紫粉色主题，柔美创意，适合设计提案、品牌策划
 * 设计理念：紫粉色的浪漫与现代创意的融合，激发想象力
 */

@import url('./common/base.css');
@import url('./common/variables.css');

:root {
  /* 主题色彩 */
  --primary-color: #8e24aa;
  --secondary-color: #ab47bc;
  --accent-color: #e91e63;
  --soft-pink: #f8bbd9;
  --text-primary: #4a148c;
  --text-secondary: #7b1fa2;
  --background-color: #fefaff;
  --surface-color: #f3e5f5;
  --border-color: #e1bee7;
}

/* 页面整体样式 */
body {
  background-color: var(--background-color);
  color: var(--text-primary);
  font-weight: var(--font-weight-normal);
  letter-spacing: 0.015em;
  line-height: 1.7;
}

/* 标题样式系统 */
h1 {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  font-size: 3rem;
  margin-bottom: var(--spacing-2xl);
  position: relative;
  text-align: center;
  padding: var(--spacing-xl) 0;
}

h1::before {
  content: '✨';
  position: absolute;
  top: 0;
  left: 20%;
  font-size: 1.5rem;
  opacity: 0.6;
  animation: sparkle 3s infinite;
}

h1::after {
  content: '✨';
  position: absolute;
  top: 0;
  right: 20%;
  font-size: 1.2rem;
  opacity: 0.4;
  animation: sparkle 3s infinite 1.5s;
}

@keyframes sparkle {
  0%, 100% { opacity: 0.2; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.2); }
}

h2 {
  color: var(--secondary-color);
  font-weight: var(--font-weight-medium);
  background: linear-gradient(135deg, var(--surface-color), rgba(233, 30, 99, 0.05));
  padding: var(--spacing-lg) var(--spacing-xl);
  border-radius: var(--radius-2xl);
  margin: var(--spacing-2xl) 0 var(--spacing-xl) 0;
  position: relative;
  box-shadow: 0 6px 25px rgba(171, 71, 188, 0.15);
  border: 2px solid transparent;
  background-clip: padding-box;
}

h2::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: 2px;
  background: linear-gradient(45deg, var(--accent-color), var(--soft-pink));
  border-radius: var(--radius-2xl);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  z-index: -1;
}

h2::after {
  content: '💫';
  position: absolute;
  right: var(--spacing-lg);
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.3rem;
  opacity: 0.7;
}

h3 {
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
  position: relative;
  padding-left: var(--spacing-lg);
  margin-top: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
}

h3::before {
  content: '◆';
  position: absolute;
  left: 0;
  color: var(--accent-color);
  font-size: 1em;
  transform: rotate(45deg);
}

h4 {
  color: var(--accent-color);
  font-weight: var(--font-weight-medium);
  font-style: italic;
  position: relative;
  margin-bottom: var(--spacing-md);
}

h4::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, var(--accent-color), var(--soft-pink), transparent);
  border-radius: var(--radius-full);
}

h5, h6 {
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

/* 段落优化 */
p {
  line-height: 1.8;
  margin-bottom: var(--spacing-lg);
}

/* 强调文本 */
strong, b {
  color: var(--primary-color);
  font-weight: var(--font-weight-bold);
  background: linear-gradient(135deg, rgba(233, 30, 99, 0.1), rgba(248, 187, 217, 0.2));
  padding: 0.1em 0.3em;
  border-radius: var(--radius-md);
}

em, i {
  color: var(--accent-color);
  font-style: italic;
  position: relative;
}

em::after, i::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--soft-pink), transparent);
}

/* 列表样式 */
ul {
  list-style: none;
  padding-left: 0;
}

ul li {
  position: relative;
  padding-left: var(--spacing-xl);
  margin-bottom: var(--spacing-md);
  line-height: 1.7;
}

ul li::before {
  content: '🌸';
  position: absolute;
  left: 0;
  font-size: 1rem;
}

ol {
  counter-reset: item;
  padding-left: 0;
}

ol li {
  position: relative;
  padding-left: var(--spacing-2xl);
  margin-bottom: var(--spacing-md);
  counter-increment: item;
  line-height: 1.7;
}

ol li::before {
  content: counter(item);
  position: absolute;
  left: 0;
  top: 0;
  background: linear-gradient(135deg, var(--accent-color), var(--soft-pink));
  color: white;
  width: 26px;
  height: 26px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-medium);
  box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);
  border: 2px solid var(--soft-pink);
}

/* 引用块样式 */
blockquote {
  background: linear-gradient(135deg, var(--surface-color), rgba(233, 30, 99, 0.03));
  border: none;
  border-radius: var(--radius-2xl);
  padding: var(--spacing-xl) var(--spacing-2xl);
  margin: var(--spacing-2xl) 0;
  position: relative;
  box-shadow: 0 8px 30px rgba(171, 71, 188, 0.15);
}

blockquote::before {
  content: '';
  position: absolute;
  top: -10px;
  left: var(--spacing-lg);
  width: 20px;
  height: 20px;
  background: var(--accent-color);
  border-radius: 50%;
  box-shadow: 30px 0 var(--soft-pink), 60px 0 var(--accent-color);
}

blockquote::after {
  content: '💭';
  position: absolute;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  font-size: 1.5rem;
  opacity: 0.5;
}

blockquote p {
  margin-bottom: 0;
  font-style: italic;
  color: var(--text-secondary);
  line-height: 1.6;
}

/* 代码样式 */
code {
  background: rgba(233, 30, 99, 0.08);
  color: var(--primary-color);
  padding: 0.2em 0.5em;
  border-radius: var(--radius-lg);
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.9em;
  border: 1px solid rgba(233, 30, 99, 0.2);
}

pre {
  background: linear-gradient(135deg, var(--surface-color), rgba(233, 30, 99, 0.02));
  border: 1px solid rgba(233, 30, 99, 0.2);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-xl);
  overflow-x: auto;
  margin: var(--spacing-2xl) 0;
  box-shadow: 0 6px 25px rgba(171, 71, 188, 0.1);
  position: relative;
}

pre::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--accent-color), var(--soft-pink), var(--accent-color));
  border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
}

pre code {
  background: transparent;
  border: none;
  padding: 0;
  color: var(--text-primary);
}

/* 表格样式 */
table {
  border-collapse: separate;
  border-spacing: 0;
  background: var(--background-color);
  border-radius: var(--radius-2xl);
  overflow: hidden;
  box-shadow: 0 10px 35px rgba(171, 71, 188, 0.2);
  margin: var(--spacing-2xl) 0;
}

th {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-lg);
  text-align: left;
  border: none;
  position: relative;
}

th::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--accent-color), var(--soft-pink));
}

td {
  padding: var(--spacing-lg);
  border-bottom: 1px solid rgba(233, 30, 99, 0.1);
  border-right: 1px solid rgba(233, 30, 99, 0.1);
}

td:last-child {
  border-right: none;
}

tr:last-child td {
  border-bottom: none;
}

tr:nth-child(even) {
  background: rgba(233, 30, 99, 0.03);
}

tr:hover {
  background: rgba(248, 187, 217, 0.1);
}

/* 链接样式 */
a {
  color: var(--accent-color);
  text-decoration: none;
  border-bottom: 2px solid transparent;
  transition: var(--transition-normal);
  position: relative;
}

a::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--accent-color), var(--soft-pink));
  transition: width 0.3s ease;
}

a:hover::after {
  width: 100%;
}

/* 分隔线 */
hr {
  border: none;
  height: 3px;
  background: linear-gradient(90deg, transparent, var(--accent-color), var(--soft-pink), var(--accent-color), transparent);
  margin: var(--spacing-3xl) 0;
  border-radius: var(--radius-full);
  position: relative;
}

hr::before {
  content: '🌟';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  background: var(--background-color);
  padding: 0 var(--spacing-sm);
  font-size: 1.2em;
}

/* 图片样式 */
img {
  border-radius: var(--radius-2xl);
  box-shadow: 0 10px 35px rgba(171, 71, 188, 0.25);
  margin: var(--spacing-xl) 0;
  border: 4px solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(45deg, var(--accent-color), var(--soft-pink)) border-box;
}

/* 打印优化 */
@media print {
  body {
    color: #000000;
    background: #ffffff;
  }
  
  h1 {
    color: #000000;
  }
  
  h1::before,
  h1::after {
    content: '';
    animation: none;
  }
  
  h2 {
    color: #000000;
    background: #f5f5f5;
    box-shadow: none;
    border: 1pt solid #cccccc;
  }
  
  h2::before {
    display: none;
  }
  
  h2::after {
    content: '';
  }
  
  h3::before {
    color: #666666;
    transform: none;
  }
  
  h4 {
    color: #333333;
  }
  
  h4::after {
    background: linear-gradient(90deg, #666666, #999999, transparent);
  }
  
  strong, b {
    background: #f0f0f0;
    color: #000000;
  }
  
  em::after, i::after {
    background: linear-gradient(90deg, transparent, #cccccc, transparent);
  }
  
  ul li::before {
    content: '•';
    color: #666666;
  }
  
  ol li::before {
    background: linear-gradient(135deg, #666666, #999999);
    border-color: #cccccc;
    box-shadow: none;
  }
  
  blockquote {
    background: #f8f8f8;
    border-left: 3pt solid #666666;
    box-shadow: none;
  }
  
  blockquote::before {
    display: none;
  }
  
  blockquote::after {
    content: '';
  }
  
  code {
    background: #f0f0f0;
    color: #000000;
    border: 1pt solid #cccccc;
  }
  
  pre {
    background: #f8f8f8;
    border: 1pt solid #cccccc;
    box-shadow: none;
  }
  
  pre::before {
    background: linear-gradient(90deg, #666666, #999999, #666666);
  }
  
  table {
    box-shadow: none;
  }
  
  th {
    background: #e0e0e0;
    color: #000000;
  }
  
  th::after {
    background: linear-gradient(90deg, #666666, #999999);
  }
  
  tr:nth-child(even) {
    background: #f5f5f5;
  }
  
  tr:hover {
    background: transparent;
  }
  
  a {
    color: #000000;
    border-bottom: 1pt solid #666666;
  }
  
  a::after {
    display: none;
  }
  
  hr {
    background: linear-gradient(90deg, transparent, #666666, #999999, #666666, transparent);
  }
  
  hr::before {
    content: '';
    background: #ffffff;
  }
  
  img {
    box-shadow: none;
    border: 2pt solid #cccccc;
    background: none;
  }
}

