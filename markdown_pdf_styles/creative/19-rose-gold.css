/*
 * 创意系列 19 - 玫瑰金调
 * Rose Gold Creative Style
 * 
 * 特色：玫瑰金色主题，奢华创意，适合高端品牌、时尚设计
 * 设计理念：玫瑰金的奢华与现代创意的精致结合
 */

@import url('./common/base.css');
@import url('./common/variables.css');

:root {
  --primary-color: #ad1457;
  --secondary-color: #c2185b;
  --accent-color: #e91e63;
  --rose-gold: #f8bbd9;
  --text-primary: #880e4f;
  --text-secondary: #ad1457;
  --background-color: #fef7f0;
  --surface-color: #fce4ec;
  --border-color: #f8bbd9;
}

body {
  background-color: var(--background-color);
  color: var(--text-primary);
  font-weight: var(--font-weight-normal);
  letter-spacing: 0.02em;
  line-height: 1.7;
}

h1 {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  font-size: 3rem;
  margin-bottom: var(--spacing-2xl);
  text-align: center;
  position: relative;
  padding: var(--spacing-xl) 0;
}

h1::before {
  content: '💎';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  font-size: 1.5rem;
  opacity: 0.7;
}

h2::after {
  content: '👑';
  position: absolute;
  right: var(--spacing-lg);
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.2rem;
  opacity: 0.7;
}

ul li::before {
  content: '💖';
  position: absolute;
  left: 0;
  font-size: 1rem;
}

blockquote::before {
  content: '🌹';
  position: absolute;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  font-size: 1.3rem;
  opacity: 0.6;
}

@media print {
  body { color: #000000; background: #ffffff; }
  h1, h2, h3 { color: #000000; }
  h1::before, h2::after, blockquote::before { content: ''; }
}

