/*
 * 创意系列 16 - 珊瑚粉调
 * Coral Pink Creative Style
 * 
 * 特色：珊瑚粉色主题，温暖亲和，适合品牌设计、用户体验
 * 设计理念：珊瑚粉的温暖与现代设计的亲和力结合
 */

@import url('./common/base.css');
@import url('./common/variables.css');

:root {
  --primary-color: #ff5722;
  --secondary-color: #ff7043;
  --accent-color: #ff8a65;
  --coral-light: #ffab91;
  --text-primary: #d84315;
  --text-secondary: #ff5722;
  --background-color: #fffaf8;
  --surface-color: #fbe9e7;
  --border-color: #ffccbc;
}

body {
  background-color: var(--background-color);
  color: var(--text-primary);
  font-weight: var(--font-weight-normal);
  letter-spacing: 0.01em;
  line-height: 1.7;
}

h1 {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  font-size: 3rem;
  margin-bottom: var(--spacing-2xl);
  text-align: center;
  position: relative;
  padding: var(--spacing-xl) 0;
}

h1::before {
  content: '🌺';
  position: absolute;
  top: 0;
  left: 25%;
  font-size: 1.5rem;
  opacity: 0.6;
}

h1::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, var(--accent-color), var(--coral-light));
  border-radius: var(--radius-full);
}

h2 {
  color: var(--secondary-color);
  font-weight: var(--font-weight-medium);
  background: linear-gradient(135deg, var(--surface-color), rgba(255, 138, 101, 0.05));
  padding: var(--spacing-lg) var(--spacing-xl);
  border-radius: var(--radius-xl);
  margin: var(--spacing-2xl) 0 var(--spacing-xl) 0;
  position: relative;
  box-shadow: 0 4px 20px rgba(255, 87, 34, 0.15);
  border-left: 4px solid var(--accent-color);
}

h2::after {
  content: '🎨';
  position: absolute;
  right: var(--spacing-lg);
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.2rem;
  opacity: 0.7;
}

h3 {
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
  position: relative;
  padding-left: var(--spacing-lg);
  margin-top: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
}

h3::before {
  content: '●';
  position: absolute;
  left: 0;
  color: var(--accent-color);
  font-size: 1em;
}

ul li::before {
  content: '🌸';
  position: absolute;
  left: 0;
  font-size: 1rem;
}

ol li::before {
  content: counter(item);
  position: absolute;
  left: 0;
  top: 0;
  background: linear-gradient(135deg, var(--accent-color), var(--coral-light));
  color: white;
  width: 26px;
  height: 26px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-medium);
  box-shadow: 0 3px 12px rgba(255, 87, 34, 0.3);
}

blockquote {
  background: linear-gradient(135deg, var(--surface-color), rgba(255, 138, 101, 0.03));
  border-left: 4px solid var(--accent-color);
  border-radius: 0 var(--radius-xl) var(--radius-xl) 0;
  padding: var(--spacing-xl);
  margin: var(--spacing-2xl) 0;
  position: relative;
  box-shadow: 0 4px 20px rgba(255, 87, 34, 0.1);
}

blockquote::before {
  content: '💖';
  position: absolute;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  font-size: 1.3rem;
  opacity: 0.6;
}

/* 其他样式继承基础样式并应用主题色彩 */
@media print {
  body { color: #000000; background: #ffffff; }
  h1, h2, h3 { color: #000000; }
  h1::after, h2::after { background: linear-gradient(90deg, #666666, #999999); }
  blockquote { background: #f8f8f8; border-left: 3pt solid #666666; }
}

