/*
 * 创意系列 15 - 天空蓝调
 * Sky Blue Creative Style
 * 
 * 特色：天空蓝色主题，开阔视野，适合科技创新、未来规划
 * 设计理念：天空蓝的无限可能与现代科技感的结合
 */

@import url('./common/base.css');
@import url('./common/variables.css');

:root {
  /* 主题色彩 */
  --primary-color: #0277bd;
  --secondary-color: #0288d1;
  --accent-color: #03a9f4;
  --light-blue: #4fc3f7;
  --text-primary: #01579b;
  --text-secondary: #0277bd;
  --background-color: #f8fcff;
  --surface-color: #e1f5fe;
  --border-color: #b3e5fc;
}

/* 页面整体样式 */
body {
  background-color: var(--background-color);
  color: var(--text-primary);
  font-weight: var(--font-weight-normal);
  letter-spacing: 0.01em;
  line-height: 1.68;
}

/* 标题样式系统 */
h1 {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  font-size: 3.2rem;
  margin-bottom: var(--spacing-2xl);
  position: relative;
  text-align: center;
  padding: var(--spacing-xl) 0;
}

h1::before {
  content: '☁️';
  position: absolute;
  top: 0;
  left: 30%;
  font-size: 1.5rem;
  opacity: 0.6;
  animation: float 3s ease-in-out infinite;
}

h1::after {
  content: '☁️';
  position: absolute;
  top: 0;
  right: 30%;
  font-size: 1.2rem;
  opacity: 0.4;
  animation: float 3s ease-in-out infinite 1.5s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

h2 {
  color: var(--secondary-color);
  font-weight: var(--font-weight-medium);
  background: linear-gradient(135deg, var(--surface-color), rgba(3, 169, 244, 0.05));
  padding: var(--spacing-lg) var(--spacing-xl);
  border-radius: var(--radius-xl);
  margin: var(--spacing-2xl) 0 var(--spacing-xl) 0;
  position: relative;
  box-shadow: 0 4px 20px rgba(3, 169, 244, 0.15);
  border-left: 4px solid var(--accent-color);
}

h2::before {
  content: '▶';
  position: absolute;
  left: var(--spacing-lg);
  top: 50%;
  transform: translateY(-50%);
  color: var(--accent-color);
  font-size: 0.8em;
}

h2::after {
  content: '🚀';
  position: absolute;
  right: var(--spacing-lg);
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.2rem;
  opacity: 0.7;
}

h3 {
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
  position: relative;
  padding-left: var(--spacing-lg);
  margin-top: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
}

h3::before {
  content: '◆';
  position: absolute;
  left: 0;
  color: var(--accent-color);
  font-size: 0.9em;
}

h4 {
  color: var(--accent-color);
  font-weight: var(--font-weight-medium);
  position: relative;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-xs);
}

h4::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, var(--accent-color), var(--light-blue));
  border-radius: var(--radius-full);
}

h5, h6 {
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

/* 段落优化 */
p {
  line-height: 1.75;
  margin-bottom: var(--spacing-lg);
}

/* 强调文本 */
strong, b {
  color: var(--primary-color);
  font-weight: var(--font-weight-bold);
  background: linear-gradient(135deg, rgba(3, 169, 244, 0.1), rgba(79, 195, 247, 0.1));
  padding: 0.1em 0.3em;
  border-radius: var(--radius-sm);
}

em, i {
  color: var(--accent-color);
  font-style: italic;
}

/* 列表样式 */
ul {
  list-style: none;
  padding-left: 0;
}

ul li {
  position: relative;
  padding-left: var(--spacing-xl);
  margin-bottom: var(--spacing-md);
  line-height: 1.7;
}

ul li::before {
  content: '✈️';
  position: absolute;
  left: 0;
  font-size: 1rem;
}

ol {
  counter-reset: item;
  padding-left: 0;
}

ol li {
  position: relative;
  padding-left: var(--spacing-2xl);
  margin-bottom: var(--spacing-md);
  counter-increment: item;
  line-height: 1.7;
}

ol li::before {
  content: counter(item);
  position: absolute;
  left: 0;
  top: 0;
  background: linear-gradient(135deg, var(--accent-color), var(--light-blue));
  color: white;
  width: 26px;
  height: 26px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-medium);
  box-shadow: 0 3px 12px rgba(3, 169, 244, 0.3);
}

/* 引用块样式 */
blockquote {
  background: linear-gradient(135deg, var(--surface-color), rgba(3, 169, 244, 0.03));
  border: none;
  border-left: 4px solid var(--accent-color);
  border-radius: 0 var(--radius-xl) var(--radius-xl) 0;
  padding: var(--spacing-xl);
  margin: var(--spacing-2xl) 0;
  position: relative;
  box-shadow: 0 4px 20px rgba(3, 169, 244, 0.1);
}

blockquote::before {
  content: '💭';
  position: absolute;
  top: var(--spacing-md);
  right: var(--spacing-md);
  font-size: 1.3rem;
  opacity: 0.6;
}

blockquote::after {
  content: '';
  position: absolute;
  top: -3px;
  right: -3px;
  width: 18px;
  height: 18px;
  background: var(--light-blue);
  border-radius: 50%;
  opacity: 0.4;
}

blockquote p {
  margin-bottom: 0;
  font-style: italic;
  color: var(--text-secondary);
  line-height: 1.6;
}

/* 代码样式 */
code {
  background: rgba(3, 169, 244, 0.08);
  color: var(--primary-color);
  padding: 0.2em 0.5em;
  border-radius: var(--radius-md);
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.9em;
  border: 1px solid rgba(3, 169, 244, 0.2);
}

pre {
  background: linear-gradient(135deg, var(--surface-color), rgba(3, 169, 244, 0.02));
  border: 1px solid rgba(3, 169, 244, 0.2);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  overflow-x: auto;
  margin: var(--spacing-2xl) 0;
  box-shadow: 0 4px 20px rgba(3, 169, 244, 0.1);
  position: relative;
}

pre::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--accent-color), var(--light-blue));
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

pre code {
  background: transparent;
  border: none;
  padding: 0;
  color: var(--text-primary);
}

/* 表格样式 */
table {
  border-collapse: separate;
  border-spacing: 0;
  background: var(--background-color);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: 0 6px 25px rgba(3, 169, 244, 0.15);
  margin: var(--spacing-2xl) 0;
}

th {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-lg);
  text-align: left;
  border: none;
}

td {
  padding: var(--spacing-lg);
  border-bottom: 1px solid rgba(3, 169, 244, 0.1);
  border-right: 1px solid rgba(3, 169, 244, 0.1);
}

td:last-child {
  border-right: none;
}

tr:last-child td {
  border-bottom: none;
}

tr:nth-child(even) {
  background: rgba(3, 169, 244, 0.03);
}

tr:hover {
  background: rgba(79, 195, 247, 0.08);
}

/* 链接样式 */
a {
  color: var(--accent-color);
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: var(--transition-normal);
  position: relative;
}

a::before {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 0;
  height: 1px;
  background: linear-gradient(90deg, var(--accent-color), var(--light-blue));
  transition: width 0.3s ease;
}

a:hover {
  color: var(--primary-color);
}

a:hover::before {
  width: 100%;
}

/* 分隔线 */
hr {
  border: none;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--accent-color), var(--light-blue), transparent);
  margin: var(--spacing-3xl) 0;
  border-radius: var(--radius-full);
  position: relative;
}

hr::before {
  content: '⭐';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  background: var(--background-color);
  padding: 0 var(--spacing-sm);
  font-size: 1.1em;
}

/* 图片样式 */
img {
  border-radius: var(--radius-xl);
  box-shadow: 0 6px 25px rgba(3, 169, 244, 0.2);
  margin: var(--spacing-xl) 0;
  border: 3px solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(45deg, var(--accent-color), var(--light-blue)) border-box;
}

/* 特殊效果 */
.info-box {
  background: linear-gradient(135deg, rgba(3, 169, 244, 0.08), rgba(79, 195, 247, 0.05));
  border: 2px solid var(--accent-color);
  border-radius: var(--radius-xl);
  padding: var(--spacing-lg);
  margin: var(--spacing-lg) 0;
  position: relative;
}

.info-box::before {
  content: 'ℹ️';
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-sm);
  font-size: 1.2rem;
}

/* 打印优化 */
@media print {
  body {
    color: #000000;
    background: #ffffff;
  }
  
  h1 {
    color: #000000;
  }
  
  h1::before,
  h1::after {
    content: '';
    animation: none;
  }
  
  h2 {
    color: #000000;
    background: #f5f5f5;
    border-left: 3pt solid #666666;
    box-shadow: none;
  }
  
  h2::before {
    color: #666666;
  }
  
  h2::after {
    content: '';
  }
  
  h3::before {
    color: #666666;
  }
  
  h4 {
    color: #333333;
  }
  
  h4::after {
    background: linear-gradient(90deg, #666666, #999999);
  }
  
  strong, b {
    background: #f0f0f0;
    color: #000000;
  }
  
  ul li::before {
    content: '•';
    color: #666666;
  }
  
  ol li::before {
    background: linear-gradient(135deg, #666666, #999999);
    box-shadow: none;
  }
  
  blockquote {
    background: #f8f8f8;
    border-left: 3pt solid #666666;
    box-shadow: none;
  }
  
  blockquote::before {
    content: '';
  }
  
  blockquote::after {
    display: none;
  }
  
  code {
    background: #f0f0f0;
    color: #000000;
    border: 1pt solid #cccccc;
  }
  
  pre {
    background: #f8f8f8;
    border: 1pt solid #cccccc;
    box-shadow: none;
  }
  
  pre::before {
    background: linear-gradient(90deg, #666666, #999999);
  }
  
  table {
    box-shadow: none;
  }
  
  th {
    background: #e0e0e0;
    color: #000000;
  }
  
  tr:nth-child(even) {
    background: #f5f5f5;
  }
  
  tr:hover {
    background: transparent;
  }
  
  a {
    color: #000000;
    border-bottom: 1pt solid #666666;
  }
  
  a::before {
    display: none;
  }
  
  hr {
    background: linear-gradient(90deg, transparent, #666666, #999999, transparent);
  }
  
  hr::before {
    content: '';
    background: #ffffff;
  }
  
  img {
    box-shadow: none;
    border: 2pt solid #cccccc;
    background: none;
  }
  
  .info-box {
    background: #f0f0f0;
    border: 2pt solid #666666;
  }
  
  .info-box::before {
    content: '';
  }
}

