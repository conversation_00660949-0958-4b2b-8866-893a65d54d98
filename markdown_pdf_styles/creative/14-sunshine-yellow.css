/*
 * 创意系列 14 - 阳光黄调
 * Sunshine Yellow Creative Style
 * 
 * 特色：阳光黄色主题，积极向上，适合教育培训、活动策划
 * 设计理念：黄色的温暖与活力，传递正能量和创新精神
 */

@import url('./common/base.css');
@import url('./common/variables.css');

:root {
  /* 主题色彩 */
  --primary-color: #f57f17;
  --secondary-color: #ffb300;
  --accent-color: #ffc107;
  --warm-orange: #ff8f00;
  --text-primary: #e65100;
  --text-secondary: #f57c00;
  --background-color: #fffef5;
  --surface-color: #fff8e1;
  --border-color: #ffecb3;
}

/* 页面整体样式 */
body {
  background-color: var(--background-color);
  color: var(--text-primary);
  font-weight: var(--font-weight-normal);
  letter-spacing: 0.01em;
  line-height: 1.65;
}

/* 标题样式系统 */
h1 {
  color: var(--primary-color);
  font-weight: var(--font-weight-bold);
  font-size: 3.5rem;
  margin-bottom: var(--spacing-2xl);
  position: relative;
  text-align: center;
  padding: var(--spacing-xl) 0;
}

h1::before {
  content: '☀️';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  font-size: 2rem;
  animation: rotate 4s linear infinite;
}

@keyframes rotate {
  from { transform: translateX(-50%) rotate(0deg); }
  to { transform: translateX(-50%) rotate(360deg); }
}

h1::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 4px;
  background: linear-gradient(90deg, var(--accent-color), var(--warm-orange));
  border-radius: var(--radius-full);
  box-shadow: 0 0 10px rgba(255, 193, 7, 0.5);
}

h2 {
  color: var(--secondary-color);
  font-weight: var(--font-weight-medium);
  background: linear-gradient(135deg, var(--surface-color), rgba(255, 193, 7, 0.08));
  padding: var(--spacing-lg) var(--spacing-xl);
  border-radius: var(--radius-xl);
  margin: var(--spacing-2xl) 0 var(--spacing-xl) 0;
  position: relative;
  box-shadow: 0 4px 20px rgba(255, 193, 7, 0.2);
  border-top: 4px solid var(--accent-color);
}

h2::before {
  content: '⚡';
  position: absolute;
  left: var(--spacing-lg);
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.2rem;
}

h2::after {
  content: '';
  position: absolute;
  right: var(--spacing-lg);
  top: 50%;
  transform: translateY(-50%);
  width: 15px;
  height: 15px;
  background: var(--warm-orange);
  border-radius: 50%;
  box-shadow: -25px 0 var(--accent-color), -50px 0 var(--warm-orange);
}

h3 {
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
  position: relative;
  padding-left: var(--spacing-lg);
  margin-top: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
}

h3::before {
  content: '★';
  position: absolute;
  left: 0;
  color: var(--accent-color);
  font-size: 1em;
}

h4 {
  color: var(--accent-color);
  font-weight: var(--font-weight-medium);
  position: relative;
  margin-bottom: var(--spacing-md);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-size: 1.1rem;
}

h4::after {
  content: '';
  position: absolute;
  bottom: -3px;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, var(--accent-color), var(--warm-orange));
  border-radius: var(--radius-full);
}

h5, h6 {
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

/* 段落优化 */
p {
  line-height: 1.75;
  margin-bottom: var(--spacing-lg);
}

/* 强调文本 */
strong, b {
  color: var(--primary-color);
  font-weight: var(--font-weight-bold);
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.15), rgba(255, 143, 0, 0.1));
  padding: 0.1em 0.3em;
  border-radius: var(--radius-md);
  box-shadow: 0 1px 3px rgba(255, 193, 7, 0.2);
}

em, i {
  color: var(--warm-orange);
  font-style: italic;
}

/* 列表样式 */
ul {
  list-style: none;
  padding-left: 0;
}

ul li {
  position: relative;
  padding-left: var(--spacing-xl);
  margin-bottom: var(--spacing-md);
  line-height: 1.7;
}

ul li::before {
  content: '🌟';
  position: absolute;
  left: 0;
  font-size: 1rem;
}

ol {
  counter-reset: item;
  padding-left: 0;
}

ol li {
  position: relative;
  padding-left: var(--spacing-2xl);
  margin-bottom: var(--spacing-md);
  counter-increment: item;
  line-height: 1.7;
}

ol li::before {
  content: counter(item);
  position: absolute;
  left: 0;
  top: 0;
  background: linear-gradient(135deg, var(--accent-color), var(--warm-orange));
  color: white;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-bold);
  box-shadow: 0 3px 12px rgba(255, 193, 7, 0.4);
  border: 2px solid var(--warm-orange);
}

/* 引用块样式 */
blockquote {
  background: linear-gradient(135deg, var(--surface-color), rgba(255, 193, 7, 0.05));
  border: none;
  border-left: 5px solid var(--accent-color);
  border-radius: 0 var(--radius-xl) var(--radius-xl) 0;
  padding: var(--spacing-xl) var(--spacing-2xl);
  margin: var(--spacing-2xl) 0;
  position: relative;
  box-shadow: 0 6px 25px rgba(255, 193, 7, 0.15);
}

blockquote::before {
  content: '💡';
  position: absolute;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  font-size: 1.5rem;
  opacity: 0.7;
}

blockquote::after {
  content: '';
  position: absolute;
  top: -8px;
  right: -8px;
  width: 24px;
  height: 24px;
  background: radial-gradient(circle, var(--warm-orange), transparent);
  border-radius: 50%;
  opacity: 0.3;
}

blockquote p {
  margin-bottom: 0;
  font-style: italic;
  color: var(--text-secondary);
  line-height: 1.6;
}

/* 代码样式 */
code {
  background: rgba(255, 193, 7, 0.1);
  color: var(--primary-color);
  padding: 0.2em 0.5em;
  border-radius: var(--radius-lg);
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.9em;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

pre {
  background: linear-gradient(135deg, var(--surface-color), rgba(255, 193, 7, 0.03));
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  overflow-x: auto;
  margin: var(--spacing-2xl) 0;
  box-shadow: 0 4px 20px rgba(255, 193, 7, 0.15);
  position: relative;
}

pre::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--accent-color), var(--warm-orange), var(--accent-color));
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

pre code {
  background: transparent;
  border: none;
  padding: 0;
  color: var(--text-primary);
}

/* 表格样式 */
table {
  border-collapse: separate;
  border-spacing: 0;
  background: var(--background-color);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: 0 8px 30px rgba(255, 193, 7, 0.2);
  margin: var(--spacing-2xl) 0;
}

th {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-lg);
  text-align: left;
  border: none;
  position: relative;
}

th::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--warm-orange);
}

td {
  padding: var(--spacing-lg);
  border-bottom: 1px solid rgba(255, 193, 7, 0.15);
  border-right: 1px solid rgba(255, 193, 7, 0.15);
}

td:last-child {
  border-right: none;
}

tr:last-child td {
  border-bottom: none;
}

tr:nth-child(even) {
  background: rgba(255, 193, 7, 0.05);
}

tr:hover {
  background: rgba(255, 143, 0, 0.1);
}

/* 链接样式 */
a {
  color: var(--accent-color);
  text-decoration: none;
  border-bottom: 2px solid transparent;
  transition: var(--transition-normal);
  font-weight: var(--font-weight-medium);
}

a:hover {
  color: var(--warm-orange);
  border-bottom-color: var(--warm-orange);
  text-shadow: 0 0 5px rgba(255, 143, 0, 0.3);
}

/* 分隔线 */
hr {
  border: none;
  height: 3px;
  background: linear-gradient(90deg, transparent, var(--accent-color), var(--warm-orange), var(--accent-color), transparent);
  margin: var(--spacing-3xl) 0;
  border-radius: var(--radius-full);
  position: relative;
  box-shadow: 0 0 8px rgba(255, 193, 7, 0.3);
}

hr::before {
  content: '✨';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  background: var(--background-color);
  padding: 0 var(--spacing-sm);
  font-size: 1.2em;
}

/* 图片样式 */
img {
  border-radius: var(--radius-xl);
  box-shadow: 0 8px 30px rgba(255, 193, 7, 0.25);
  margin: var(--spacing-xl) 0;
  border: 4px solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(45deg, var(--accent-color), var(--warm-orange)) border-box;
}

/* 特殊效果 */
.callout {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 143, 0, 0.05));
  border: 2px solid var(--accent-color);
  border-radius: var(--radius-xl);
  padding: var(--spacing-lg);
  margin: var(--spacing-lg) 0;
  position: relative;
}

.callout::before {
  content: '⚠️';
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-sm);
  font-size: 1.2rem;
}

/* 打印优化 */
@media print {
  body {
    color: #000000;
    background: #ffffff;
  }
  
  h1 {
    color: #000000;
  }
  
  h1::before {
    content: '';
    animation: none;
  }
  
  h1::after {
    background: linear-gradient(90deg, #666666, #999999);
    box-shadow: none;
  }
  
  h2 {
    color: #000000;
    background: #f5f5f5;
    border-top: 3pt solid #666666;
    box-shadow: none;
  }
  
  h2::before {
    content: '';
  }
  
  h2::after {
    background: #999999;
    box-shadow: -25px 0 #666666, -50px 0 #999999;
  }
  
  h3::before {
    color: #666666;
  }
  
  h4 {
    color: #333333;
  }
  
  h4::after {
    background: linear-gradient(90deg, #666666, #999999);
  }
  
  strong, b {
    background: #f0f0f0;
    color: #000000;
    box-shadow: none;
  }
  
  ul li::before {
    content: '•';
    color: #666666;
  }
  
  ol li::before {
    background: linear-gradient(135deg, #666666, #999999);
    border-color: #999999;
    box-shadow: none;
  }
  
  blockquote {
    background: #f8f8f8;
    border-left: 3pt solid #666666;
    box-shadow: none;
  }
  
  blockquote::before {
    content: '';
  }
  
  blockquote::after {
    display: none;
  }
  
  code {
    background: #f0f0f0;
    color: #000000;
    border: 1pt solid #cccccc;
  }
  
  pre {
    background: #f8f8f8;
    border: 1pt solid #cccccc;
    box-shadow: none;
  }
  
  pre::before {
    background: linear-gradient(90deg, #666666, #999999, #666666);
  }
  
  table {
    box-shadow: none;
  }
  
  th {
    background: #e0e0e0;
    color: #000000;
  }
  
  th::after {
    background: #999999;
  }
  
  tr:nth-child(even) {
    background: #f5f5f5;
  }
  
  tr:hover {
    background: transparent;
  }
  
  a {
    color: #000000;
    border-bottom: 1pt solid #666666;
    text-shadow: none;
  }
  
  hr {
    background: linear-gradient(90deg, transparent, #666666, #999999, #666666, transparent);
    box-shadow: none;
  }
  
  hr::before {
    content: '';
    background: #ffffff;
  }
  
  img {
    box-shadow: none;
    border: 2pt solid #cccccc;
    background: none;
  }
  
  .callout {
    background: #f0f0f0;
    border: 2pt solid #666666;
  }
  
  .callout::before {
    content: '';
  }
}

