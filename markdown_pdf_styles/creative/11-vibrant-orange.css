/*
 * 创意系列 11 - 活力橙调
 * Vibrant Orange Creative Style
 * 
 * 特色：活力橙色主题，充满能量，适合创意提案、营销策划
 * 设计理念：橙色的活力与现代设计的结合，激发创新思维
 */

@import url('./common/base.css');
@import url('./common/variables.css');

:root {
  /* 主题色彩 */
  --primary-color: #e65100;
  --secondary-color: #ff9800;
  --accent-color: #ff5722;
  --warm-yellow: #ffc107;
  --text-primary: #bf360c;
  --text-secondary: #d84315;
  --background-color: #fffef7;
  --surface-color: #fff3e0;
  --border-color: #ffcc80;
}

/* 页面整体样式 */
body {
  background-color: var(--background-color);
  color: var(--text-primary);
  font-weight: var(--font-weight-normal);
  letter-spacing: 0.01em;
  line-height: 1.65;
}

/* 标题样式系统 */
h1 {
  color: var(--primary-color);
  font-weight: var(--font-weight-bold);
  font-size: 3.2rem;
  margin-bottom: var(--spacing-2xl);
  position: relative;
  text-align: center;
  padding: var(--spacing-xl) 0;
  background: linear-gradient(135deg, transparent, rgba(255, 152, 0, 0.05), transparent);
  border-radius: var(--radius-xl);
}

h1::before {
  content: '✦';
  position: absolute;
  top: var(--spacing-sm);
  left: 50%;
  transform: translateX(-50%);
  color: var(--warm-yellow);
  font-size: 1.5rem;
}

h1::after {
  content: '';
  position: absolute;
  bottom: var(--spacing-sm);
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, var(--accent-color), var(--warm-yellow));
  border-radius: var(--radius-full);
}

h2 {
  color: var(--secondary-color);
  font-weight: var(--font-weight-medium);
  background: linear-gradient(135deg, var(--surface-color), rgba(255, 87, 34, 0.05));
  padding: var(--spacing-lg) var(--spacing-xl);
  border-radius: var(--radius-xl);
  margin: var(--spacing-2xl) 0 var(--spacing-xl) 0;
  position: relative;
  box-shadow: 0 4px 20px rgba(255, 152, 0, 0.15);
  border-left: 5px solid var(--accent-color);
}

h2::before {
  content: '▶';
  position: absolute;
  left: var(--spacing-lg);
  top: 50%;
  transform: translateY(-50%);
  color: var(--accent-color);
  font-size: 0.8em;
}

h2::after {
  content: '✨';
  position: absolute;
  right: var(--spacing-lg);
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.2rem;
  opacity: 0.6;
}

h3 {
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
  position: relative;
  padding-left: var(--spacing-lg);
  margin-top: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
}

h3::before {
  content: '●';
  position: absolute;
  left: 0;
  color: var(--accent-color);
  font-size: 1.2em;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

h4 {
  color: var(--accent-color);
  font-weight: var(--font-weight-medium);
  background: linear-gradient(90deg, var(--accent-color), var(--warm-yellow));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 1.2rem;
  margin-bottom: var(--spacing-md);
}

h5, h6 {
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

/* 段落优化 */
p {
  line-height: 1.75;
  margin-bottom: var(--spacing-lg);
}

/* 强调文本 */
strong, b {
  color: var(--primary-color);
  font-weight: var(--font-weight-bold);
  background: linear-gradient(135deg, transparent, rgba(255, 193, 7, 0.2), transparent);
  padding: 0 0.2em;
  border-radius: var(--radius-sm);
}

em, i {
  color: var(--accent-color);
  font-style: italic;
}

/* 列表样式 */
ul {
  list-style: none;
  padding-left: 0;
}

ul li {
  position: relative;
  padding-left: var(--spacing-xl);
  margin-bottom: var(--spacing-md);
  line-height: 1.7;
}

ul li::before {
  content: '🔥';
  position: absolute;
  left: 0;
  font-size: 1rem;
}

ol {
  counter-reset: item;
  padding-left: 0;
}

ol li {
  position: relative;
  padding-left: var(--spacing-2xl);
  margin-bottom: var(--spacing-md);
  counter-increment: item;
  line-height: 1.7;
}

ol li::before {
  content: counter(item);
  position: absolute;
  left: 0;
  top: 0;
  background: linear-gradient(135deg, var(--accent-color), var(--warm-yellow));
  color: white;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-bold);
  box-shadow: 0 3px 12px rgba(255, 87, 34, 0.4);
}

/* 引用块样式 */
blockquote {
  background: linear-gradient(135deg, var(--surface-color), rgba(255, 87, 34, 0.05));
  border: none;
  border-left: 5px solid var(--accent-color);
  border-radius: 0 var(--radius-xl) var(--radius-xl) 0;
  padding: var(--spacing-xl) var(--spacing-2xl);
  margin: var(--spacing-2xl) 0;
  position: relative;
  box-shadow: 0 6px 25px rgba(255, 152, 0, 0.15);
}

blockquote::before {
  content: '💡';
  position: absolute;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  font-size: 1.5rem;
}

blockquote::after {
  content: '';
  position: absolute;
  top: -5px;
  right: -5px;
  width: 20px;
  height: 20px;
  background: var(--warm-yellow);
  border-radius: 50%;
  opacity: 0.3;
}

blockquote p {
  margin-bottom: 0;
  font-style: italic;
  color: var(--text-secondary);
  line-height: 1.6;
}

/* 代码样式 */
code {
  background: rgba(255, 87, 34, 0.1);
  color: var(--primary-color);
  padding: 0.2em 0.5em;
  border-radius: var(--radius-lg);
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.9em;
  border: 1px solid rgba(255, 87, 34, 0.2);
}

pre {
  background: linear-gradient(135deg, var(--surface-color), rgba(255, 87, 34, 0.03));
  border: 1px solid rgba(255, 87, 34, 0.2);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  overflow-x: auto;
  margin: var(--spacing-2xl) 0;
  box-shadow: 0 4px 20px rgba(255, 152, 0, 0.1);
  position: relative;
}

pre::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--accent-color), var(--warm-yellow), var(--accent-color));
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

pre code {
  background: transparent;
  border: none;
  padding: 0;
  color: var(--text-primary);
}

/* 表格样式 */
table {
  border-collapse: separate;
  border-spacing: 0;
  background: var(--background-color);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: 0 8px 30px rgba(255, 152, 0, 0.2);
  margin: var(--spacing-2xl) 0;
}

th {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-lg);
  text-align: left;
  border: none;
}

td {
  padding: var(--spacing-lg);
  border-bottom: 1px solid rgba(255, 87, 34, 0.1);
  border-right: 1px solid rgba(255, 87, 34, 0.1);
}

td:last-child {
  border-right: none;
}

tr:last-child td {
  border-bottom: none;
}

tr:nth-child(even) {
  background: rgba(255, 87, 34, 0.03);
}

tr:hover {
  background: rgba(255, 193, 7, 0.1);
}

/* 链接样式 */
a {
  color: var(--accent-color);
  text-decoration: none;
  border-bottom: 2px solid transparent;
  transition: var(--transition-normal);
  background: linear-gradient(90deg, var(--accent-color), var(--warm-yellow));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

a:hover {
  border-bottom-color: var(--warm-yellow);
  transform: translateY(-1px);
}

/* 分隔线 */
hr {
  border: none;
  height: 3px;
  background: linear-gradient(90deg, transparent, var(--accent-color), var(--warm-yellow), var(--accent-color), transparent);
  margin: var(--spacing-3xl) 0;
  border-radius: var(--radius-full);
  position: relative;
}

hr::before {
  content: '⚡';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  background: var(--background-color);
  padding: 0 var(--spacing-sm);
  font-size: 1.2em;
}

/* 图片样式 */
img {
  border-radius: var(--radius-xl);
  box-shadow: 0 8px 30px rgba(255, 152, 0, 0.25);
  margin: var(--spacing-xl) 0;
  border: 3px solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(45deg, var(--accent-color), var(--warm-yellow)) border-box;
}

/* 打印优化 */
@media print {
  body {
    color: #000000;
    background: #ffffff;
  }
  
  h1 {
    color: #000000;
    background: none;
  }
  
  h1::before {
    content: '';
  }
  
  h1::after {
    background: linear-gradient(90deg, #666666, #999999);
  }
  
  h2 {
    color: #000000;
    background: #f5f5f5;
    border-left: 3pt solid #666666;
    box-shadow: none;
  }
  
  h2::before {
    color: #666666;
  }
  
  h2::after {
    content: '';
  }
  
  h3::before {
    color: #666666;
    animation: none;
  }
  
  h4 {
    color: #333333;
    background: none;
    -webkit-text-fill-color: initial;
  }
  
  strong, b {
    background: #f0f0f0;
    color: #000000;
  }
  
  ul li::before {
    content: '•';
    color: #666666;
  }
  
  ol li::before {
    background: linear-gradient(135deg, #666666, #999999);
    box-shadow: none;
  }
  
  blockquote {
    background: #f8f8f8;
    border-left: 3pt solid #666666;
    box-shadow: none;
  }
  
  blockquote::before {
    content: '';
  }
  
  blockquote::after {
    display: none;
  }
  
  code {
    background: #f0f0f0;
    color: #000000;
    border: 1pt solid #cccccc;
  }
  
  pre {
    background: #f8f8f8;
    border: 1pt solid #cccccc;
    box-shadow: none;
  }
  
  pre::before {
    background: linear-gradient(90deg, #666666, #999999, #666666);
  }
  
  table {
    box-shadow: none;
  }
  
  th {
    background: #e0e0e0;
    color: #000000;
  }
  
  tr:nth-child(even) {
    background: #f5f5f5;
  }
  
  tr:hover {
    background: transparent;
  }
  
  a {
    color: #000000;
    background: none;
    -webkit-text-fill-color: initial;
    border-bottom: 1pt solid #666666;
  }
  
  a:hover {
    transform: none;
  }
  
  hr {
    background: linear-gradient(90deg, transparent, #666666, #999999, #666666, transparent);
  }
  
  hr::before {
    content: '';
    background: #ffffff;
  }
  
  img {
    box-shadow: none;
    border: 2pt solid #cccccc;
    background: none;
  }
}

