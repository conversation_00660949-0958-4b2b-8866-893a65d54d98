/*
 * 创意系列 20 - 霓虹青调
 * Neon Cyan Creative Style
 * 
 * 特色：霓虹青色主题，未来科技感，适合科技创新、数字化项目
 * 设计理念：霓虹青的未来感与数字化创新的结合
 */

@import url('./common/base.css');
@import url('./common/variables.css');

:root {
  --primary-color: #006064;
  --secondary-color: #00838f;
  --accent-color: #00acc1;
  --neon-cyan: #26c6da;
  --text-primary: #004d40;
  --text-secondary: #006064;
  --background-color: #f0fdff;
  --surface-color: #e0f7fa;
  --border-color: #b2ebf2;
}

body {
  background-color: var(--background-color);
  color: var(--text-primary);
  font-weight: var(--font-weight-normal);
  letter-spacing: 0.01em;
  line-height: 1.68;
}

h1 {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  font-size: 3rem;
  margin-bottom: var(--spacing-2xl);
  text-align: center;
  position: relative;
  padding: var(--spacing-xl) 0;
}

h1::before {
  content: '⚡';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  font-size: 1.5rem;
  opacity: 0.7;
  color: var(--neon-cyan);
}

h2::after {
  content: '🔬';
  position: absolute;
  right: var(--spacing-lg);
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.2rem;
  opacity: 0.7;
}

ul li::before {
  content: '⚡';
  position: absolute;
  left: 0;
  font-size: 1rem;
  color: var(--neon-cyan);
}

blockquote::before {
  content: '🔮';
  position: absolute;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  font-size: 1.3rem;
  opacity: 0.6;
}

@media print {
  body { color: #000000; background: #ffffff; }
  h1, h2, h3 { color: #000000; }
  h1::before, h2::after, blockquote::before { content: ''; }
}

