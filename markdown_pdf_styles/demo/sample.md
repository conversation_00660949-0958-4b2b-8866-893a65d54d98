# Markdown 样式演示文档

这是一个用于测试Markdown PDF样式的演示文档，包含了所有常用的Markdown元素。

## 二级标题示例

这是一个二级标题下的段落内容。段落应该有合适的行高和字间距，确保良好的可读性。

### 三级标题示例

这里是三级标题的内容。我们需要确保标题层级清晰，字重和字号搭配合理。

#### 四级标题示例

四级标题通常用于更细分的内容组织。

##### 五级标题示例

五级标题的使用频率较低，但仍需要保持良好的视觉层次。

###### 六级标题示例

六级标题是最小的标题级别，通常用于最细节的分类。

## 文本格式示例

这是一个包含**粗体文本**和*斜体文本*的段落。我们还可以使用`行内代码`来突出显示特定的术语或代码片段。

这是另一个段落，用来测试段落之间的间距是否合适。段落间距应该既不会太紧密，也不会过于松散。

## 列表示例

### 无序列表

- 第一个列表项
- 第二个列表项
  - 嵌套列表项
  - 另一个嵌套项
- 第三个列表项

### 有序列表

1. 第一个编号项
2. 第二个编号项
   1. 嵌套编号项
   2. 另一个嵌套编号项
3. 第三个编号项

## 引用块示例

> 这是一个引用块的示例。引用块通常用于突出显示重要的观点、名言或者来自其他来源的内容。
> 
> 引用块可以包含多个段落，每个段落都应该保持一致的格式和样式。

## 代码示例

### 行内代码

在文本中使用 `console.log()` 这样的行内代码。

### 代码块

```javascript
function calculateTotal(items) {
  return items.reduce((sum, item) => {
    return sum + item.price * item.quantity;
  }, 0);
}

const items = [
  { name: '产品A', price: 100, quantity: 2 },
  { name: '产品B', price: 150, quantity: 1 }
];

console.log('总计:', calculateTotal(items));
```

```python
def analyze_data(data):
    """
    分析数据并返回统计结果
    """
    total = sum(data)
    average = total / len(data)
    
    return {
        'total': total,
        'average': average,
        'count': len(data)
    }

# 示例数据
sales_data = [1200, 1500, 980, 1800, 1350]
result = analyze_data(sales_data)
print(f"分析结果: {result}")
```

## 表格示例

| 产品名称 | 价格 | 数量 | 总计 |
|---------|------|------|------|
| 产品A   | ¥100 | 2    | ¥200 |
| 产品B   | ¥150 | 1    | ¥150 |
| 产品C   | ¥80  | 3    | ¥240 |
| **合计** | -    | 6    | **¥590** |

## 链接示例

这里有一个[外部链接示例](https://www.example.com)，以及一个[内部链接](#二级标题示例)。

## 分隔线示例

上面的内容和下面的内容之间有一条分隔线。

---

这是分隔线下方的内容。

## 图片示例

![示例图片](https://via.placeholder.com/600x300/4A90E2/FFFFFF?text=示例图片)

*图片说明：这是一个示例图片，用于测试图片在文档中的显示效果。*

## 复杂内容示例

### 嵌套列表与代码

1. **数据收集阶段**
   - 确定数据源
   - 设计收集方案
   - 实施数据收集
   
2. **数据处理阶段**
   ```python
   # 数据清洗示例
   def clean_data(raw_data):
       cleaned = []
       for item in raw_data:
           if item is not None and item != '':
               cleaned.append(item.strip())
       return cleaned
   ```
   
3. **数据分析阶段**
   > 在这个阶段，我们需要运用统计学方法对清洗后的数据进行深入分析，
   > 以发现数据中隐藏的模式和趋势。

### 表格与引用结合

| 指标 | Q1 | Q2 | Q3 | Q4 |
|------|----|----|----|----|
| 收入 | 100万 | 120万 | 135万 | 150万 |
| 成本 | 60万 | 70万 | 75万 | 80万 |
| 利润 | 40万 | 50万 | 60万 | 70万 |

> **分析结论**：从上表可以看出，公司在全年保持了稳定的增长趋势，
> 利润率也在逐步提升，这表明公司的运营效率在不断改善。

## 总结

这个演示文档包含了Markdown的所有主要元素，可以用来测试和验证PDF样式的效果。每个样式都应该确保：

- **可读性**：文字清晰，对比度适当
- **层次感**：标题层级分明，信息组织有序
- **专业感**：整体风格统一，符合商务文档要求
- **打印一致性**：屏幕显示与打印效果保持一致

通过这个演示文档，我们可以全面评估每套样式的效果，确保最终交付的样式文件都能满足高质量商务文档的需求。

