#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import matplotlib
matplotlib.use('Agg')

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import os
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 小清新亮色配色方案
COLORS = ['#FF9AA2', '#FFB7B2', '#FFDAC1', '#E2F0CB', '#B5EAD7', '#C7CEEA']
COLORS2 = ['#A8E6CF', '#DCEDC1', '#FFD3A5', '#FFA8A8', '#B5B5EA', '#F8BBD9']
COLORS3 = ['#87CEEB', '#98FB98', '#F0E68C', '#FFA07A', '#DDA0DD', '#F5DEB3']

def create_output_dir():
    output_dir = "output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/"
    os.makedirs(output_dir, exist_ok=True)
    return output_dir

def set_chart_style():
    """设置图表样式"""
    sns.set_style("whitegrid", {
        'axes.grid': True,
        'axes.edgecolor': '#E0E0E0',
        'axes.linewidth': 0.8,
        'grid.color': '#F0F0F0',
        'grid.linewidth': 0.5
    })
    plt.rcParams.update({
        'figure.facecolor': 'white',
        'axes.facecolor': 'white',
        'savefig.facecolor': 'white',
        'font.size': 10,
        'axes.titlesize': 14,
        'axes.labelsize': 12,
        'xtick.labelsize': 10,
        'ytick.labelsize': 10,
        'legend.fontsize': 10
    })

def generate_chart_1():
    """宋庄镇基本情况概览 - 复杂组合图"""
    fig = plt.figure(figsize=(16, 10))
    gs = fig.add_gridspec(3, 4, hspace=0.3, wspace=0.3)
    
    # 主要指标柱状图
    ax1 = fig.add_subplot(gs[0, :2])
    categories = ['总面积\n(平方公里)', '常住人口\n(万人)', '耕地面积\n(万亩)', '艺术家\n(千人)', '年游客量\n(万人次)']
    values = [40, 5, 1.5, 5, 120]
    colors = COLORS[:5]
    
    bars = ax1.bar(categories, values, color=colors, alpha=0.8, edgecolor='white', linewidth=2)
    ax1.set_title('宋庄镇基本情况概览', fontsize=16, fontweight='bold', pad=20)
    ax1.set_ylabel('数值')
    
    for bar, value in zip(bars, values):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + height*0.02,
                f'{value}', ha='center', va='bottom', fontweight='bold', fontsize=11)
    
    # 交通便利性雷达图
    ax2 = fig.add_subplot(gs[0, 2:], projection='polar')
    transport_aspects = ['高速公路', '公共交通', '地铁规划', '共享出行', '停车便利', '可达性']
    transport_scores = [4.5, 3.8, 3.2, 4.0, 3.5, 4.2]
    
    angles = np.linspace(0, 2*np.pi, len(transport_aspects), endpoint=False).tolist()
    transport_scores += transport_scores[:1]
    angles += angles[:1]
    
    ax2.plot(angles, transport_scores, 'o-', linewidth=3, color=COLORS[0], markersize=8)
    ax2.fill(angles, transport_scores, alpha=0.25, color=COLORS[0])
    ax2.set_xticks(angles[:-1])
    ax2.set_xticklabels(transport_aspects, fontsize=10)
    ax2.set_ylim(0, 5)
    ax2.set_title('交通便利性评估', fontsize=14, pad=30)
    ax2.grid(True)
    
    # 产业结构饼图
    ax3 = fig.add_subplot(gs[1, :2])
    industries = ['农业生产', '艺术创意', '旅游服务', '商业服务', '其他产业']
    industry_values = [30, 35, 20, 10, 5]
    
    wedges, texts, autotexts = ax3.pie(industry_values, labels=industries, 
                                      colors=COLORS2[:5], autopct='%1.1f%%',
                                      startangle=90, textprops={'fontsize': 10})
    ax3.set_title('产业结构分布', fontsize=14, pad=20)
    
    # 发展趋势线图
    ax4 = fig.add_subplot(gs[1, 2:])
    years = list(range(2018, 2024))
    visitors = [80, 85, 92, 75, 88, 120]  # 万人次
    revenue = [1200, 1350, 1580, 1200, 1450, 1800]  # 万元
    
    ax4_twin = ax4.twinx()
    
    line1 = ax4.plot(years, visitors, marker='o', linewidth=3, color=COLORS[1], 
                     markersize=8, label='年游客量')
    ax4.fill_between(years, visitors, alpha=0.3, color=COLORS[1])
    
    line2 = ax4_twin.plot(years, revenue, marker='s', linewidth=3, color=COLORS[3], 
                          markersize=8, label='旅游收入')
    
    ax4.set_xlabel('年份')
    ax4.set_ylabel('游客量(万人次)', color=COLORS[1])
    ax4_twin.set_ylabel('收入(万元)', color=COLORS[3])
    ax4.set_title('旅游发展趋势', fontsize=14, pad=20)
    ax4.grid(True, alpha=0.3)
    
    # 合并图例
    lines1, labels1 = ax4.get_legend_handles_labels()
    lines2, labels2 = ax4_twin.get_legend_handles_labels()
    ax4.legend(lines1 + lines2, labels1 + labels2, loc='upper left')
    
    # 资源禀赋对比
    ax5 = fig.add_subplot(gs[2, :])
    resources = ['自然资源', '文化资源', '人力资源', '基础设施', '政策支持', '区位优势']
    songzhuang_scores = [3.8, 4.5, 3.5, 3.2, 4.2, 4.0]
    beijing_avg = [4.0, 3.8, 4.2, 4.5, 4.0, 3.5]
    
    x = np.arange(len(resources))
    width = 0.35
    
    bars1 = ax5.bar(x - width/2, songzhuang_scores, width, label='宋庄镇', 
                    color=COLORS[0], alpha=0.8, edgecolor='white', linewidth=1)
    bars2 = ax5.bar(x + width/2, beijing_avg, width, label='北京市平均', 
                    color=COLORS2[0], alpha=0.8, edgecolor='white', linewidth=1)
    
    ax5.set_xlabel('资源类型')
    ax5.set_ylabel('评分(满分5分)')
    ax5.set_title('资源禀赋对比分析', fontsize=14, pad=20)
    ax5.set_xticks(x)
    ax5.set_xticklabels(resources)
    ax5.legend()
    ax5.grid(True, alpha=0.3, axis='y')
    ax5.set_ylim(0, 5)
    
    # 添加数值标签
    for bars in [bars1, bars2]:
        for bar in bars:
            height = bar.get_height()
            ax5.text(bar.get_x() + bar.get_width()/2., height + 0.05,
                    f'{height}', ha='center', va='bottom', fontweight='bold', fontsize=9)
    
    plt.suptitle('宋庄镇农旅数字化项目基础条件综合分析', fontsize=18, fontweight='bold', y=0.98)
    plt.tight_layout()
    return fig

def generate_chart_2():
    """市场需求与竞争分析 - 复杂组合图"""
    fig = plt.figure(figsize=(16, 10))
    gs = fig.add_gridspec(2, 3, hspace=0.3, wspace=0.3)
    
    # 需求结构分析
    ax1 = fig.add_subplot(gs[0, 0])
    needs = ['亲子农事\n体验', '文化创意\n活动', '健康饮食\n体验', '自然生态\n体验', '教育研学\n活动', '休闲娱乐']
    percentages = [28, 25, 20, 15, 8, 4]
    colors = COLORS[:6]
    
    wedges, texts, autotexts = ax1.pie(percentages, labels=needs, colors=colors,
                                      autopct='%1.1f%%', startangle=90,
                                      textprops={'fontsize': 9})
    ax1.set_title('城区家庭周末休闲需求结构', fontsize=12, pad=20)
    
    # 消费能力分析
    ax2 = fig.add_subplot(gs[0, 1])
    income_groups = ['3000以下', '3000-5000', '5000-8000', '8000-12000', '12000以上']
    spending = [150, 280, 450, 680, 950]  # 月均休闲支出
    
    bars = ax2.bar(income_groups, spending, color=COLORS2[:5], alpha=0.8,
                   edgecolor='white', linewidth=2)
    ax2.set_title('不同收入群体月均休闲支出', fontsize=12, pad=20)
    ax2.set_xlabel('家庭月收入(元)')
    ax2.set_ylabel('月均支出(元)')
    ax2.tick_params(axis='x', rotation=45)
    
    for bar, value in zip(bars, spending):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 10,
                f'{value}', ha='center', va='bottom', fontweight='bold')
    
    # 市场规模预测
    ax3 = fig.add_subplot(gs[0, 2])
    years = list(range(2024, 2030))
    market_size = [2500, 3200, 4100, 5200, 6500, 8000]  # 万元
    growth_rate = [0, 28, 28.1, 26.8, 25.0, 23.1]  # %
    
    ax3_twin = ax3.twinx()
    
    bars = ax3.bar(years, market_size, color=COLORS[2], alpha=0.7, width=0.6)
    line = ax3_twin.plot(years[1:], growth_rate[1:], marker='o', linewidth=3, 
                         color='red', markersize=8, label='增长率')
    
    ax3.set_xlabel('年份')
    ax3.set_ylabel('市场规模(万元)', color=COLORS[2])
    ax3_twin.set_ylabel('增长率(%)', color='red')
    ax3.set_title('农旅市场规模预测', fontsize=12, pad=20)
    
    for bar, value in zip(bars, market_size):
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height + 100,
                f'{value}', ha='center', va='bottom', fontweight='bold', fontsize=9)
    
    # 竞争对手分析
    ax4 = fig.add_subplot(gs[1, :])
    competitors = ['密云古北水镇', '延庆世园会', '房山十渡', '昌平草莓园', '大兴梨花村', '宋庄项目(预期)']
    visitor_volume = [200, 180, 120, 80, 60, 150]  # 万人次/年
    avg_spending = [280, 320, 150, 120, 100, 200]  # 元/人次
    satisfaction = [4.2, 4.0, 3.5, 3.8, 3.6, 4.5]  # 满意度评分
    
    # 创建气泡图
    bubble_sizes = [v * 5 for v in visitor_volume]  # 调整气泡大小
    colors_bubble = COLORS3[:len(competitors)]
    
    scatter = ax4.scatter(avg_spending, satisfaction, s=bubble_sizes, c=colors_bubble, 
                         alpha=0.7, edgecolors='white', linewidth=2)
    
    # 添加标签
    for i, comp in enumerate(competitors):
        ax4.annotate(f'{comp}\n({visitor_volume[i]}万人次)', 
                    (avg_spending[i], satisfaction[i]),
                    xytext=(5, 5), textcoords='offset points',
                    fontsize=9, ha='left')
    
    ax4.set_xlabel('人均消费(元/人次)')
    ax4.set_ylabel('游客满意度(满分5分)')
    ax4.set_title('北京周边农旅项目竞争分析(气泡大小表示年游客量)', fontsize=14, pad=20)
    ax4.grid(True, alpha=0.3)
    ax4.set_xlim(80, 350)
    ax4.set_ylim(3.0, 5.0)
    
    # 添加象限分析线
    ax4.axhline(y=4.0, color='red', linestyle='--', alpha=0.5)
    ax4.axvline(x=200, color='red', linestyle='--', alpha=0.5)
    ax4.text(300, 4.7, '高消费\n高满意度', ha='center', va='center', 
            bbox=dict(boxstyle="round,pad=0.3", facecolor='lightgreen', alpha=0.7))
    ax4.text(120, 3.3, '低消费\n低满意度', ha='center', va='center', 
            bbox=dict(boxstyle="round,pad=0.3", facecolor='lightcoral', alpha=0.7))
    
    plt.suptitle('农旅数字化项目市场需求与竞争环境分析', fontsize=18, fontweight='bold', y=0.98)
    plt.tight_layout()
    return fig

def generate_chart_3():
    """数字农业发展趋势与技术应用 - 复杂组合图"""
    fig = plt.figure(figsize=(16, 10))
    gs = fig.add_gridspec(2, 3, hspace=0.3, wspace=0.3)

    # 数字农业市场规模趋势
    ax1 = fig.add_subplot(gs[0, :2])
    years = list(range(2018, 2026))
    china_market = [1200, 1450, 1750, 2100, 2500, 3000, 3600, 4300]  # 亿元
    beijing_market = [45, 58, 72, 89, 108, 132, 158, 190]  # 亿元
    growth_rate = [0, 20.8, 20.7, 20.0, 19.0, 20.0, 20.0, 19.4]  # %

    ax1_twin = ax1.twinx()

    # 面积图显示市场规模
    ax1.fill_between(years, china_market, alpha=0.3, color=COLORS[0], label='全国市场规模')
    ax1.plot(years, china_market, marker='o', linewidth=3, color=COLORS[0], markersize=8)

    ax1.fill_between(years, beijing_market, alpha=0.5, color=COLORS[1], label='北京市场规模')
    ax1.plot(years, beijing_market, marker='s', linewidth=3, color=COLORS[1], markersize=8)

    # 增长率柱状图
    bars = ax1_twin.bar(years[1:], growth_rate[1:], alpha=0.6, color=COLORS[3],
                        width=0.4, label='增长率')

    ax1.set_xlabel('年份')
    ax1.set_ylabel('市场规模(亿元)')
    ax1_twin.set_ylabel('增长率(%)')
    ax1.set_title('数字农业市场规模发展趋势', fontsize=14, pad=20)
    ax1.legend(loc='upper left')
    ax1_twin.legend(loc='upper right')
    ax1.grid(True, alpha=0.3)

    # 技术应用成熟度雷达图
    ax2 = fig.add_subplot(gs[0, 2], projection='polar')
    technologies = ['物联网', '大数据', '人工智能', 'VR/AR', '区块链', '云计算']
    maturity_scores = [4.2, 3.8, 3.5, 3.2, 2.8, 4.5]

    angles = np.linspace(0, 2*np.pi, len(technologies), endpoint=False).tolist()
    maturity_scores += maturity_scores[:1]
    angles += angles[:1]

    ax2.plot(angles, maturity_scores, 'o-', linewidth=3, color=COLORS[2], markersize=8)
    ax2.fill(angles, maturity_scores, alpha=0.25, color=COLORS[2])
    ax2.set_xticks(angles[:-1])
    ax2.set_xticklabels(technologies, fontsize=10)
    ax2.set_ylim(0, 5)
    ax2.set_title('技术成熟度评估', fontsize=12, pad=30)
    ax2.grid(True)

    # 投资回报分析
    ax3 = fig.add_subplot(gs[1, 0])
    investment_years = list(range(2025, 2030))
    investment = [500, 300, 200, 150, 100]  # 万元
    revenue = [200, 450, 680, 845, 920]  # 万元
    profit = [r - i for r, i in zip(revenue, investment)]

    x = np.arange(len(investment_years))
    width = 0.35

    bars1 = ax3.bar(x - width/2, investment, width, label='投资', color=COLORS[4], alpha=0.8)
    bars2 = ax3.bar(x + width/2, revenue, width, label='收入', color=COLORS[5], alpha=0.8)

    # 利润线
    ax3_twin = ax3.twinx()
    line = ax3_twin.plot(x, profit, marker='o', linewidth=3, color='red',
                         markersize=8, label='净利润')

    ax3.set_xlabel('年份')
    ax3.set_ylabel('金额(万元)')
    ax3_twin.set_ylabel('净利润(万元)', color='red')
    ax3.set_title('项目投资回报分析', fontsize=12, pad=20)
    ax3.set_xticks(x)
    ax3.set_xticklabels(investment_years)
    ax3.legend(loc='upper left')
    ax3_twin.legend(loc='upper right')
    ax3.grid(True, alpha=0.3)

    # 政策支持力度分析
    ax4 = fig.add_subplot(gs[1, 1])
    policies = ['数字乡村\n战略', '乡村振兴\n战略', '文旅融合\n政策', '数字经济\n政策', '农业现代化\n政策']
    support_level = [4.8, 4.6, 4.5, 4.7, 4.2]
    funding = [2000, 1800, 1500, 2200, 1600]  # 万元

    # 柱状图显示支持力度
    bars = ax4.bar(policies, support_level, color=COLORS2[:5], alpha=0.8)

    # 在柱子上显示资金支持
    for bar, fund in zip(bars, funding):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                f'{fund}万元', ha='center', va='bottom', fontweight='bold', fontsize=9)

    ax4.set_ylabel('支持力度评分(满分5分)')
    ax4.set_title('政策支持力度与资金投入', fontsize=12, pad=20)
    ax4.set_ylim(0, 5.5)
    ax4.grid(True, alpha=0.3, axis='y')

    # 风险评估矩阵
    ax5 = fig.add_subplot(gs[1, 2])
    risks = ['技术风险', '市场风险', '政策风险', '资金风险', '运营风险', '环境风险']
    probability = [0.3, 0.4, 0.2, 0.35, 0.45, 0.15]
    impact = [0.7, 0.8, 0.9, 0.6, 0.5, 0.4]

    # 根据风险等级设置颜色
    risk_colors = []
    for p, i in zip(probability, impact):
        risk_level = p * i
        if risk_level > 0.25:
            risk_colors.append('red')
        elif risk_level > 0.15:
            risk_colors.append('orange')
        else:
            risk_colors.append('green')

    scatter = ax5.scatter(probability, impact, s=200, c=risk_colors,
                         alpha=0.7, edgecolors='white', linewidth=2)

    for i, risk in enumerate(risks):
        ax5.annotate(risk, (probability[i], impact[i]), xytext=(5, 5),
                    textcoords='offset points', fontsize=9)

    ax5.set_xlabel('发生概率')
    ax5.set_ylabel('影响程度')
    ax5.set_title('项目风险评估矩阵', fontsize=12, pad=20)
    ax5.grid(True, alpha=0.3)
    ax5.set_xlim(0, 0.5)
    ax5.set_ylim(0, 1)

    # 添加风险区域标识
    ax5.axhline(y=0.6, color='red', linestyle='--', alpha=0.5)
    ax5.axvline(x=0.3, color='red', linestyle='--', alpha=0.5)

    plt.suptitle('数字农业发展趋势与项目可行性分析', fontsize=18, fontweight='bold', y=0.98)
    plt.tight_layout()
    return fig

def generate_professional_charts():
    """生成专业的中文组合图表"""
    set_chart_style()
    output_dir = create_output_dir()
    chart_count = 0

    # 清空现有图表
    for file in os.listdir(output_dir):
        if file.endswith('.png'):
            os.remove(os.path.join(output_dir, file))
    print("已清空现有图表，开始生成新的中文专业图表...")

    # 生成图表1
    try:
        fig1 = generate_chart_1()
        fig1.savefig(os.path.join(output_dir, 'chart_1_基本情况_1.png'),
                    dpi=300, bbox_inches='tight', facecolor='white')
        plt.close(fig1)
        chart_count += 1
        print("✓ 图表1: 宋庄镇基本情况综合分析")
    except Exception as e:
        print(f"❌ 图表1生成失败: {e}")

    # 生成图表2
    try:
        fig2 = generate_chart_2()
        fig2.savefig(os.path.join(output_dir, 'chart_1_需求分布_2.png'),
                    dpi=300, bbox_inches='tight', facecolor='white')
        plt.close(fig2)
        chart_count += 1
        print("✓ 图表2: 市场需求与竞争分析")
    except Exception as e:
        print(f"❌ 图表2生成失败: {e}")

    # 生成图表3
    try:
        fig3 = generate_chart_3()
        fig3.savefig(os.path.join(output_dir, 'chart_1_市场趋势_3.png'),
                    dpi=300, bbox_inches='tight', facecolor='white')
        plt.close(fig3)
        chart_count += 1
        print("✓ 图表3: 数字农业发展趋势与技术应用")
    except Exception as e:
        print(f"❌ 图表3生成失败: {e}")

    return chart_count

if __name__ == "__main__":
    try:
        count = generate_professional_charts()
        print(f"\n✅ 成功生成 {count} 个专业中文图表!")
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()
