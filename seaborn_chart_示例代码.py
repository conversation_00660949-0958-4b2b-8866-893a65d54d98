#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import os
from matplotlib import font_manager

# 查找阿里巴巴字体
font_list = font_manager.findSystemFonts(fontpaths=None, fontext='ttf')
alibaba_fonts = [f for f in font_list if 'Alibaba' in f]
print("找到的阿里巴巴字体:")
for font in alibaba_fonts:
    print(f"  - {os.path.basename(font)}")

# 创建字体对象
alibaba_font = None
if alibaba_fonts:
    try:
        # 尝试使用第一个找到的阿里巴巴字体
        alibaba_font = font_manager.FontProperties(fname=alibaba_fonts[0])
        print(f"\n成功创建字体对象，使用字体: {alibaba_fonts[0]}")
    except Exception as e:
        print(f"创建字体对象失败: {e}")

# 手动添加阿里巴巴字体
for font_path in alibaba_fonts:
    try:
        font_manager.fontManager.addfont(font_path)
    except Exception as e:
        print(f"添加字体失败: {font_path}, 错误: {e}")

# 刷新字体缓存
try:
    # 尝试使用新版本的方法
    font_manager.fontManager.rebuild()
except AttributeError:
    try:
        # 尝试使用旧版本的方法
        font_manager._rebuild()
    except AttributeError:
        print("无法刷新字体缓存，继续执行")

# 设置字体
plt.rcParams['font.sans-serif'] = ['AlibabaPuHuiTi-3-55-Regular', 'AlibabaPuHuiTi-3-65-Medium', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

# 如果没有成功创建字体对象，尝试使用名称创建
if not alibaba_font:
    try:
        alibaba_font = font_manager.FontProperties(family='AlibabaPuHuiTi-3-55-Regular')
        print("使用字体名称创建字体对象成功")
    except Exception as e:
        print(f"使用字体名称创建字体对象失败: {e}")
        # 尝试使用系统默认中文字体
        try:
            alibaba_font = font_manager.FontProperties(family='SimHei')
            print("使用SimHei字体作为备选")
        except:
            print("无法创建任何中文字体对象，图表中文可能无法正确显示")

# 设置全局字体样式
plt.rcParams['font.family'] = 'sans-serif'

# 打印当前可用的字体，用于调试
print("\n当前可用的字体:")
available_fonts = sorted([f.name for f in font_manager.fontManager.ttflist])
for i, font in enumerate(available_fonts):
    if 'Alibaba' in font:
        print(f"  - {font}")

# 设置特定的字体对象用于标题和标签
try:
    alibaba_font = font_manager.FontProperties(fname=alibaba_fonts[0])
    print(f"\n使用字体文件: {os.path.basename(alibaba_fonts[0])}")
except:
    alibaba_font = None
    print("\n无法加载阿里巴巴字体文件，将使用默认字体")

# 设置matplotlib的字体缓存目录权限
os.environ['MPLCONFIGDIR'] = os.path.expanduser('~/.matplotlib')
os.makedirs(os.environ['MPLCONFIGDIR'], exist_ok=True)

# 创建输出目录
output_dir = "output/charts"
os.makedirs(output_dir, exist_ok=True)

# 设置小清新配色方案
palette = sns.color_palette(["#FF9AA2", "#FFB7B2", "#FFDAC1", "#E2F0CB", "#B5EAD7", "#C7CEEA"])

# 创建示例数据
np.random.seed(42)

# 确保中文标签正确显示
months = ['1月', '2月', '3月', '4月', '5月', '6月']
# 使用Unicode编码确保中文字符正确处理
months_unicode = [m.encode('utf-8').decode('utf-8') for m in months]

data = {
    '月份': months_unicode,
    '销售额': np.random.randint(50, 100, 6),
    '利润': np.random.randint(20, 50, 6),
    '客户数': np.random.randint(200, 500, 6),
}
df = pd.DataFrame(data)

# 打印数据框，确认中文正确
print("\n数据框内容:")
print(df.head())

# 设置Seaborn风格
sns.set_style("whitegrid")
plt.figure(figsize=(12, 8))

# 创建图表
plt.subplot(2, 2, 1)
sns.barplot(x='月份', y='销售额', data=df, palette=palette)
if alibaba_font:
    plt.title('月度销售额', fontsize=14, fontproperties=alibaba_font)
    plt.ylabel('销售额 (万元)', fontproperties=alibaba_font)
    plt.xlabel('月份', fontproperties=alibaba_font)
else:
    plt.title('月度销售额', fontsize=14)
    plt.ylabel('销售额 (万元)')
    plt.xlabel('月份')

plt.subplot(2, 2, 2)
sns.lineplot(x='月份', y='利润', data=df, marker='o', color=palette[3])
if alibaba_font:
    plt.title('月度利润', fontsize=14, fontproperties=alibaba_font)
    plt.ylabel('利润 (万元)', fontproperties=alibaba_font)
    plt.xlabel('月份', fontproperties=alibaba_font)
else:
    plt.title('月度利润', fontsize=14)
    plt.ylabel('利润 (万元)')
    plt.xlabel('月份')

plt.subplot(2, 2, 3)
sns.barplot(x='月份', y='客户数', data=df, palette=palette)
if alibaba_font:
    plt.title('月度客户数', fontsize=14, fontproperties=alibaba_font)
    plt.ylabel('客户数', fontproperties=alibaba_font)
    plt.xlabel('月份', fontproperties=alibaba_font)
else:
    plt.title('月度客户数', fontsize=14)
    plt.ylabel('客户数')
    plt.xlabel('月份')

plt.subplot(2, 2, 4)
# 计算销售额与利润的比率
df['利润率'] = df['利润'] / df['销售额'] * 100
sns.lineplot(x='月份', y='利润率', data=df, marker='o', color=palette[5])
if alibaba_font:
    plt.title('月度利润率', fontsize=14, fontproperties=alibaba_font)
    plt.ylabel('利润率 (%)', fontproperties=alibaba_font)
    plt.xlabel('月份', fontproperties=alibaba_font)
else:
    plt.title('月度利润率', fontsize=14)
    plt.ylabel('利润率 (%)')
    plt.xlabel('月份')

# 设置整体标题
if alibaba_font:
    plt.suptitle('2023年上半年业绩概览', fontsize=18, y=0.98, fontproperties=alibaba_font)
else:
    plt.suptitle('2023年上半年业绩概览', fontsize=18, y=0.98)
plt.tight_layout(rect=[0, 0, 1, 0.96])

# 定义一个函数来处理中文字体问题
def set_tick_font(ax):
    """设置刻度标签的字体"""
    if alibaba_font:
        for label in ax.get_xticklabels() + ax.get_yticklabels():
            label.set_fontproperties(alibaba_font)

# 应用字体设置到所有子图
for i in range(1, 5):
    ax = plt.subplot(2, 2, i)
    set_tick_font(ax)

# 保存图表前确保所有中文都正确显示
plt.rcParams['savefig.dpi'] = 300
plt.rcParams['savefig.bbox'] = 'tight'
plt.rcParams['savefig.pad_inches'] = 0.1

# 保存图表
output_path = os.path.join(output_dir, 'business_performance.png')
plt.savefig(output_path, dpi=300, bbox_inches='tight', 
           facecolor='white', edgecolor='none')
print(f"\n图表已保存至: {output_path}")

# 显示图表
# plt.show()