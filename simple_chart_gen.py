#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import os
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 小清新亮色配色方案
COLORS = ['#FF9AA2', '#FFB7B2', '#FFDAC1', '#E2F0CB', '#B5EAD7', '#C7CEEA']

def create_charts():
    """创建图表"""
    # 创建输出目录
    output_dir = "output/北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/"
    os.makedirs(output_dir, exist_ok=True)
    
    # 设置样式
    sns.set_style("whitegrid")
    plt.rcParams.update({
        'figure.facecolor': 'white',
        'axes.facecolor': 'white',
        'savefig.facecolor': 'white'
    })
    
    print("开始生成图表...")
    
    # 图表1: 基本情况
    fig, ax = plt.subplots(figsize=(10, 6))
    categories = ['总面积(km²)', '人口(万)', '耕地(万亩)', '艺术家(千人)']
    values = [40, 5, 1.5, 5]
    bars = ax.bar(categories, values, color=COLORS[:4], alpha=0.8)
    ax.set_title('宋庄镇基本情况概览', fontsize=14, pad=20)
    ax.set_ylabel('数值')
    
    for bar, value in zip(bars, values):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                f'{value}', ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    fig.savefig(os.path.join(output_dir, 'chart_1_基本情况_1.png'), 
                dpi=300, bbox_inches='tight', facecolor='white')
    plt.close(fig)
    print("✓ 生成图表1: 基本情况概览")
    
    # 图表2: 需求分布饼图
    fig, ax = plt.subplots(figsize=(10, 8))
    needs = ['亲子农事体验', '文化创意活动', '健康饮食体验', '自然生态体验', '教育研学', '其他']
    percentages = [28, 25, 20, 15, 8, 4]
    
    wedges, texts, autotexts = ax.pie(percentages, labels=needs, colors=COLORS,
                                     autopct='%1.1f%%', startangle=90)
    ax.set_title('北京城区家庭周末休闲需求分布', fontsize=14, pad=20)
    
    plt.tight_layout()
    fig.savefig(os.path.join(output_dir, 'chart_1_需求分布_2.png'), 
                dpi=300, bbox_inches='tight', facecolor='white')
    plt.close(fig)
    print("✓ 生成图表2: 需求分布")
    
    # 图表3: 市场趋势
    fig, ax = plt.subplots(figsize=(10, 6))
    years = list(range(2018, 2026))
    market_size = [1200, 1450, 1750, 2100, 2500, 3000, 3600, 4300]
    
    ax.plot(years, market_size, marker='o', linewidth=3, color=COLORS[0], markersize=8)
    ax.fill_between(years, market_size, alpha=0.3, color=COLORS[0])
    ax.set_title('数字农业市场规模增长趋势', fontsize=14, pad=20)
    ax.set_xlabel('年份')
    ax.set_ylabel('市场规模(亿元)')
    ax.grid(True, alpha=0.3)
    
    for year, size in zip(years, market_size):
        ax.annotate(f'{size}', (year, size), textcoords="offset points",
                   xytext=(0,10), ha='center', fontsize=9)
    
    plt.tight_layout()
    fig.savefig(os.path.join(output_dir, 'chart_1_市场趋势_3.png'), 
                dpi=300, bbox_inches='tight', facecolor='white')
    plt.close(fig)
    print("✓ 生成图表3: 市场趋势")
    
    # 图表4: 投资回报分析
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 8))
    
    # 投资构成
    investment_types = ['建设投资', '运营投资', '维护投资']
    investment_amounts = [1800, 580, 200]
    ax1.pie(investment_amounts, labels=investment_types, colors=COLORS[:3], 
            autopct='%1.1f%%', startangle=90)
    ax1.set_title('总投资构成(万元)')
    
    # 年度收益
    years = list(range(2025, 2030))
    revenues = [200, 450, 680, 845, 920]
    costs = [150, 280, 380, 475, 520]
    
    x = np.arange(len(years))
    width = 0.35
    ax2.bar(x - width/2, revenues, width, label='收益', color=COLORS[1], alpha=0.8)
    ax2.bar(x + width/2, costs, width, label='成本', color=COLORS[2], alpha=0.8)
    ax2.set_xlabel('年份')
    ax2.set_ylabel('金额(万元)')
    ax2.set_title('年度收益预测')
    ax2.set_xticks(x)
    ax2.set_xticklabels(years)
    ax2.legend()
    
    # 累计利润
    profits = [r - c for r, c in zip(revenues, costs)]
    cumulative_profit = np.cumsum(profits)
    ax3.plot(years, cumulative_profit, marker='s', linewidth=3, color=COLORS[3])
    ax3.axhline(y=sum(investment_amounts), color='red', linestyle='--', 
                label=f'初始投资({sum(investment_amounts)}万元)')
    ax3.set_title('投资回收期分析')
    ax3.set_xlabel('年份')
    ax3.set_ylabel('累计利润(万元)')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 财务指标
    indicators = ['NPV\n(万元)', 'IRR\n(%)', '回收期\n(年)', 'ROI\n(%)']
    values = [1250, 28.5, 3.2, 25.0]
    bars = ax4.bar(indicators, values, color=COLORS[:4], alpha=0.8)
    ax4.set_title('关键财务指标')
    
    for bar, value in zip(bars, values):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                f'{value}', ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    fig.savefig(os.path.join(output_dir, 'chart_1_投资回报_5.png'), 
                dpi=300, bbox_inches='tight', facecolor='white')
    plt.close(fig)
    print("✓ 生成图表4: 投资回报分析")
    
    # 图表5: 政策支持雷达图
    fig, ax = plt.subplots(figsize=(10, 8), subplot_kw=dict(projection='polar'))
    
    policies = ['数字乡村', '乡村振兴', '文旅融合', '数字经济', '农业现代化', '创新驱动']
    scores = [4.8, 4.6, 4.5, 4.7, 4.2, 4.3]
    
    angles = np.linspace(0, 2*np.pi, len(policies), endpoint=False).tolist()
    scores += scores[:1]
    angles += angles[:1]
    
    ax.plot(angles, scores, 'o-', linewidth=3, color=COLORS[0], markersize=8)
    ax.fill(angles, scores, alpha=0.25, color=COLORS[0])
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(policies)
    ax.set_ylim(0, 5)
    ax.set_title('国家政策支持力度评估\n(满分5分)', fontsize=14, pad=30)
    ax.grid(True)
    
    plt.tight_layout()
    fig.savefig(os.path.join(output_dir, 'chart_2_政策支持_6.png'), 
                dpi=300, bbox_inches='tight', facecolor='white')
    plt.close(fig)
    print("✓ 生成图表5: 政策支持雷达图")
    
    print(f"\n✅ 成功生成5个图表，保存在: {output_dir}")
    return 5

if __name__ == "__main__":
    count = create_charts()
    print(f"总共生成了 {count} 个图表")
