# 📊 政府报告视觉增强指令（专业完善版）

## 🎯 核心原则

### 1. 内容为王原则
- 视觉元素必须服务于内容表达，不得喧宾夺主
- 每个视觉元素都必须有明确的信息传达目的
- 优先选择最能提升理解效果的位置插入

### 2. 适度增强原则
- 严格控制视觉元素总量，避免过度设计
- 遵循"少而精"的设计理念
- 保持文档的专业性和可读性

### 3. 质量优先原则
- 宁缺毋滥，确保每个视觉元素都达到专业标准
- 统一的视觉风格和技术规范
- 完善的质量检查和优化机制

## 📏 数量控制标准

### 智能数量计算公式
```
基础图表数 = max(文档总行数 ÷ 150, 文档章节数 × 2)
最大图表数 = min(基础图表数 × 1.5, 文档总行数 ÷ 100)
最终图表数 = 在[基础图表数, 最大图表数]范围内，根据内容适配度确定

图片数量 = 图表数量 × 0.6（图片作为图表的补充）
```

### 密度控制标准
- **最小间隔**：相邻视觉元素间至少间隔80行
- **章节平衡**：每章节视觉元素数量相对均衡
- **内容密度**：数据密集章节可适当增加图表
- **阅读节奏**：避免连续出现多个视觉元素

## 🎨 视觉元素分类与适用场景

### A类：数据图表（优先级：高）
**适用场景**：
- 包含具体数值、百分比、统计数据的段落
- 对比分析、趋势说明、结构分析内容
- 预算、投资、收益等财务数据
- 市场调研、问卷调查结果

**图表类型选择**：
- 数值对比 → 柱状图、条形图
- 比例关系 → 饼图、环形图
- 时间趋势 → 折线图、面积图
- 多维数据 → 雷达图、散点图
- 流程关系 → 流程图、桑基图

### B类：概念图示（优先级：中）
**适用场景**：
- 抽象概念需要具象化表达
- 系统架构、组织结构说明
- 发展模式、运营模式介绍
- 技术路线、实施路径描述

### C类：实景图片（优先级：中低）
**适用场景**：
- 地理位置、环境条件描述
- 现状展示、案例说明
- 设施设备、技术应用展示
- 人文活动、社会效益体现

## 🔍 内容适配性评估标准

### 必要性评估（5分制）
- **5分**：包含具体数据，必须用图表表达
- **4分**：复杂关系，图表能显著提升理解
- **3分**：一般内容，图表有一定帮助
- **2分**：简单内容，图表作用有限
- **1分**：纯文字内容，不适合图表

### 插入阈值
- **必须插入**：评分≥4分
- **建议插入**：评分=3分，且符合数量控制
- **不建议插入**：评分≤2分

## 📋 执行流程（三阶段优化）

### 阶段一：智能分析与规划
1. **文档结构分析**
   - 统计文档总行数、章节数、段落数
   - 识别数据密集区域和概念密集区域
   - 计算合理的视觉元素数量上限

2. **内容适配性评估**
   - 逐段落评估视觉元素需求
   - 标记高价值插入位置
   - 生成优先级排序列表

3. **规划文件生成**
   - 创建详细的视觉元素规划
   - 确定精确的插入位置
   - 制定质量检查标准

### 阶段二：批量生成与优化
1. **图表代码生成**
   - 基于规划文件批量生成
   - 统一样式和配色方案
   - 确保中文字体正确显示

2. **质量检查与优化**
   - 视觉效果评估
   - 数据准确性检查
   - 样式一致性验证

### 阶段三：精确插入与验证
1. **文档复制与插入**
   - 复制原始文档
   - 按规划精确插入视觉元素
   - 保持原有格式和结构

2. **最终质量验证**
   - 检查插入位置准确性
   - 验证引用链接有效性
   - 确认整体视觉效果

## 📁 文件管理规范

### 目录结构
```
output/
└── [文档名称]/
    ├── charts/          # 图表文件
    ├── images/          # 图片文件
    ├── plans/           # 规划文件
    └── enhanced.md      # 增强后文档
```

### 命名规范
- **图表**：`chart_[章节号]_[类型]_[序号].png`
- **图片**：`image_[章节号]_[类型]_[序号].jpg`
- **规划**：`visual_plan.json`

## 🎯 质量标准与检查

### 技术质量标准
- **图表分辨率**：≥1200×800像素
- **文件大小**：图表50-200KB，图片100-500KB
- **色彩规范**：政府蓝(#1f77b4) + 专业配色
- **字体规范**：确保中文正确显示

### 内容质量标准
- **相关性**：与段落内容高度匹配
- **准确性**：数据和信息准确无误
- **专业性**：符合政府报告标准
- **美观性**：视觉效果专业美观

### 用户体验标准
- **可读性**：不影响正常阅读流程
- **导航性**：视觉元素有助于内容理解
- **一致性**：整体风格统一协调
- **实用性**：每个元素都有实际价值

## ⚠️ 风险控制与应急预案

### 常见风险识别
1. **过度设计风险**：视觉元素过多过密
2. **内容不匹配风险**：强行插入不相关元素
3. **技术实现风险**：生成失败或质量不达标
4. **用户体验风险**：影响文档可读性

### 应急处理机制
1. **数量超标**：按优先级删减，保留最重要的
2. **质量不达标**：重新生成或使用备选方案
3. **技术故障**：降级到基础图表类型
4. **时间不足**：优先完成高优先级项目

## 🔧 技术实现要点

### 中文字体处理
```python
# 严格按照seaborn_chart_示例代码.py的方法
plt.rcParams['font.sans-serif'] = ['AlibabaPuHuiTi-3-55-Regular', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False
```

### 图表样式统一
```python
# 专业配色方案
COLORS = {
    'primary': '#1f77b4',    # 政府蓝
    'secondary': '#2E8B57',  # 农业绿
    'accent': '#FF7F0E',     # 强调橙
    'neutral': '#7F7F7F'     # 中性灰
}

# 统一图表尺寸
plt.figure(figsize=(12, 8))
```

## 📊 规划文件格式

```json
{
  "document_info": {
    "total_lines": 3470,
    "total_chapters": 12,
    "calculated_chart_limit": 25,
    "calculated_image_limit": 15
  },
  "visual_elements": [
    {
      "id": "chart_01",
      "type": "chart",
      "title": "图表标题",
      "chart_type": "bar",
      "necessity_score": 5,
      "insert_after_line": 150,
      "chapter": "第一章",
      "content_summary": "相关内容摘要",
      "file_path": "charts/chart_01_bar_01.png",
      "markdown_ref": "![图表标题](./charts/chart_01_bar_01.png)"
    }
  ],
  "quality_checks": {
    "total_elements": 25,
    "density_check": "passed",
    "distribution_check": "passed",
    "content_match_check": "passed"
  }
}
```

## 🎯 执行检查清单

### 执行前检查
- [ ] 文档结构分析完成
- [ ] 数量控制计算正确
- [ ] 内容适配性评估完成
- [ ] 规划文件生成并验证

### 执行中检查
- [ ] 图表生成质量达标
- [ ] 中文字体显示正常
- [ ] 文件命名规范正确
- [ ] 插入位置精确无误

### 执行后检查
- [ ] 视觉元素总数符合控制标准
- [ ] 文档整体视觉效果协调
- [ ] 所有链接引用有效
- [ ] 用户阅读体验良好

## 🚀 具体执行指导

### 第一步：文档智能分析
```python
# 示例分析代码
def analyze_document(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()

    total_lines = len(lines)
    chapters = count_chapters(lines)
    data_paragraphs = identify_data_paragraphs(lines)

    # 智能计算图表数量
    base_charts = max(total_lines // 150, chapters * 2)
    max_charts = min(base_charts * 1.5, total_lines // 100)

    return {
        'total_lines': total_lines,
        'chapters': chapters,
        'recommended_charts': base_charts,
        'max_charts': max_charts,
        'data_paragraphs': data_paragraphs
    }
```

### 第二步：内容适配性评估
```python
def evaluate_content_suitability(paragraph):
    score = 0

    # 数据指标检测
    if has_numbers(paragraph): score += 2
    if has_percentages(paragraph): score += 2
    if has_comparisons(paragraph): score += 1
    if has_trends(paragraph): score += 1
    if has_structures(paragraph): score += 1

    # 内容复杂度检测
    if is_complex_concept(paragraph): score += 1
    if needs_visualization(paragraph): score += 1

    return min(score, 5)  # 最高5分
```

### 第三步：图表类型智能匹配
```python
def select_chart_type(content):
    if contains_comparison_data(content):
        return 'bar_chart'
    elif contains_proportion_data(content):
        return 'pie_chart'
    elif contains_trend_data(content):
        return 'line_chart'
    elif contains_relationship_data(content):
        return 'scatter_plot'
    elif contains_process_flow(content):
        return 'flow_chart'
    else:
        return 'bar_chart'  # 默认选择
```

## 🎨 专业图表模板库

### 模板1：政府标准柱状图
```python
def create_government_bar_chart(data, title):
    plt.figure(figsize=(12, 8))

    # 政府标准配色
    colors = ['#1f77b4', '#2E8B57', '#FF7F0E', '#D62728', '#9467BD']

    bars = plt.bar(data['categories'], data['values'],
                   color=colors[:len(data['categories'])],
                   alpha=0.8, edgecolor='white', linewidth=1.5)

    # 添加数值标签
    for bar in bars:
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{height}', ha='center', va='bottom', fontweight='bold')

    plt.title(title, fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('类别', fontsize=14, fontweight='bold')
    plt.ylabel('数值', fontsize=14, fontweight='bold')
    plt.grid(True, alpha=0.3)

    return plt
```

### 模板2：专业饼图
```python
def create_professional_pie_chart(data, title):
    plt.figure(figsize=(10, 8))

    colors = ['#1f77b4', '#2E8B57', '#FF7F0E', '#D62728', '#9467BD']

    wedges, texts, autotexts = plt.pie(data['values'],
                                       labels=data['labels'],
                                       colors=colors,
                                       autopct='%1.1f%%',
                                       startangle=90,
                                       explode=[0.05] * len(data['values']))

    plt.title(title, fontsize=16, fontweight='bold', pad=20)

    return plt
```

## 📝 最佳实践案例

### 案例1：数据段落识别与图表生成
**原文段落**：
"根据调研数据，宋庄镇农业用地面积为1500亩，其中水稻种植占40%，蔬菜种植占35%，果树种植占25%。"

**处理流程**：
1. 识别为数据段落（包含具体数值和百分比）
2. 适配性评分：5分（必须插入）
3. 选择图表类型：饼图（比例数据）
4. 生成专业饼图展示土地利用结构

### 案例2：概念段落的视觉化处理
**原文段落**：
"数字农旅融合发展模式包括智慧农业生产、数字化营销推广、在线服务平台、数据分析决策四个核心环节。"

**处理流程**：
1. 识别为概念段落（系统架构描述）
2. 适配性评分：4分（建议插入）
3. 选择图表类型：流程图
4. 生成系统架构流程图

## ⚡ 效率优化技巧

### 1. 批量处理策略
- 一次性分析所有需要图表的位置
- 批量生成所有图表代码
- 统一执行插入操作

### 2. 模板复用机制
- 建立标准图表模板库
- 根据数据类型自动选择模板
- 减少重复代码编写

### 3. 质量检查自动化
- 自动检查图表生成是否成功
- 验证中文字体显示效果
- 确认文件保存路径正确

## 🔧 故障排除指南

### 常见问题及解决方案

**问题1：中文字体显示为方块**
```python
# 解决方案：严格按照示例代码设置字体
import matplotlib.pyplot as plt
from matplotlib import font_manager

# 查找并设置中文字体
font_list = font_manager.findSystemFonts(fontpaths=None, fontext='ttf')
chinese_fonts = [f for f in font_list if 'SimHei' in f or 'Microsoft' in f]

if chinese_fonts:
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
else:
    print("警告：未找到中文字体，可能出现显示问题")

plt.rcParams['axes.unicode_minus'] = False
```

**问题2：图表数量过多**
```python
# 解决方案：严格执行数量控制
def control_chart_quantity(planned_charts, max_allowed):
    if len(planned_charts) > max_allowed:
        # 按优先级排序，保留最重要的
        sorted_charts = sorted(planned_charts,
                             key=lambda x: x['necessity_score'],
                             reverse=True)
        return sorted_charts[:max_allowed]
    return planned_charts
```

**问题3：插入位置不准确**
```python
# 解决方案：使用绝对行号定位
def insert_chart_reference(lines, insert_line, chart_ref):
    if 0 <= insert_line < len(lines):
        lines.insert(insert_line + 1, f"\n{chart_ref}\n\n")
    return lines
```

---

**使用说明**：本指令基于最佳实践设计，确保AI Agent能够生成专业、合理、美观的政府报告视觉增强效果。请严格按照各项标准执行，确保最终成果的专业性和实用性。

**重要提醒**：
1. 始终以内容质量为第一优先级
2. 严格控制视觉元素数量，避免过度设计
3. 确保每个视觉元素都有明确的信息传达价值
4. 保持文档的专业性和可读性
5. 遇到问题时优先选择保守方案
